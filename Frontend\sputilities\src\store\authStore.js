import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { authAPI, handleAPIError } from '../services/api';

const useAuthStore = create(
  persist(
    (set, get) => ({
      // State
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async () => {
        set({ isLoading: true, error: null });
        try {
          const response = await authAPI.login();
          const { redirectUrl } = response.data.data;
          
          // Redirect to Spotify OAuth
          window.location.href = redirectUrl;
        } catch (error) {
          const errorMessage = handleAPIError(error);
          set({ error: errorMessage, isLoading: false });
          throw error;
        }
      },

      checkAuth: async () => {
        set({ isLoading: true, error: null });
        try {
          const response = await authAPI.check();
          const { authenticated, user } = response.data.data;
          
          set({
            isAuthenticated: authenticated,
            user: authenticated ? user : null,
            isLoading: false,
          });
          
          return authenticated;
        } catch (error) {
          const errorMessage = handleAPIError(error);
          set({
            isAuthenticated: false,
            user: null,
            error: errorMessage,
            isLoading: false,
          });
          return false;
        }
      },

      logout: async () => {
        set({ isLoading: true, error: null });
        try {
          await authAPI.logout();
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          const errorMessage = handleAPIError(error);
          set({ error: errorMessage, isLoading: false });
          // Still clear auth state even if logout fails
          set({
            user: null,
            isAuthenticated: false,
          });
        }
      },

      clearError: () => set({ error: null }),

      // Initialize auth state
      initialize: async () => {
        const { checkAuth } = get();
        await checkAuth();
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

export default useAuthStore;
