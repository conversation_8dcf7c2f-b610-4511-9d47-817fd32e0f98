/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<76d98da4c53b448f8fd80e8b19ad7686>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Image/Image.js.flow
 */

import type { ImageType } from "./ImageTypes.flow";
export type { ImageProgressEventIOS, ImagePropsIOS, ImagePropsAndroid, ImageSourcePropType, ImageLoadEvent, ImageErrorEvent, ImagePropsBase, ImageProps, ImageBackgroundProps } from "./ImageProps";
export type { ImageResolvedAssetSource, ImageSize } from "./ImageTypes.flow";
declare const $$Image: ImageType;
declare type $$Image = typeof $$Image;
export default $$Image;
