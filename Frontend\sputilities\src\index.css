@font-face {
  font-family: "spotify-circular";
  src: url("fonts/CircularSpotifyText-Black.otf") format("opentype"),
    url("fonts/CircularSpotifyText-BlackItalic.otf") format("opentype"),
    url("fonts/CircularSpotifyText-Bold.otf") format("opentype"),
    url("fonts/CircularSpotifyText-Book.otf") format("opentype"),
    url("fonts/CircularSpotifyText-BookItalic.otf") format("opentype"),
    url("fonts/CircularSpotifyText-Light.otf") format("opentype"),
    url("fonts/CircularSpotifyText-Medium.otf") format("opentype"),
    url("fonts/CircularSpotifyText-MediumItalic.otf") format("opentype");
  font-weight: normal;
  font-style: normal;
}

/* Ensure the root container takes up the full height of the viewport */
html, body {
  height: 100%;
  margin: 0;
  display: flex;
  flex-direction: column;
}

html {
  background-color: #191414;
}

/* The root element containing the app */
#root {
  font-family: "spotify-circular";
  display: flex;
  flex-direction: column;
  flex-grow: 1; /* Ensures the main content grows to fill available space */
}

/* Main content area */
main {
  flex-grow: 1; /* Pushes the footer to the bottom when content is short */
}

/* Footer styling */
footer {
  background-color: #242323;
  color: #fff;
  padding: 20px;
  text-align: center;
  margin-top: auto; /* Ensures the footer stays at the bottom */
}

/* On Scroll effect */
