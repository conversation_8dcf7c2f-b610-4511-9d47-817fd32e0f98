@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: "spotify-circular";
  src: url("fonts/CircularSpotifyText-Black.otf") format("opentype"),
    url("fonts/CircularSpotifyText-BlackItalic.otf") format("opentype"),
    url("fonts/CircularSpotifyText-Bold.otf") format("opentype"),
    url("fonts/CircularSpotifyText-Book.otf") format("opentype"),
    url("fonts/CircularSpotifyText-BookItalic.otf") format("opentype"),
    url("fonts/CircularSpotifyText-Light.otf") format("opentype"),
    url("fonts/CircularSpotifyText-Medium.otf") format("opentype"),
    url("fonts/CircularSpotifyText-MediumItalic.otf") format("opentype");
  font-weight: normal;
  font-style: normal;
}

@layer base {
  html, body {
    @apply h-full m-0 bg-spotify-black font-spotify;
  }

  #root {
    @apply flex flex-col flex-grow h-full;
  }

  main {
    @apply flex-grow;
  }
}

@layer components {
  .btn-primary {
    @apply bg-spotify-green hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-transparent border border-spotify-green text-spotify-green hover:bg-spotify-green hover:text-white font-bold py-2 px-4 rounded transition-all duration-200;
  }

  .card {
    @apply bg-spotify-dark rounded-lg p-6 shadow-lg border border-gray-800;
  }

  .input-field {
    @apply bg-spotify-dark border border-gray-600 text-white rounded-lg px-4 py-2 focus:outline-none focus:border-spotify-green transition-colors duration-200;
  }

  .parallax-bg {
    @apply fixed top-0 left-0 w-full h-full -z-10 bg-gradient-to-b from-spotify-green/20 to-spotify-black;
    animation: parallax 20s linear infinite;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-spotify-green to-green-400 bg-clip-text text-transparent;
  }

  .glass-effect {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .hover-lift {
    @apply transition-transform duration-200 hover:-translate-y-1;
  }
}
