/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<240812cac3ce270f35082629b6e5d62b>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Utilities/NativeDeviceInfo.js
 */

export * from "../../src/private/specs_DEPRECATED/modules/NativeDeviceInfo";
import NativeDeviceInfo from "../../src/private/specs_DEPRECATED/modules/NativeDeviceInfo";
declare const $$NativeDeviceInfo: typeof NativeDeviceInfo;
declare type $$NativeDeviceInfo = typeof $$NativeDeviceInfo;
export default $$NativeDeviceInfo;
