{"hash": "541d7d25", "configHash": "ea2e88ec", "lockfileHash": "533d7845", "browserHash": "153e40a4", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "9b906c13", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "c8462497", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "30758a9f", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "68ba8a59", "needsInterop": true}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "347fe2e1", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "83734702", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "c470b0ad", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "d6815f13", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "17cdbdac", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "c0dec56f", "needsInterop": true}, "react-hot-toast": {"src": "../../react-hot-toast/dist/index.mjs", "file": "react-hot-toast.js", "fileHash": "828ebd86", "needsInterop": false}, "react-parallax": {"src": "../../react-parallax/lib/index.js", "file": "react-parallax.js", "fileHash": "9dc6e2a7", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "e040ffbe", "needsInterop": false}, "zustand": {"src": "../../zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "d15c165c", "needsInterop": false}, "zustand/middleware": {"src": "../../zustand/esm/middleware.mjs", "file": "zustand_middleware.js", "fileHash": "b83c9916", "needsInterop": false}, "@headlessui/react": {"src": "../../@headlessui/react/dist/headlessui.esm.js", "file": "@headlessui_react.js", "fileHash": "b6eb7e16", "needsInterop": false}}, "chunks": {"chunk-BJ47VTWB": {"file": "chunk-BJ47VTWB.js"}, "chunk-HZXZPQUA": {"file": "chunk-HZXZPQUA.js"}, "chunk-P5CFCGMN": {"file": "chunk-P5CFCGMN.js"}, "chunk-HXA6O6EE": {"file": "chunk-HXA6O6EE.js"}}}