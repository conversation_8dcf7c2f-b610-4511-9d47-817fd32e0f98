import React from 'react';
import { motion } from 'framer-motion';
import { Heart, Download, RotateCcw, Search } from 'lucide-react';

const LikedSongs = () => {
  return (
    <div className="p-6">
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="mb-8"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Liked Songs</h1>
            <p className="text-gray-400">Manage your liked songs library</p>
          </div>
          <div className="flex space-x-3">
            <button className="btn-secondary flex items-center space-x-2">
              <Download className="w-4 h-4" />
              <span>Backup</span>
            </button>
            <button className="btn-secondary flex items-center space-x-2 text-red-400 border-red-400 hover:bg-red-400 hover:text-white">
              <RotateCcw className="w-4 h-4" />
              <span>Reset</span>
            </button>
          </div>
        </div>
      </motion.div>

      {/* Stats */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6"
      >
        {[
          { label: 'Total Liked Songs', value: '1,234', icon: Heart },
          { label: 'Added via App', value: '156', icon: Heart },
          { label: 'Total Duration', value: '82h 15m', icon: Heart },
        ].map((stat, index) => (
          <div key={stat.label} className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">{stat.label}</p>
                <p className="text-2xl font-bold text-white">{stat.value}</p>
              </div>
              <stat.icon className="w-8 h-8 text-red-400" />
            </div>
          </div>
        ))}
      </motion.div>

      {/* Search */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="card mb-6"
      >
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search your liked songs..."
            className="input-field pl-10 w-full"
          />
        </div>
      </motion.div>

      {/* Songs List */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.3 }}
        className="card"
      >
        <h2 className="text-xl font-bold text-white mb-4">Your Liked Songs</h2>
        <div className="space-y-3">
          {[1, 2, 3, 4, 5].map((item) => (
            <div key={item} className="flex items-center space-x-4 p-3 bg-spotify-black rounded-lg hover:bg-gray-800 transition-colors">
              <div className="w-12 h-12 bg-gradient-to-br from-spotify-green to-green-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold">{item}</span>
              </div>
              <div className="flex-1">
                <h3 className="text-white font-medium">Song Title {item}</h3>
                <p className="text-gray-400 text-sm">Artist Name • Album Name</p>
              </div>
              <div className="text-right">
                <p className="text-gray-400 text-sm">3:45</p>
                <p className="text-gray-500 text-xs">2 days ago</p>
              </div>
              <Heart className="w-5 h-5 text-red-400 fill-current" />
            </div>
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default LikedSongs;
