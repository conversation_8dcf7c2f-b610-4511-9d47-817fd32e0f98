import React from 'react';
import { motion } from 'framer-motion';
import { Music, Heart, Activity, TrendingUp } from 'lucide-react';

const Dashboard = () => {
  return (
    <div className="p-6">
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="mb-8"
      >
        <h1 className="text-3xl font-bold text-white mb-2">Dashboard</h1>
        <p className="text-gray-400">Welcome back! Here's your Spotify overview.</p>
      </motion.div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {[
          { icon: Music, label: 'Total Playlists', value: '42', color: 'text-blue-400' },
          { icon: Heart, label: 'Liked Songs', value: '1,234', color: 'text-red-400' },
          { icon: Activity, label: 'Active Operations', value: '3', color: 'text-yellow-400' },
          { icon: TrendingUp, label: 'This Month', value: '+15', color: 'text-green-400' },
        ].map((stat, index) => (
          <motion.div
            key={stat.label}
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: index * 0.1 }}
            className="card"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">{stat.label}</p>
                <p className="text-2xl font-bold text-white">{stat.value}</p>
              </div>
              <stat.icon className={`w-8 h-8 ${stat.color}`} />
            </div>
          </motion.div>
        ))}
      </div>

      {/* Content Placeholder */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.4 }}
          className="card"
        >
          <h2 className="text-xl font-bold text-white mb-4">Recent Activity</h2>
          <div className="space-y-3">
            {[1, 2, 3].map((item) => (
              <div key={item} className="flex items-center space-x-3 p-3 bg-spotify-black rounded-lg">
                <div className="w-2 h-2 bg-spotify-green rounded-full"></div>
                <div className="flex-1">
                  <p className="text-white text-sm">Operation completed</p>
                  <p className="text-gray-400 text-xs">2 minutes ago</p>
                </div>
              </div>
            ))}
          </div>
        </motion.div>

        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="card"
        >
          <h2 className="text-xl font-bold text-white mb-4">Quick Actions</h2>
          <div className="space-y-3">
            <button className="w-full btn-primary text-left">Create New Playlist</button>
            <button className="w-full btn-secondary text-left">Backup Liked Songs</button>
            <button className="w-full btn-secondary text-left">Find Duplicates</button>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Dashboard;
