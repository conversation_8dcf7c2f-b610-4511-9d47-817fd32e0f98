import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>ap, Co<PERSON>, GitMerge, ArrowUpDown, Search } from 'lucide-react';

const SmartFeatures = () => {
  const features = [
    {
      icon: Copy,
      title: 'Duplicate Detection',
      description: 'Find and remove duplicate tracks in your playlists',
      action: 'Find Duplicates',
    },
    {
      icon: GitMerge,
      title: 'Smart Merge',
      description: 'Intelligently combine multiple playlists with advanced options',
      action: 'Merge Playlists',
    },
    {
      icon: ArrowUpDown,
      title: 'Smart Sorting',
      description: 'Sort playlists by various criteria including audio features',
      action: 'Sort Playlist',
    },
    {
      icon: Search,
      title: 'Playlist Comparison',
      description: 'Compare playlists to find similarities and differences',
      action: 'Compare Playlists',
    },
  ];

  return (
    <div className="p-6">
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="mb-8"
      >
        <div className="flex items-center space-x-3 mb-4">
          <Zap className="w-8 h-8 text-spotify-green" />
          <h1 className="text-3xl font-bold text-white">Smart Features</h1>
        </div>
        <p className="text-gray-400">Advanced tools to optimize and organize your music library</p>
      </motion.div>

      {/* Features Grid */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8"
      >
        {features.map((feature, index) => (
          <motion.div
            key={feature.title}
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: index * 0.1 }}
            className="card hover-lift cursor-pointer group"
          >
            <div className="flex items-start space-x-4">
              <div className="w-12 h-12 bg-spotify-green/20 rounded-lg flex items-center justify-center group-hover:bg-spotify-green/30 transition-colors">
                <feature.icon className="w-6 h-6 text-spotify-green" />
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-bold text-white mb-2">{feature.title}</h3>
                <p className="text-gray-400 mb-4">{feature.description}</p>
                <button className="btn-primary text-sm">
                  {feature.action}
                </button>
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>

      {/* Recent Smart Operations */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.3 }}
        className="card"
      >
        <h2 className="text-xl font-bold text-white mb-4">Recent Smart Operations</h2>
        <div className="space-y-3">
          {[
            { type: 'Duplicate Detection', playlist: 'My Favorites', result: '5 duplicates found and removed' },
            { type: 'Smart Merge', playlist: 'Workout Mix + Gym Hits', result: 'Successfully merged 2 playlists' },
            { type: 'Smart Sort', playlist: 'Chill Vibes', result: 'Sorted by energy level (low to high)' },
          ].map((operation, index) => (
            <div key={index} className="flex items-center space-x-4 p-3 bg-spotify-black rounded-lg">
              <div className="w-2 h-2 bg-spotify-green rounded-full"></div>
              <div className="flex-1">
                <p className="text-white text-sm font-medium">{operation.type}</p>
                <p className="text-gray-400 text-xs">{operation.playlist}</p>
              </div>
              <div className="text-right">
                <p className="text-gray-300 text-xs">{operation.result}</p>
                <p className="text-gray-500 text-xs">2 hours ago</p>
              </div>
            </div>
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default SmartFeatures;
