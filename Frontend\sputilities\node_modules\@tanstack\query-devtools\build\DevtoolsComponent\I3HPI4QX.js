import { createLocalStorage, THEME_PREFERENCE, QueryDevtoolsContext, PiPProvider, ThemeContext, Devtools } from '../chunk/HASRA6WW.js';
import { getPreferredColorScheme, createMemo, createComponent } from '../chunk/HR7YXZ5H.js';

// src/DevtoolsComponent.tsx
var DevtoolsComponent = (props) => {
  const [localStore, setLocalStore] = createLocalStorage({
    prefix: "TanstackQueryDevtools"
  });
  const colorScheme = getPreferredColorScheme();
  const theme = createMemo(() => {
    const preference = localStore.theme_preference || THEME_PREFERENCE;
    if (preference !== "system") return preference;
    return colorScheme();
  });
  return createComponent(QueryDevtoolsContext.Provider, {
    value: props,
    get children() {
      return createComponent(PiPProvider, {
        localStore,
        setLocalStore,
        get children() {
          return createComponent(ThemeContext.Provider, {
            value: theme,
            get children() {
              return createComponent(Devtools, {
                localStore,
                setLocalStore
              });
            }
          });
        }
      });
    }
  });
};
var DevtoolsComponent_default = DevtoolsComponent;

export { DevtoolsComponent_default as default };
