import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: 'http://localhost:3600/api/v1',
  withCredentials: true,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging and auth
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error);
    
    if (error.response?.status === 401) {
      // Handle unauthorized access
      window.location.href = '/login';
    }
    
    return Promise.reject(error);
  }
);

// Authentication API
export const authAPI = {
  login: () => api.get('/auth/login'),
  check: () => api.get('/auth/check'),
  logout: () => api.post('/auth/logout'),
};

// Playlists API
export const playlistsAPI = {
  getPlaylists: (params = {}) => api.get('/playlists', { params }),
  createPlaylist: (data) => api.post('/playlists', data),
  deletePlaylist: (playlistId) => api.delete(`/playlists/${playlistId}`),
  bulkDelete: (playlistIds) => api.post('/playlists/bulk-delete', { playlistIds }),
  getAnalytics: () => api.get('/playlists/analytics'),
};

// Liked Songs API
export const likedSongsAPI = {
  getLikedSongs: (params = {}) => api.get('/liked-songs', { params }),
  likePlaylist: (playlistId) => api.post(`/liked-songs/like-playlist/${playlistId}`),
  reset: () => api.post('/liked-songs/reset'),
  backup: (data) => api.post('/liked-songs/backup', data),
  getAnalytics: () => api.get('/liked-songs/analytics'),
};

// Operations API
export const operationsAPI = {
  getOperations: (params = {}) => api.get('/operations', { params }),
  getOperation: (operationId) => api.get(`/operations/${operationId}`),
  cancelOperation: (operationId) => api.post(`/operations/${operationId}/cancel`),
};

// Playlist Manager API
export const playlistManagerAPI = {
  getDashboard: () => api.get('/playlist-manager/dashboard'),
  getPlaylists: (params = {}) => api.get('/playlist-manager/playlists', { params }),
  getDeletionPreview: (playlistIds) => api.post('/playlist-manager/deletion-preview', { playlistIds }),
  getDuplicates: () => api.get('/playlist-manager/duplicates'),
};

// Tracks API
export const tracksAPI = {
  search: (params = {}) => api.get('/tracks/search', { params }),
  getAnalytics: (trackId) => api.get(`/tracks/${trackId}/analytics`),
  batchOperations: (data) => api.post('/tracks/batch-operations', data),
  addToPlaylist: (playlistId, data) => api.post(`/playlists/${playlistId}/tracks`, data),
  addAlbumToPlaylist: (playlistId, data) => api.post(`/playlists/${playlistId}/albums`, data),
  removeFromPlaylist: (playlistId, data) => api.delete(`/playlists/${playlistId}/tracks`, { data }),
  reorderTracks: (playlistId, data) => api.put(`/playlists/${playlistId}/tracks/reorder`, data),
  filterPlaylistTracks: (playlistId, params = {}) => api.get(`/playlists/${playlistId}/tracks/filter`, { params }),
  getPlaylistAnalytics: (playlistId) => api.get(`/playlists/${playlistId}/analytics`),
};

// Smart Management API
export const smartAPI = {
  findDuplicates: (playlistId) => api.get(`/smart/playlists/${playlistId}/duplicates`),
  removeDuplicates: (playlistId, data) => api.post(`/smart/playlists/${playlistId}/remove-duplicates`, data),
  comparePlaylists: (data) => api.post('/smart/playlists/compare', data),
  mergePlaylists: (data) => api.post('/smart/playlists/merge', data),
  previewMerge: (data) => api.post('/smart/playlists/merge/preview', data),
  sortPlaylist: (playlistId, data) => api.put(`/smart/playlists/${playlistId}/sort`, data),
  getGenres: () => api.get('/smart/genres'),
};

// Error handling utility
export const handleAPIError = (error) => {
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  if (error.message) {
    return error.message;
  }
  return 'An unexpected error occurred';
};

// Success response utility
export const getAPIData = (response) => {
  return response.data?.data || response.data;
};

export default api;
