/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<f487a5c07b14844a12641cc26cf7500f>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/NativeModules/specs/NativeDialogManagerAndroid.js
 */

export * from "../../../src/private/specs_DEPRECATED/modules/NativeDialogManagerAndroid";
import NativeDialogManagerAndroid from "../../../src/private/specs_DEPRECATED/modules/NativeDialogManagerAndroid";
declare const $$NativeDialogManagerAndroid: typeof NativeDialogManagerAndroid;
declare type $$NativeDialogManagerAndroid = typeof $$NativeDialogManagerAndroid;
export default $$NativeDialogManagerAndroid;
