/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<cc4b4373a1bd4ebb6f384549778a3397>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Utilities/differ/deepDiffer.js
 */

type Options = {
  readonly unsafelyIgnoreFunctions?: boolean;
};
declare function deepDiffer(one: any, two: any, maxDepthOrOptions?: Options | number, maybeOptions?: Options): boolean;
declare const $$deepDiffer: typeof deepDiffer;
declare type $$deepDiffer = typeof $$deepDiffer;
export default $$deepDiffer;
