{"name": "sputilities", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint src --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/react-fontawesome": "^0.2.0", "axios": "^1.4.0", "react-intersection-observer": "^9.5.2", "react-loader-spinner": "^5.4.5", "react-router-dom": "^6.15.0"}, "devDependencies": {"@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "@babel/preset-react": "^7.22.5", "@types/react": "^18.0.37", "@types/react-dom": "^18.0.11", "@vitejs/plugin-react": "^4.0.0", "babel-loader": "^9.1.3", "eslint": "^8.38.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "react": "^18.2.0", "react-dom": "^18.2.0", "vite": "^4.3.9"}}