import React from 'react';
import { motion } from 'framer-motion';
import { Plus, Search, Filter } from 'lucide-react';

const PlaylistManager = () => {
  return (
    <div className="p-6">
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="mb-8"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Playlist Manager</h1>
            <p className="text-gray-400">Manage and organize your Spotify playlists</p>
          </div>
          <button className="btn-primary flex items-center space-x-2">
            <Plus className="w-4 h-4" />
            <span>Create Playlist</span>
          </button>
        </div>
      </motion.div>

      {/* Search and Filters */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.1 }}
        className="card mb-6"
      >
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search playlists..."
              className="input-field pl-10 w-full"
            />
          </div>
          <button className="btn-secondary flex items-center space-x-2">
            <Filter className="w-4 h-4" />
            <span>Filters</span>
          </button>
        </div>
      </motion.div>

      {/* Playlist Grid */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
      >
        {[1, 2, 3, 4, 5, 6, 7, 8].map((item) => (
          <div key={item} className="card hover-lift cursor-pointer">
            <div className="w-full h-48 bg-gradient-to-br from-spotify-green to-green-600 rounded-lg mb-4 flex items-center justify-center">
              <span className="text-white text-4xl font-bold">{item}</span>
            </div>
            <h3 className="text-white font-semibold mb-1">Playlist {item}</h3>
            <p className="text-gray-400 text-sm mb-2">25 songs • 1h 30m</p>
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-500">Created via app</span>
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <div className="w-2 h-2 bg-gray-600 rounded-full"></div>
              </div>
            </div>
          </div>
        ))}
      </motion.div>
    </div>
  );
};

export default PlaylistManager;
