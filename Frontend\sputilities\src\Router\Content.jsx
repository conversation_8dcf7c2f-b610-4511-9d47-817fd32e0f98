import React from "react";
import { Routes, Route, Navigate } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "react-hot-toast";

// Pages
import LandingPage from "../pages/LandingPage";
import Dashboard from "../pages/Dashboard";
import PlaylistManager from "../pages/PlaylistManager";
import LikedSongs from "../pages/LikedSongs";
import Operations from "../pages/Operations";
import SmartFeatures from "../pages/SmartFeatures";
import Settings from "../pages/Settings";
import Login from "../pages/Login";
import AuthCallback from "../pages/AuthCallback";

// Components
import ProtectedRoute from "../components/ProtectedRoute";
import Layout from "../components/Layout";

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

const Content = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <div className="min-h-screen bg-spotify-black">
        <Routes>
          {/* Public Routes */}
          <Route path="/" element={<LandingPage />} />
          <Route path="/login" element={<Login />} />
          <Route path="/auth/callback" element={<AuthCallback />} />

          {/* Protected Routes */}
          <Route path="/app" element={
            <ProtectedRoute>
              <Layout />
            </ProtectedRoute>
          }>
            <Route index element={<Navigate to="/app/dashboard" replace />} />
            <Route path="dashboard" element={<Dashboard />} />
            <Route path="playlists" element={<PlaylistManager />} />
            <Route path="liked-songs" element={<LikedSongs />} />
            <Route path="operations" element={<Operations />} />
            <Route path="smart" element={<SmartFeatures />} />
            <Route path="settings" element={<Settings />} />
          </Route>

          {/* Catch all route */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>

        {/* Global Toast Notifications */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#121212',
              color: '#fff',
              border: '1px solid #1DB954',
            },
            success: {
              iconTheme: {
                primary: '#1DB954',
                secondary: '#fff',
              },
            },
            error: {
              iconTheme: {
                primary: '#ef4444',
                secondary: '#fff',
              },
            },
          }}
        />
      </div>
    </QueryClientProvider>
  );
};

export default Content;
