import React from 'react';
import { motion } from 'framer-motion';
import { Activity, Clock, CheckCircle, XCircle, Pause } from 'lucide-react';

const Operations = () => {
  const operations = [
    { id: 1, type: 'like_all_playlist_songs', status: 'completed', progress: 100, name: 'Like all songs from "My Favorites"' },
    { id: 2, type: 'bulk_delete_playlists', status: 'in_progress', progress: 65, name: 'Delete 5 playlists' },
    { id: 3, type: 'backup_liked_songs', status: 'pending', progress: 0, name: 'Backup liked songs' },
    { id: 4, type: 'remove_duplicates', status: 'failed', progress: 45, name: 'Remove duplicates from "Workout Mix"' },
  ];

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'in_progress':
        return <Activity className="w-5 h-5 text-blue-400 animate-spin" />;
      case 'pending':
        return <Clock className="w-5 h-5 text-yellow-400" />;
      case 'failed':
        return <XCircle className="w-5 h-5 text-red-400" />;
      default:
        return <Pause className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'text-green-400';
      case 'in_progress':
        return 'text-blue-400';
      case 'pending':
        return 'text-yellow-400';
      case 'failed':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  return (
    <div className="p-6">
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="mb-8"
      >
        <h1 className="text-3xl font-bold text-white mb-2">Operations</h1>
        <p className="text-gray-400">Track and manage your background operations</p>
      </motion.div>

      {/* Stats */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"
      >
        {[
          { label: 'Total Operations', value: '24', color: 'text-blue-400' },
          { label: 'In Progress', value: '1', color: 'text-yellow-400' },
          { label: 'Completed', value: '20', color: 'text-green-400' },
          { label: 'Failed', value: '3', color: 'text-red-400' },
        ].map((stat, index) => (
          <div key={stat.label} className="card">
            <p className="text-gray-400 text-sm">{stat.label}</p>
            <p className={`text-2xl font-bold ${stat.color}`}>{stat.value}</p>
          </div>
        ))}
      </motion.div>

      {/* Operations List */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="card"
      >
        <h2 className="text-xl font-bold text-white mb-4">Recent Operations</h2>
        <div className="space-y-4">
          {operations.map((operation, index) => (
            <motion.div
              key={operation.id}
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: index * 0.1 }}
              className="p-4 bg-spotify-black rounded-lg border border-gray-800"
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(operation.status)}
                  <div>
                    <h3 className="text-white font-medium">{operation.name}</h3>
                    <p className="text-gray-400 text-sm capitalize">{operation.type.replace(/_/g, ' ')}</p>
                  </div>
                </div>
                <span className={`text-sm font-medium capitalize ${getStatusColor(operation.status)}`}>
                  {operation.status}
                </span>
              </div>
              
              {/* Progress Bar */}
              <div className="w-full bg-gray-700 rounded-full h-2">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: `${operation.progress}%` }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className={`h-2 rounded-full ${
                    operation.status === 'completed' ? 'bg-green-400' :
                    operation.status === 'in_progress' ? 'bg-blue-400' :
                    operation.status === 'failed' ? 'bg-red-400' : 'bg-gray-600'
                  }`}
                />
              </div>
              <div className="flex justify-between items-center mt-2">
                <span className="text-xs text-gray-400">{operation.progress}% complete</span>
                {operation.status === 'in_progress' && (
                  <button className="text-xs text-red-400 hover:text-red-300">Cancel</button>
                )}
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default Operations;
