{"version": 3, "sources": ["../../@tanstack/virtual-core/src/utils.ts", "../../@tanstack/virtual-core/src/index.ts", "../../@tanstack/react-virtual/src/index.tsx", "../../@headlessui/react/dist/components/combobox/combobox.js", "../../@headlessui/react/dist/hooks/use-computed.js", "../../@headlessui/react/dist/hooks/use-iso-morphic-effect.js", "../../@headlessui/react/dist/utils/env.js", "../../@headlessui/react/dist/hooks/use-latest-value.js", "../../@headlessui/react/dist/hooks/use-controllable.js", "../../@headlessui/react/dist/hooks/use-event.js", "../../@headlessui/react/dist/hooks/use-disposables.js", "../../@headlessui/react/dist/utils/micro-task.js", "../../@headlessui/react/dist/utils/disposables.js", "../../@headlessui/react/dist/hooks/use-id.js", "../../@headlessui/react/dist/hooks/use-server-handoff-complete.js", "../../@headlessui/react/dist/hooks/use-outside-click.js", "../../@headlessui/react/dist/utils/match.js", "../../@headlessui/react/dist/utils/owner.js", "../../@headlessui/react/dist/utils/focus-management.js", "../../@headlessui/react/dist/utils/platform.js", "../../@headlessui/react/dist/hooks/use-document-event.js", "../../@headlessui/react/dist/hooks/use-window-event.js", "../../@headlessui/react/dist/hooks/use-owner.js", "../../@headlessui/react/dist/hooks/use-resolve-button-type.js", "../../@headlessui/react/dist/hooks/use-sync-refs.js", "../../@headlessui/react/dist/hooks/use-tracked-pointer.js", "../../@headlessui/react/dist/hooks/use-tree-walker.js", "../../@headlessui/react/dist/hooks/use-watch.js", "../../@headlessui/react/dist/utils/render.js", "../../@headlessui/react/dist/utils/class-names.js", "../../@headlessui/react/dist/internal/hidden.js", "../../@headlessui/react/dist/internal/open-closed.js", "../../@headlessui/react/dist/utils/document-ready.js", "../../@headlessui/react/dist/utils/active-element-history.js", "../../@headlessui/react/dist/utils/bugs.js", "../../@headlessui/react/dist/utils/calculate-active-index.js", "../../@headlessui/react/dist/utils/form.js", "../../@headlessui/react/dist/components/keyboard.js", "../../@headlessui/react/dist/components/dialog/dialog.js", "../../@headlessui/react/dist/components/focus-trap/focus-trap.js", "../../@headlessui/react/dist/hooks/use-event-listener.js", "../../@headlessui/react/dist/hooks/use-is-mounted.js", "../../@headlessui/react/dist/hooks/use-on-unmount.js", "../../@headlessui/react/dist/hooks/use-tab-direction.js", "../../@headlessui/react/dist/components/portal/portal.js", "../../@headlessui/react/dist/internal/portal-force-root.js", "../../@headlessui/react/dist/use-sync-external-store-shim/index.js", "../../@headlessui/react/dist/use-sync-external-store-shim/useSyncExternalStoreShimClient.js", "../../@headlessui/react/dist/use-sync-external-store-shim/useSyncExternalStoreShimServer.js", "../../@headlessui/react/dist/hooks/use-store.js", "../../@headlessui/react/dist/utils/store.js", "../../@headlessui/react/dist/hooks/document-overflow/adjust-scrollbar-padding.js", "../../@headlessui/react/dist/hooks/document-overflow/handle-ios-locking.js", "../../@headlessui/react/dist/hooks/document-overflow/prevent-scroll.js", "../../@headlessui/react/dist/hooks/document-overflow/overflow-store.js", "../../@headlessui/react/dist/hooks/document-overflow/use-document-overflow.js", "../../@headlessui/react/dist/hooks/use-inert.js", "../../@headlessui/react/dist/hooks/use-root-containers.js", "../../@headlessui/react/dist/internal/stack-context.js", "../../@headlessui/react/dist/components/description/description.js", "../../@headlessui/react/dist/components/disclosure/disclosure.js", "../../@headlessui/react/dist/utils/start-transition.js", "../../@headlessui/react/dist/components/listbox/listbox.js", "../../@headlessui/react/dist/hooks/use-text-value.js", "../../@headlessui/react/dist/utils/get-text-value.js", "../../@headlessui/react/dist/components/menu/menu.js", "../../@headlessui/react/dist/components/popover/popover.js", "../../@headlessui/react/dist/components/radio-group/radio-group.js", "../../@headlessui/react/dist/components/label/label.js", "../../@headlessui/react/dist/hooks/use-flags.js", "../../@headlessui/react/dist/components/switch/switch.js", "../../@headlessui/react/dist/components/tabs/tabs.js", "../../@headlessui/react/dist/internal/focus-sentinel.js", "../../@headlessui/react/dist/utils/stable-collection.js", "../../@headlessui/react/dist/components/transitions/transition.js", "../../@headlessui/react/dist/utils/once.js", "../../@headlessui/react/dist/components/transitions/utils/transition.js", "../../@headlessui/react/dist/hooks/use-transition.js"], "sourcesContent": ["export type NoInfer<A extends any> = [A][A extends any ? 0 : never]\n\nexport type PartialKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>\n\nexport function memo<TDeps extends ReadonlyArray<any>, TResult>(\n  getDeps: () => [...TDeps],\n  fn: (...args: NoInfer<[...TDeps]>) => TResult,\n  opts: {\n    key: false | string\n    debug?: () => boolean\n    onChange?: (result: TResult) => void\n    initialDeps?: TDeps\n  },\n) {\n  let deps = opts.initialDeps ?? []\n  let result: TResult | undefined\n\n  function memoizedFunction(): TResult {\n    let depTime: number\n    if (opts.key && opts.debug?.()) depTime = Date.now()\n\n    const newDeps = getDeps()\n\n    const depsChanged =\n      newDeps.length !== deps.length ||\n      newDeps.some((dep: any, index: number) => deps[index] !== dep)\n\n    if (!depsChanged) {\n      return result!\n    }\n\n    deps = newDeps\n\n    let resultTime: number\n    if (opts.key && opts.debug?.()) resultTime = Date.now()\n\n    result = fn(...newDeps)\n\n    if (opts.key && opts.debug?.()) {\n      const depEndTime = Math.round((Date.now() - depTime!) * 100) / 100\n      const resultEndTime = Math.round((Date.now() - resultTime!) * 100) / 100\n      const resultFpsPercentage = resultEndTime / 16\n\n      const pad = (str: number | string, num: number) => {\n        str = String(str)\n        while (str.length < num) {\n          str = ' ' + str\n        }\n        return str\n      }\n\n      console.info(\n        `%c⏱ ${pad(resultEndTime, 5)} /${pad(depEndTime, 5)} ms`,\n        `\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(\n              0,\n              Math.min(120 - 120 * resultFpsPercentage, 120),\n            )}deg 100% 31%);`,\n        opts?.key,\n      )\n    }\n\n    opts?.onChange?.(result)\n\n    return result\n  }\n\n  // Attach updateDeps to the function itself\n  memoizedFunction.updateDeps = (newDeps: [...TDeps]) => {\n    deps = newDeps\n  }\n\n  return memoizedFunction\n}\n\nexport function notUndefined<T>(value: T | undefined, msg?: string): T {\n  if (value === undefined) {\n    throw new Error(`Unexpected undefined${msg ? `: ${msg}` : ''}`)\n  } else {\n    return value\n  }\n}\n\nexport const approxEqual = (a: number, b: number) => Math.abs(a - b) <= 1\n\nexport const debounce = (\n  targetWindow: Window & typeof globalThis,\n  fn: Function,\n  ms: number,\n) => {\n  let timeoutId: number\n  return function (this: any, ...args: Array<any>) {\n    targetWindow.clearTimeout(timeoutId)\n    timeoutId = targetWindow.setTimeout(() => fn.apply(this, args), ms)\n  }\n}\n", "import { approxEqual, debounce, memo, notUndefined } from './utils'\n\nexport * from './utils'\n\n//\n\ntype ScrollDirection = 'forward' | 'backward'\n\ntype ScrollAlignment = 'start' | 'center' | 'end' | 'auto'\n\ntype ScrollBehavior = 'auto' | 'smooth'\n\nexport interface ScrollToOptions {\n  align?: ScrollAlignment\n  behavior?: ScrollBehavior\n}\n\ntype ScrollToOffsetOptions = ScrollToOptions\n\ntype ScrollToIndexOptions = ScrollToOptions\n\nexport interface Range {\n  startIndex: number\n  endIndex: number\n  overscan: number\n  count: number\n}\n\ntype Key = number | string | bigint\n\nexport interface VirtualItem {\n  key: Key\n  index: number\n  start: number\n  end: number\n  size: number\n  lane: number\n}\n\nexport interface Rect {\n  width: number\n  height: number\n}\n\n//\n\nconst getRect = (element: HTMLElement): Rect => {\n  const { offsetWidth, offsetHeight } = element\n  return { width: offsetWidth, height: offsetHeight }\n}\n\nexport const defaultKeyExtractor = (index: number) => index\n\nexport const defaultRangeExtractor = (range: Range) => {\n  const start = Math.max(range.startIndex - range.overscan, 0)\n  const end = Math.min(range.endIndex + range.overscan, range.count - 1)\n\n  const arr = []\n\n  for (let i = start; i <= end; i++) {\n    arr.push(i)\n  }\n\n  return arr\n}\n\nexport const observeElementRect = <T extends Element>(\n  instance: Virtualizer<T, any>,\n  cb: (rect: Rect) => void,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n  const targetWindow = instance.targetWindow\n  if (!targetWindow) {\n    return\n  }\n\n  const handler = (rect: Rect) => {\n    const { width, height } = rect\n    cb({ width: Math.round(width), height: Math.round(height) })\n  }\n\n  handler(getRect(element as unknown as HTMLElement))\n\n  if (!targetWindow.ResizeObserver) {\n    return () => {}\n  }\n\n  const observer = new targetWindow.ResizeObserver((entries) => {\n    const run = () => {\n      const entry = entries[0]\n      if (entry?.borderBoxSize) {\n        const box = entry.borderBoxSize[0]\n        if (box) {\n          handler({ width: box.inlineSize, height: box.blockSize })\n          return\n        }\n      }\n      handler(getRect(element as unknown as HTMLElement))\n    }\n\n    instance.options.useAnimationFrameWithResizeObserver\n      ? requestAnimationFrame(run)\n      : run()\n  })\n\n  observer.observe(element, { box: 'border-box' })\n\n  return () => {\n    observer.unobserve(element)\n  }\n}\n\nconst addEventListenerOptions = {\n  passive: true,\n}\n\nexport const observeWindowRect = (\n  instance: Virtualizer<Window, any>,\n  cb: (rect: Rect) => void,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n\n  const handler = () => {\n    cb({ width: element.innerWidth, height: element.innerHeight })\n  }\n  handler()\n\n  element.addEventListener('resize', handler, addEventListenerOptions)\n\n  return () => {\n    element.removeEventListener('resize', handler)\n  }\n}\n\nconst supportsScrollend =\n  typeof window == 'undefined' ? true : 'onscrollend' in window\n\ntype ObserveOffsetCallBack = (offset: number, isScrolling: boolean) => void\n\nexport const observeElementOffset = <T extends Element>(\n  instance: Virtualizer<T, any>,\n  cb: ObserveOffsetCallBack,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n  const targetWindow = instance.targetWindow\n  if (!targetWindow) {\n    return\n  }\n\n  let offset = 0\n  const fallback =\n    instance.options.useScrollendEvent && supportsScrollend\n      ? () => undefined\n      : debounce(\n          targetWindow,\n          () => {\n            cb(offset, false)\n          },\n          instance.options.isScrollingResetDelay,\n        )\n\n  const createHandler = (isScrolling: boolean) => () => {\n    const { horizontal, isRtl } = instance.options\n    offset = horizontal\n      ? element['scrollLeft'] * ((isRtl && -1) || 1)\n      : element['scrollTop']\n    fallback()\n    cb(offset, isScrolling)\n  }\n  const handler = createHandler(true)\n  const endHandler = createHandler(false)\n  endHandler()\n\n  element.addEventListener('scroll', handler, addEventListenerOptions)\n  const registerScrollendEvent =\n    instance.options.useScrollendEvent && supportsScrollend\n  if (registerScrollendEvent) {\n    element.addEventListener('scrollend', endHandler, addEventListenerOptions)\n  }\n  return () => {\n    element.removeEventListener('scroll', handler)\n    if (registerScrollendEvent) {\n      element.removeEventListener('scrollend', endHandler)\n    }\n  }\n}\n\nexport const observeWindowOffset = (\n  instance: Virtualizer<Window, any>,\n  cb: ObserveOffsetCallBack,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n  const targetWindow = instance.targetWindow\n  if (!targetWindow) {\n    return\n  }\n\n  let offset = 0\n  const fallback =\n    instance.options.useScrollendEvent && supportsScrollend\n      ? () => undefined\n      : debounce(\n          targetWindow,\n          () => {\n            cb(offset, false)\n          },\n          instance.options.isScrollingResetDelay,\n        )\n\n  const createHandler = (isScrolling: boolean) => () => {\n    offset = element[instance.options.horizontal ? 'scrollX' : 'scrollY']\n    fallback()\n    cb(offset, isScrolling)\n  }\n  const handler = createHandler(true)\n  const endHandler = createHandler(false)\n  endHandler()\n\n  element.addEventListener('scroll', handler, addEventListenerOptions)\n  const registerScrollendEvent =\n    instance.options.useScrollendEvent && supportsScrollend\n  if (registerScrollendEvent) {\n    element.addEventListener('scrollend', endHandler, addEventListenerOptions)\n  }\n  return () => {\n    element.removeEventListener('scroll', handler)\n    if (registerScrollendEvent) {\n      element.removeEventListener('scrollend', endHandler)\n    }\n  }\n}\n\nexport const measureElement = <TItemElement extends Element>(\n  element: TItemElement,\n  entry: ResizeObserverEntry | undefined,\n  instance: Virtualizer<any, TItemElement>,\n) => {\n  if (entry?.borderBoxSize) {\n    const box = entry.borderBoxSize[0]\n    if (box) {\n      const size = Math.round(\n        box[instance.options.horizontal ? 'inlineSize' : 'blockSize'],\n      )\n      return size\n    }\n  }\n\n  return (element as unknown as HTMLElement)[\n    instance.options.horizontal ? 'offsetWidth' : 'offsetHeight'\n  ]\n}\n\nexport const windowScroll = <T extends Window>(\n  offset: number,\n  {\n    adjustments = 0,\n    behavior,\n  }: { adjustments?: number; behavior?: ScrollBehavior },\n  instance: Virtualizer<T, any>,\n) => {\n  const toOffset = offset + adjustments\n\n  instance.scrollElement?.scrollTo?.({\n    [instance.options.horizontal ? 'left' : 'top']: toOffset,\n    behavior,\n  })\n}\n\nexport const elementScroll = <T extends Element>(\n  offset: number,\n  {\n    adjustments = 0,\n    behavior,\n  }: { adjustments?: number; behavior?: ScrollBehavior },\n  instance: Virtualizer<T, any>,\n) => {\n  const toOffset = offset + adjustments\n\n  instance.scrollElement?.scrollTo?.({\n    [instance.options.horizontal ? 'left' : 'top']: toOffset,\n    behavior,\n  })\n}\n\nexport interface VirtualizerOptions<\n  TScrollElement extends Element | Window,\n  TItemElement extends Element,\n> {\n  // Required from the user\n  count: number\n  getScrollElement: () => TScrollElement | null\n  estimateSize: (index: number) => number\n\n  // Required from the framework adapter (but can be overridden)\n  scrollToFn: (\n    offset: number,\n    options: { adjustments?: number; behavior?: ScrollBehavior },\n    instance: Virtualizer<TScrollElement, TItemElement>,\n  ) => void\n  observeElementRect: (\n    instance: Virtualizer<TScrollElement, TItemElement>,\n    cb: (rect: Rect) => void,\n  ) => void | (() => void)\n  observeElementOffset: (\n    instance: Virtualizer<TScrollElement, TItemElement>,\n    cb: ObserveOffsetCallBack,\n  ) => void | (() => void)\n  // Optional\n  debug?: boolean\n  initialRect?: Rect\n  onChange?: (\n    instance: Virtualizer<TScrollElement, TItemElement>,\n    sync: boolean,\n  ) => void\n  measureElement?: (\n    element: TItemElement,\n    entry: ResizeObserverEntry | undefined,\n    instance: Virtualizer<TScrollElement, TItemElement>,\n  ) => number\n  overscan?: number\n  horizontal?: boolean\n  paddingStart?: number\n  paddingEnd?: number\n  scrollPaddingStart?: number\n  scrollPaddingEnd?: number\n  initialOffset?: number | (() => number)\n  getItemKey?: (index: number) => Key\n  rangeExtractor?: (range: Range) => Array<number>\n  scrollMargin?: number\n  gap?: number\n  indexAttribute?: string\n  initialMeasurementsCache?: Array<VirtualItem>\n  lanes?: number\n  isScrollingResetDelay?: number\n  useScrollendEvent?: boolean\n  enabled?: boolean\n  isRtl?: boolean\n  useAnimationFrameWithResizeObserver?: boolean\n}\n\nexport class Virtualizer<\n  TScrollElement extends Element | Window,\n  TItemElement extends Element,\n> {\n  private unsubs: Array<void | (() => void)> = []\n  options!: Required<VirtualizerOptions<TScrollElement, TItemElement>>\n  scrollElement: TScrollElement | null = null\n  targetWindow: (Window & typeof globalThis) | null = null\n  isScrolling = false\n  private scrollToIndexTimeoutId: number | null = null\n  measurementsCache: Array<VirtualItem> = []\n  private itemSizeCache = new Map<Key, number>()\n  private pendingMeasuredCacheIndexes: Array<number> = []\n  scrollRect: Rect | null = null\n  scrollOffset: number | null = null\n  scrollDirection: ScrollDirection | null = null\n  private scrollAdjustments = 0\n  shouldAdjustScrollPositionOnItemSizeChange:\n    | undefined\n    | ((\n        item: VirtualItem,\n        delta: number,\n        instance: Virtualizer<TScrollElement, TItemElement>,\n      ) => boolean)\n  elementsCache = new Map<Key, TItemElement>()\n  private observer = (() => {\n    let _ro: ResizeObserver | null = null\n\n    const get = () => {\n      if (_ro) {\n        return _ro\n      }\n\n      if (!this.targetWindow || !this.targetWindow.ResizeObserver) {\n        return null\n      }\n\n      return (_ro = new this.targetWindow.ResizeObserver((entries) => {\n        entries.forEach((entry) => {\n          const run = () => {\n            this._measureElement(entry.target as TItemElement, entry)\n          }\n          this.options.useAnimationFrameWithResizeObserver\n            ? requestAnimationFrame(run)\n            : run()\n        })\n      }))\n    }\n\n    return {\n      disconnect: () => {\n        get()?.disconnect()\n        _ro = null\n      },\n      observe: (target: Element) =>\n        get()?.observe(target, { box: 'border-box' }),\n      unobserve: (target: Element) => get()?.unobserve(target),\n    }\n  })()\n  range: { startIndex: number; endIndex: number } | null = null\n\n  constructor(opts: VirtualizerOptions<TScrollElement, TItemElement>) {\n    this.setOptions(opts)\n  }\n\n  setOptions = (opts: VirtualizerOptions<TScrollElement, TItemElement>) => {\n    Object.entries(opts).forEach(([key, value]) => {\n      if (typeof value === 'undefined') delete (opts as any)[key]\n    })\n\n    this.options = {\n      debug: false,\n      initialOffset: 0,\n      overscan: 1,\n      paddingStart: 0,\n      paddingEnd: 0,\n      scrollPaddingStart: 0,\n      scrollPaddingEnd: 0,\n      horizontal: false,\n      getItemKey: defaultKeyExtractor,\n      rangeExtractor: defaultRangeExtractor,\n      onChange: () => {},\n      measureElement,\n      initialRect: { width: 0, height: 0 },\n      scrollMargin: 0,\n      gap: 0,\n      indexAttribute: 'data-index',\n      initialMeasurementsCache: [],\n      lanes: 1,\n      isScrollingResetDelay: 150,\n      enabled: true,\n      isRtl: false,\n      useScrollendEvent: false,\n      useAnimationFrameWithResizeObserver: false,\n      ...opts,\n    }\n  }\n\n  private notify = (sync: boolean) => {\n    this.options.onChange?.(this, sync)\n  }\n\n  private maybeNotify = memo(\n    () => {\n      this.calculateRange()\n\n      return [\n        this.isScrolling,\n        this.range ? this.range.startIndex : null,\n        this.range ? this.range.endIndex : null,\n      ]\n    },\n    (isScrolling) => {\n      this.notify(isScrolling)\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'maybeNotify',\n      debug: () => this.options.debug,\n      initialDeps: [\n        this.isScrolling,\n        this.range ? this.range.startIndex : null,\n        this.range ? this.range.endIndex : null,\n      ] as [boolean, number | null, number | null],\n    },\n  )\n\n  private cleanup = () => {\n    this.unsubs.filter(Boolean).forEach((d) => d!())\n    this.unsubs = []\n    this.observer.disconnect()\n    this.scrollElement = null\n    this.targetWindow = null\n  }\n\n  _didMount = () => {\n    return () => {\n      this.cleanup()\n    }\n  }\n\n  _willUpdate = () => {\n    const scrollElement = this.options.enabled\n      ? this.options.getScrollElement()\n      : null\n\n    if (this.scrollElement !== scrollElement) {\n      this.cleanup()\n\n      if (!scrollElement) {\n        this.maybeNotify()\n        return\n      }\n\n      this.scrollElement = scrollElement\n\n      if (this.scrollElement && 'ownerDocument' in this.scrollElement) {\n        this.targetWindow = this.scrollElement.ownerDocument.defaultView\n      } else {\n        this.targetWindow = this.scrollElement?.window ?? null\n      }\n\n      this.elementsCache.forEach((cached) => {\n        this.observer.observe(cached)\n      })\n\n      this._scrollToOffset(this.getScrollOffset(), {\n        adjustments: undefined,\n        behavior: undefined,\n      })\n\n      this.unsubs.push(\n        this.options.observeElementRect(this, (rect) => {\n          this.scrollRect = rect\n          this.maybeNotify()\n        }),\n      )\n\n      this.unsubs.push(\n        this.options.observeElementOffset(this, (offset, isScrolling) => {\n          this.scrollAdjustments = 0\n          this.scrollDirection = isScrolling\n            ? this.getScrollOffset() < offset\n              ? 'forward'\n              : 'backward'\n            : null\n          this.scrollOffset = offset\n          this.isScrolling = isScrolling\n\n          this.maybeNotify()\n        }),\n      )\n    }\n  }\n\n  private getSize = () => {\n    if (!this.options.enabled) {\n      this.scrollRect = null\n      return 0\n    }\n\n    this.scrollRect = this.scrollRect ?? this.options.initialRect\n\n    return this.scrollRect[this.options.horizontal ? 'width' : 'height']\n  }\n\n  private getScrollOffset = () => {\n    if (!this.options.enabled) {\n      this.scrollOffset = null\n      return 0\n    }\n\n    this.scrollOffset =\n      this.scrollOffset ??\n      (typeof this.options.initialOffset === 'function'\n        ? this.options.initialOffset()\n        : this.options.initialOffset)\n\n    return this.scrollOffset\n  }\n\n  private getFurthestMeasurement = (\n    measurements: Array<VirtualItem>,\n    index: number,\n  ) => {\n    const furthestMeasurementsFound = new Map<number, true>()\n    const furthestMeasurements = new Map<number, VirtualItem>()\n    for (let m = index - 1; m >= 0; m--) {\n      const measurement = measurements[m]!\n\n      if (furthestMeasurementsFound.has(measurement.lane)) {\n        continue\n      }\n\n      const previousFurthestMeasurement = furthestMeasurements.get(\n        measurement.lane,\n      )\n      if (\n        previousFurthestMeasurement == null ||\n        measurement.end > previousFurthestMeasurement.end\n      ) {\n        furthestMeasurements.set(measurement.lane, measurement)\n      } else if (measurement.end < previousFurthestMeasurement.end) {\n        furthestMeasurementsFound.set(measurement.lane, true)\n      }\n\n      if (furthestMeasurementsFound.size === this.options.lanes) {\n        break\n      }\n    }\n\n    return furthestMeasurements.size === this.options.lanes\n      ? Array.from(furthestMeasurements.values()).sort((a, b) => {\n          if (a.end === b.end) {\n            return a.index - b.index\n          }\n\n          return a.end - b.end\n        })[0]\n      : undefined\n  }\n\n  private getMeasurementOptions = memo(\n    () => [\n      this.options.count,\n      this.options.paddingStart,\n      this.options.scrollMargin,\n      this.options.getItemKey,\n      this.options.enabled,\n    ],\n    (count, paddingStart, scrollMargin, getItemKey, enabled) => {\n      this.pendingMeasuredCacheIndexes = []\n      return {\n        count,\n        paddingStart,\n        scrollMargin,\n        getItemKey,\n        enabled,\n      }\n    },\n    {\n      key: false,\n    },\n  )\n\n  private getMeasurements = memo(\n    () => [this.getMeasurementOptions(), this.itemSizeCache],\n    (\n      { count, paddingStart, scrollMargin, getItemKey, enabled },\n      itemSizeCache,\n    ) => {\n      if (!enabled) {\n        this.measurementsCache = []\n        this.itemSizeCache.clear()\n        return []\n      }\n\n      if (this.measurementsCache.length === 0) {\n        this.measurementsCache = this.options.initialMeasurementsCache\n        this.measurementsCache.forEach((item) => {\n          this.itemSizeCache.set(item.key, item.size)\n        })\n      }\n\n      const min =\n        this.pendingMeasuredCacheIndexes.length > 0\n          ? Math.min(...this.pendingMeasuredCacheIndexes)\n          : 0\n      this.pendingMeasuredCacheIndexes = []\n\n      const measurements = this.measurementsCache.slice(0, min)\n\n      for (let i = min; i < count; i++) {\n        const key = getItemKey(i)\n\n        const furthestMeasurement =\n          this.options.lanes === 1\n            ? measurements[i - 1]\n            : this.getFurthestMeasurement(measurements, i)\n\n        const start = furthestMeasurement\n          ? furthestMeasurement.end + this.options.gap\n          : paddingStart + scrollMargin\n\n        const measuredSize = itemSizeCache.get(key)\n        const size =\n          typeof measuredSize === 'number'\n            ? measuredSize\n            : this.options.estimateSize(i)\n\n        const end = start + size\n\n        const lane = furthestMeasurement\n          ? furthestMeasurement.lane\n          : i % this.options.lanes\n\n        measurements[i] = {\n          index: i,\n          start,\n          size,\n          end,\n          key,\n          lane,\n        }\n      }\n\n      this.measurementsCache = measurements\n\n      return measurements\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'getMeasurements',\n      debug: () => this.options.debug,\n    },\n  )\n\n  calculateRange = memo(\n    () => [\n      this.getMeasurements(),\n      this.getSize(),\n      this.getScrollOffset(),\n      this.options.lanes,\n    ],\n    (measurements, outerSize, scrollOffset, lanes) => {\n      return (this.range =\n        measurements.length > 0 && outerSize > 0\n          ? calculateRange({\n              measurements,\n              outerSize,\n              scrollOffset,\n              lanes,\n            })\n          : null)\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'calculateRange',\n      debug: () => this.options.debug,\n    },\n  )\n\n  getVirtualIndexes = memo(\n    () => {\n      let startIndex: number | null = null\n      let endIndex: number | null = null\n      const range = this.calculateRange()\n      if (range) {\n        startIndex = range.startIndex\n        endIndex = range.endIndex\n      }\n      this.maybeNotify.updateDeps([this.isScrolling, startIndex, endIndex])\n      return [\n        this.options.rangeExtractor,\n        this.options.overscan,\n        this.options.count,\n        startIndex,\n        endIndex,\n      ]\n    },\n    (rangeExtractor, overscan, count, startIndex, endIndex) => {\n      return startIndex === null || endIndex === null\n        ? []\n        : rangeExtractor({\n            startIndex,\n            endIndex,\n            overscan,\n            count,\n          })\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'getVirtualIndexes',\n      debug: () => this.options.debug,\n    },\n  )\n\n  indexFromElement = (node: TItemElement) => {\n    const attributeName = this.options.indexAttribute\n    const indexStr = node.getAttribute(attributeName)\n\n    if (!indexStr) {\n      console.warn(\n        `Missing attribute name '${attributeName}={index}' on measured element.`,\n      )\n      return -1\n    }\n\n    return parseInt(indexStr, 10)\n  }\n\n  private _measureElement = (\n    node: TItemElement,\n    entry: ResizeObserverEntry | undefined,\n  ) => {\n    const index = this.indexFromElement(node)\n    const item = this.measurementsCache[index]\n    if (!item) {\n      return\n    }\n    const key = item.key\n    const prevNode = this.elementsCache.get(key)\n\n    if (prevNode !== node) {\n      if (prevNode) {\n        this.observer.unobserve(prevNode)\n      }\n      this.observer.observe(node)\n      this.elementsCache.set(key, node)\n    }\n\n    if (node.isConnected) {\n      this.resizeItem(index, this.options.measureElement(node, entry, this))\n    }\n  }\n\n  resizeItem = (index: number, size: number) => {\n    const item = this.measurementsCache[index]\n    if (!item) {\n      return\n    }\n    const itemSize = this.itemSizeCache.get(item.key) ?? item.size\n    const delta = size - itemSize\n\n    if (delta !== 0) {\n      if (\n        this.shouldAdjustScrollPositionOnItemSizeChange !== undefined\n          ? this.shouldAdjustScrollPositionOnItemSizeChange(item, delta, this)\n          : this.scrollDirection === 'backward' &&\n            item.start < this.getScrollOffset() + this.scrollAdjustments\n      ) {\n        if (process.env.NODE_ENV !== 'production' && this.options.debug) {\n          console.info('correction', delta)\n        }\n\n        this._scrollToOffset(this.getScrollOffset(), {\n          adjustments: (this.scrollAdjustments += delta),\n          behavior: undefined,\n        })\n      }\n\n      this.pendingMeasuredCacheIndexes.push(item.index)\n      this.itemSizeCache = new Map(this.itemSizeCache.set(item.key, size))\n\n      this.notify(false)\n    }\n  }\n\n  measureElement = (node: TItemElement | null | undefined) => {\n    if (!node) {\n      this.elementsCache.forEach((cached, key) => {\n        if (!cached.isConnected) {\n          this.observer.unobserve(cached)\n          this.elementsCache.delete(key)\n        }\n      })\n      return\n    }\n\n    this._measureElement(node, undefined)\n  }\n\n  getVirtualItems = memo(\n    () => [this.getVirtualIndexes(), this.getMeasurements()],\n    (indexes, measurements) => {\n      const virtualItems: Array<VirtualItem> = []\n\n      for (let k = 0, len = indexes.length; k < len; k++) {\n        const i = indexes[k]!\n        const measurement = measurements[i]!\n\n        virtualItems.push(measurement)\n      }\n\n      return virtualItems\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'getVirtualItems',\n      debug: () => this.options.debug,\n    },\n  )\n\n  getVirtualItemForOffset = (offset: number) => {\n    const measurements = this.getMeasurements()\n    if (measurements.length === 0) {\n      return undefined\n    }\n    return notUndefined(\n      measurements[\n        findNearestBinarySearch(\n          0,\n          measurements.length - 1,\n          (index: number) => notUndefined(measurements[index]).start,\n          offset,\n        )\n      ],\n    )\n  }\n\n  getOffsetForAlignment = (\n    toOffset: number,\n    align: ScrollAlignment,\n    itemSize = 0,\n  ) => {\n    const size = this.getSize()\n    const scrollOffset = this.getScrollOffset()\n\n    if (align === 'auto') {\n      align = toOffset >= scrollOffset + size ? 'end' : 'start'\n    }\n\n    if (align === 'center') {\n      // When aligning to a particular item (e.g. with scrollToIndex),\n      // adjust offset by the size of the item to center on the item\n      toOffset += (itemSize - size) / 2\n    } else if (align === 'end') {\n      toOffset -= size\n    }\n\n    const maxOffset = this.getTotalSize() - size\n\n    return Math.max(Math.min(maxOffset, toOffset), 0)\n  }\n\n  getOffsetForIndex = (index: number, align: ScrollAlignment = 'auto') => {\n    index = Math.max(0, Math.min(index, this.options.count - 1))\n\n    const item = this.measurementsCache[index]\n    if (!item) {\n      return undefined\n    }\n\n    const size = this.getSize()\n    const scrollOffset = this.getScrollOffset()\n\n    if (align === 'auto') {\n      if (item.end >= scrollOffset + size - this.options.scrollPaddingEnd) {\n        align = 'end'\n      } else if (item.start <= scrollOffset + this.options.scrollPaddingStart) {\n        align = 'start'\n      } else {\n        return [scrollOffset, align] as const\n      }\n    }\n\n    const toOffset =\n      align === 'end'\n        ? item.end + this.options.scrollPaddingEnd\n        : item.start - this.options.scrollPaddingStart\n\n    return [\n      this.getOffsetForAlignment(toOffset, align, item.size),\n      align,\n    ] as const\n  }\n\n  private isDynamicMode = () => this.elementsCache.size > 0\n\n  private cancelScrollToIndex = () => {\n    if (this.scrollToIndexTimeoutId !== null && this.targetWindow) {\n      this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId)\n      this.scrollToIndexTimeoutId = null\n    }\n  }\n\n  scrollToOffset = (\n    toOffset: number,\n    { align = 'start', behavior }: ScrollToOffsetOptions = {},\n  ) => {\n    this.cancelScrollToIndex()\n\n    if (behavior === 'smooth' && this.isDynamicMode()) {\n      console.warn(\n        'The `smooth` scroll behavior is not fully supported with dynamic size.',\n      )\n    }\n\n    this._scrollToOffset(this.getOffsetForAlignment(toOffset, align), {\n      adjustments: undefined,\n      behavior,\n    })\n  }\n\n  scrollToIndex = (\n    index: number,\n    { align: initialAlign = 'auto', behavior }: ScrollToIndexOptions = {},\n  ) => {\n    index = Math.max(0, Math.min(index, this.options.count - 1))\n\n    this.cancelScrollToIndex()\n\n    if (behavior === 'smooth' && this.isDynamicMode()) {\n      console.warn(\n        'The `smooth` scroll behavior is not fully supported with dynamic size.',\n      )\n    }\n\n    const offsetAndAlign = this.getOffsetForIndex(index, initialAlign)\n    if (!offsetAndAlign) return\n\n    const [offset, align] = offsetAndAlign\n\n    this._scrollToOffset(offset, { adjustments: undefined, behavior })\n\n    if (behavior !== 'smooth' && this.isDynamicMode() && this.targetWindow) {\n      this.scrollToIndexTimeoutId = this.targetWindow.setTimeout(() => {\n        this.scrollToIndexTimeoutId = null\n\n        const elementInDOM = this.elementsCache.has(\n          this.options.getItemKey(index),\n        )\n\n        if (elementInDOM) {\n          const result = this.getOffsetForIndex(index, align)\n          if (!result) return\n          const [latestOffset] = result\n\n          const currentScrollOffset = this.getScrollOffset()\n          if (!approxEqual(latestOffset, currentScrollOffset)) {\n            this.scrollToIndex(index, { align, behavior })\n          }\n        } else {\n          this.scrollToIndex(index, { align, behavior })\n        }\n      })\n    }\n  }\n\n  scrollBy = (delta: number, { behavior }: ScrollToOffsetOptions = {}) => {\n    this.cancelScrollToIndex()\n\n    if (behavior === 'smooth' && this.isDynamicMode()) {\n      console.warn(\n        'The `smooth` scroll behavior is not fully supported with dynamic size.',\n      )\n    }\n\n    this._scrollToOffset(this.getScrollOffset() + delta, {\n      adjustments: undefined,\n      behavior,\n    })\n  }\n\n  getTotalSize = () => {\n    const measurements = this.getMeasurements()\n\n    let end: number\n    // If there are no measurements, set the end to paddingStart\n    // If there is only one lane, use the last measurement's end\n    // Otherwise find the maximum end value among all measurements\n    if (measurements.length === 0) {\n      end = this.options.paddingStart\n    } else if (this.options.lanes === 1) {\n      end = measurements[measurements.length - 1]?.end ?? 0\n    } else {\n      const endByLane = Array<number | null>(this.options.lanes).fill(null)\n      let endIndex = measurements.length - 1\n      while (endIndex >= 0 && endByLane.some((val) => val === null)) {\n        const item = measurements[endIndex]!\n        if (endByLane[item.lane] === null) {\n          endByLane[item.lane] = item.end\n        }\n\n        endIndex--\n      }\n\n      end = Math.max(...endByLane.filter((val): val is number => val !== null))\n    }\n\n    return Math.max(\n      end - this.options.scrollMargin + this.options.paddingEnd,\n      0,\n    )\n  }\n\n  private _scrollToOffset = (\n    offset: number,\n    {\n      adjustments,\n      behavior,\n    }: {\n      adjustments: number | undefined\n      behavior: ScrollBehavior | undefined\n    },\n  ) => {\n    this.options.scrollToFn(offset, { behavior, adjustments }, this)\n  }\n\n  measure = () => {\n    this.itemSizeCache = new Map()\n    this.notify(false)\n  }\n}\n\nconst findNearestBinarySearch = (\n  low: number,\n  high: number,\n  getCurrentValue: (i: number) => number,\n  value: number,\n) => {\n  while (low <= high) {\n    const middle = ((low + high) / 2) | 0\n    const currentValue = getCurrentValue(middle)\n\n    if (currentValue < value) {\n      low = middle + 1\n    } else if (currentValue > value) {\n      high = middle - 1\n    } else {\n      return middle\n    }\n  }\n\n  if (low > 0) {\n    return low - 1\n  } else {\n    return 0\n  }\n}\n\nfunction calculateRange({\n  measurements,\n  outerSize,\n  scrollOffset,\n  lanes,\n}: {\n  measurements: Array<VirtualItem>\n  outerSize: number\n  scrollOffset: number\n  lanes: number\n}) {\n  const lastIndex = measurements.length - 1\n  const getOffset = (index: number) => measurements[index]!.start\n\n  // handle case when item count is less than or equal to lanes\n  if (measurements.length <= lanes) {\n    return {\n      startIndex: 0,\n      endIndex: lastIndex,\n    }\n  }\n\n  let startIndex = findNearestBinarySearch(\n    0,\n    lastIndex,\n    getOffset,\n    scrollOffset,\n  )\n  let endIndex = startIndex\n\n  if (lanes === 1) {\n    while (\n      endIndex < lastIndex &&\n      measurements[endIndex]!.end < scrollOffset + outerSize\n    ) {\n      endIndex++\n    }\n  } else if (lanes > 1) {\n    // Expand forward until we include the visible items from all lanes\n    // which are closer to the end of the virtualizer window\n    const endPerLane = Array(lanes).fill(0)\n    while (\n      endIndex < lastIndex &&\n      endPerLane.some((pos) => pos < scrollOffset + outerSize)\n    ) {\n      const item = measurements[endIndex]!\n      endPerLane[item.lane] = item.end\n      endIndex++\n    }\n\n    // Expand backward until we include all lanes' visible items\n    // closer to the top\n    const startPerLane = Array(lanes).fill(scrollOffset + outerSize)\n    while (startIndex >= 0 && startPerLane.some((pos) => pos >= scrollOffset)) {\n      const item = measurements[startIndex]!\n      startPerLane[item.lane] = item.start\n      startIndex--\n    }\n\n    // Align startIndex to the beginning of its lane\n    startIndex = Math.max(0, startIndex - (startIndex % lanes))\n    // Align endIndex to the end of its lane\n    endIndex = Math.min(lastIndex, endIndex + (lanes - 1 - (endIndex % lanes)))\n  }\n\n  return { startIndex, endIndex }\n}\n", "import * as React from 'react'\nimport { flushSync } from 'react-dom'\nimport {\n  Virtualizer,\n  elementScroll,\n  observeElementOffset,\n  observeElementRect,\n  observeWindowOffset,\n  observeWindowRect,\n  windowScroll,\n} from '@tanstack/virtual-core'\nimport type { PartialKeys, VirtualizerOptions } from '@tanstack/virtual-core'\n\nexport * from '@tanstack/virtual-core'\n\nconst useIsomorphicLayoutEffect =\n  typeof document !== 'undefined' ? React.useLayoutEffect : React.useEffect\n\nfunction useVirtualizerBase<\n  TScrollElement extends Element | Window,\n  TItemElement extends Element,\n>(\n  options: VirtualizerOptions<TScrollElement, TItemElement>,\n): Virtualizer<TScrollElement, TItemElement> {\n  const rerender = React.useReducer(() => ({}), {})[1]\n\n  const resolvedOptions: VirtualizerOptions<TScrollElement, TItemElement> = {\n    ...options,\n    onChange: (instance, sync) => {\n      if (sync) {\n        flushSync(rerender)\n      } else {\n        rerender()\n      }\n      options.onChange?.(instance, sync)\n    },\n  }\n\n  const [instance] = React.useState(\n    () => new Virtualizer<TScrollElement, TItemElement>(resolvedOptions),\n  )\n\n  instance.setOptions(resolvedOptions)\n\n  useIsomorphicLayoutEffect(() => {\n    return instance._didMount()\n  }, [])\n\n  useIsomorphicLayoutEffect(() => {\n    return instance._willUpdate()\n  })\n\n  return instance\n}\n\nexport function useVirtualizer<\n  TScrollElement extends Element,\n  TItemElement extends Element,\n>(\n  options: PartialKeys<\n    VirtualizerOptions<TScrollElement, TItemElement>,\n    'observeElementRect' | 'observeElementOffset' | 'scrollToFn'\n  >,\n): Virtualizer<TScrollElement, TItemElement> {\n  return useVirtualizerBase<TScrollElement, TItemElement>({\n    observeElementRect: observeElementRect,\n    observeElementOffset: observeElementOffset,\n    scrollToFn: elementScroll,\n    ...options,\n  })\n}\n\nexport function useWindowVirtualizer<TItemElement extends Element>(\n  options: PartialKeys<\n    VirtualizerOptions<Window, TItemElement>,\n    | 'getScrollElement'\n    | 'observeElementRect'\n    | 'observeElementOffset'\n    | 'scrollToFn'\n  >,\n): Virtualizer<Window, TItemElement> {\n  return useVirtualizerBase<Window, TItemElement>({\n    getScrollElement: () => (typeof document !== 'undefined' ? window : null),\n    observeElementRect: observeWindowRect,\n    observeElementOffset: observeWindowOffset,\n    scrollToFn: windowScroll,\n    initialOffset: () => (typeof document !== 'undefined' ? window.scrollY : 0),\n    ...options,\n  })\n}\n", "import{useVirtualizer as Ee}from\"@tanstack/react-virtual\";import w,{createContext as ie,createRef as P<PERSON>,Fragment as me,use<PERSON><PERSON>back as Ie,useContext as ue,useEffect as Ve,useMemo as U,useReducer as _e,useRef as B,useState as Fe}from\"react\";import{useComputed as pe}from'../../hooks/use-computed.js';import{useControllable as Le}from'../../hooks/use-controllable.js';import{useDisposables as se}from'../../hooks/use-disposables.js';import{useEvent as m}from'../../hooks/use-event.js';import{useId as Q}from'../../hooks/use-id.js';import{useIsoMorphicEffect as H}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as De}from'../../hooks/use-latest-value.js';import{useOutsideClick as Me}from'../../hooks/use-outside-click.js';import{useOwnerDocument as he}from'../../hooks/use-owner.js';import{useResolveButtonType as Be}from'../../hooks/use-resolve-button-type.js';import{useSyncRefs as Z}from'../../hooks/use-sync-refs.js';import{useTrackedPointer as ke}from'../../hooks/use-tracked-pointer.js';import{useTreeWalker as we}from'../../hooks/use-tree-walker.js';import{useWatch as Te}from'../../hooks/use-watch.js';import{Features as Ue,Hidden as He}from'../../internal/hidden.js';import{OpenClosedProvider as Ne,State as re,useOpenClosed as Ge}from'../../internal/open-closed.js';import{history as xe}from'../../utils/active-element-history.js';import{isDisabledReactIssue7711 as Xe}from'../../utils/bugs.js';import{calculateActiveIndex as ge,Focus as y}from'../../utils/calculate-active-index.js';import{disposables as ve}from'../../utils/disposables.js';import{sortByDomNode as je}from'../../utils/focus-management.js';import{objectToFormEntries as Je}from'../../utils/form.js';import{match as W}from'../../utils/match.js';import{isMobile as Ke}from'../../utils/platform.js';import{compact as We,Features as Oe,forwardRefWithAs as $,render as q}from'../../utils/render.js';import{Keys as M}from'../keyboard.js';var $e=(o=>(o[o.Open=0]=\"Open\",o[o.Closed=1]=\"Closed\",o))($e||{}),qe=(o=>(o[o.Single=0]=\"Single\",o[o.Multi=1]=\"Multi\",o))(qe||{}),ze=(a=>(a[a.Pointer=0]=\"Pointer\",a[a.Focus=1]=\"Focus\",a[a.Other=2]=\"Other\",a))(ze||{}),Ye=(e=>(e[e.OpenCombobox=0]=\"OpenCombobox\",e[e.CloseCombobox=1]=\"CloseCombobox\",e[e.GoToOption=2]=\"GoToOption\",e[e.RegisterOption=3]=\"RegisterOption\",e[e.UnregisterOption=4]=\"UnregisterOption\",e[e.RegisterLabel=5]=\"RegisterLabel\",e[e.SetActivationTrigger=6]=\"SetActivationTrigger\",e[e.UpdateVirtualOptions=7]=\"UpdateVirtualOptions\",e))(Ye||{});function de(t,r=o=>o){let o=t.activeOptionIndex!==null?t.options[t.activeOptionIndex]:null,a=r(t.options.slice()),i=a.length>0&&a[0].dataRef.current.order!==null?a.sort((p,c)=>p.dataRef.current.order-c.dataRef.current.order):je(a,p=>p.dataRef.current.domRef.current),u=o?i.indexOf(o):null;return u===-1&&(u=null),{options:i,activeOptionIndex:u}}let Qe={[1](t){var r;return(r=t.dataRef.current)!=null&&r.disabled||t.comboboxState===1?t:{...t,activeOptionIndex:null,comboboxState:1}},[0](t){var r,o;if((r=t.dataRef.current)!=null&&r.disabled||t.comboboxState===0)return t;if((o=t.dataRef.current)!=null&&o.value){let a=t.dataRef.current.calculateIndex(t.dataRef.current.value);if(a!==-1)return{...t,activeOptionIndex:a,comboboxState:0}}return{...t,comboboxState:0}},[2](t,r){var u,p,c,e,l;if((u=t.dataRef.current)!=null&&u.disabled||(p=t.dataRef.current)!=null&&p.optionsRef.current&&!((c=t.dataRef.current)!=null&&c.optionsPropsRef.current.static)&&t.comboboxState===1)return t;if(t.virtual){let T=r.focus===y.Specific?r.idx:ge(r,{resolveItems:()=>t.virtual.options,resolveActiveIndex:()=>{var f,v;return(v=(f=t.activeOptionIndex)!=null?f:t.virtual.options.findIndex(S=>!t.virtual.disabled(S)))!=null?v:null},resolveDisabled:t.virtual.disabled,resolveId(){throw new Error(\"Function not implemented.\")}}),g=(e=r.trigger)!=null?e:2;return t.activeOptionIndex===T&&t.activationTrigger===g?t:{...t,activeOptionIndex:T,activationTrigger:g}}let o=de(t);if(o.activeOptionIndex===null){let T=o.options.findIndex(g=>!g.dataRef.current.disabled);T!==-1&&(o.activeOptionIndex=T)}let a=r.focus===y.Specific?r.idx:ge(r,{resolveItems:()=>o.options,resolveActiveIndex:()=>o.activeOptionIndex,resolveId:T=>T.id,resolveDisabled:T=>T.dataRef.current.disabled}),i=(l=r.trigger)!=null?l:2;return t.activeOptionIndex===a&&t.activationTrigger===i?t:{...t,...o,activeOptionIndex:a,activationTrigger:i}},[3]:(t,r)=>{var u,p,c;if((u=t.dataRef.current)!=null&&u.virtual)return{...t,options:[...t.options,r.payload]};let o=r.payload,a=de(t,e=>(e.push(o),e));t.activeOptionIndex===null&&(p=t.dataRef.current)!=null&&p.isSelected(r.payload.dataRef.current.value)&&(a.activeOptionIndex=a.options.indexOf(o));let i={...t,...a,activationTrigger:2};return(c=t.dataRef.current)!=null&&c.__demoMode&&t.dataRef.current.value===void 0&&(i.activeOptionIndex=0),i},[4]:(t,r)=>{var a;if((a=t.dataRef.current)!=null&&a.virtual)return{...t,options:t.options.filter(i=>i.id!==r.id)};let o=de(t,i=>{let u=i.findIndex(p=>p.id===r.id);return u!==-1&&i.splice(u,1),i});return{...t,...o,activationTrigger:2}},[5]:(t,r)=>t.labelId===r.id?t:{...t,labelId:r.id},[6]:(t,r)=>t.activationTrigger===r.trigger?t:{...t,activationTrigger:r.trigger},[7]:(t,r)=>{var a;if(((a=t.virtual)==null?void 0:a.options)===r.options)return t;let o=t.activeOptionIndex;if(t.activeOptionIndex!==null){let i=r.options.indexOf(t.virtual.options[t.activeOptionIndex]);i!==-1?o=i:o=null}return{...t,activeOptionIndex:o,virtual:Object.assign({},t.virtual,{options:r.options})}}},be=ie(null);be.displayName=\"ComboboxActionsContext\";function ee(t){let r=ue(be);if(r===null){let o=new Error(`<${t} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,ee),o}return r}let Ce=ie(null);function Ze(t){var c;let r=j(\"VirtualProvider\"),[o,a]=U(()=>{let e=r.optionsRef.current;if(!e)return[0,0];let l=window.getComputedStyle(e);return[parseFloat(l.paddingBlockStart||l.paddingTop),parseFloat(l.paddingBlockEnd||l.paddingBottom)]},[r.optionsRef.current]),i=Ee({scrollPaddingStart:o,scrollPaddingEnd:a,count:r.virtual.options.length,estimateSize(){return 40},getScrollElement(){var e;return(e=r.optionsRef.current)!=null?e:null},overscan:12}),[u,p]=Fe(0);return H(()=>{p(e=>e+1)},[(c=r.virtual)==null?void 0:c.options]),w.createElement(Ce.Provider,{value:i},w.createElement(\"div\",{style:{position:\"relative\",width:\"100%\",height:`${i.getTotalSize()}px`},ref:e=>{if(e){if(typeof process!=\"undefined\"&&process.env.JEST_WORKER_ID!==void 0||r.activationTrigger===0)return;r.activeOptionIndex!==null&&r.virtual.options.length>r.activeOptionIndex&&i.scrollToIndex(r.activeOptionIndex)}}},i.getVirtualItems().map(e=>{var l;return w.createElement(me,{key:e.key},w.cloneElement((l=t.children)==null?void 0:l.call(t,{option:r.virtual.options[e.index],open:r.comboboxState===0}),{key:`${u}-${e.key}`,\"data-index\":e.index,\"aria-setsize\":r.virtual.options.length,\"aria-posinset\":e.index+1,style:{position:\"absolute\",top:0,left:0,transform:`translateY(${e.start}px)`,overflowAnchor:\"none\"}}))})))}let ce=ie(null);ce.displayName=\"ComboboxDataContext\";function j(t){let r=ue(ce);if(r===null){let o=new Error(`<${t} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,j),o}return r}function et(t,r){return W(r.type,Qe,t,r)}let tt=me;function ot(t,r){var fe;let{value:o,defaultValue:a,onChange:i,form:u,name:p,by:c=null,disabled:e=!1,__demoMode:l=!1,nullable:T=!1,multiple:g=!1,immediate:f=!1,virtual:v=null,...S}=t,R=!1,s=null,[I=g?[]:void 0,V]=Le(o,i,a),[_,E]=_e(et,{dataRef:Pe(),comboboxState:l?0:1,options:[],virtual:s?{options:s.options,disabled:(fe=s.disabled)!=null?fe:()=>!1}:null,activeOptionIndex:null,activationTrigger:2,labelId:null}),k=B(!1),J=B({static:!1,hold:!1}),K=B(null),z=B(null),te=B(null),X=B(null),x=m(typeof c==\"string\"?(d,b)=>{let P=c;return(d==null?void 0:d[P])===(b==null?void 0:b[P])}:c!=null?c:(d,b)=>d===b),O=m(d=>s?c===null?s.options.indexOf(d):s.options.findIndex(b=>x(b,d)):_.options.findIndex(b=>x(b.dataRef.current.value,d))),L=Ie(d=>W(n.mode,{[1]:()=>I.some(b=>x(b,d)),[0]:()=>x(I,d)}),[I]),oe=m(d=>_.activeOptionIndex===O(d)),n=U(()=>({..._,immediate:R,optionsPropsRef:J,labelRef:K,inputRef:z,buttonRef:te,optionsRef:X,value:I,defaultValue:a,disabled:e,mode:g?1:0,virtual:_.virtual,get activeOptionIndex(){if(k.current&&_.activeOptionIndex===null&&(s?s.options.length>0:_.options.length>0)){if(s){let b=s.options.findIndex(P=>{var G,Y;return!((Y=(G=s==null?void 0:s.disabled)==null?void 0:G.call(s,P))!=null&&Y)});if(b!==-1)return b}let d=_.options.findIndex(b=>!b.dataRef.current.disabled);if(d!==-1)return d}return _.activeOptionIndex},calculateIndex:O,compare:x,isSelected:L,isActive:oe,nullable:T,__demoMode:l}),[I,a,e,g,T,l,_,s]);H(()=>{s&&E({type:7,options:s.options})},[s,s==null?void 0:s.options]),H(()=>{_.dataRef.current=n},[n]),Me([n.buttonRef,n.inputRef,n.optionsRef],()=>le.closeCombobox(),n.comboboxState===0);let F=U(()=>{var d,b,P;return{open:n.comboboxState===0,disabled:e,activeIndex:n.activeOptionIndex,activeOption:n.activeOptionIndex===null?null:n.virtual?n.virtual.options[(d=n.activeOptionIndex)!=null?d:0]:(P=(b=n.options[n.activeOptionIndex])==null?void 0:b.dataRef.current.value)!=null?P:null,value:I}},[n,e,I]),A=m(()=>{if(n.activeOptionIndex!==null){if(n.virtual)ae(n.virtual.options[n.activeOptionIndex]);else{let{dataRef:d}=n.options[n.activeOptionIndex];ae(d.current.value)}le.goToOption(y.Specific,n.activeOptionIndex)}}),h=m(()=>{E({type:0}),k.current=!0}),C=m(()=>{E({type:1}),k.current=!1}),D=m((d,b,P)=>(k.current=!1,d===y.Specific?E({type:2,focus:y.Specific,idx:b,trigger:P}):E({type:2,focus:d,trigger:P}))),N=m((d,b)=>(E({type:3,payload:{id:d,dataRef:b}}),()=>{n.isActive(b.current.value)&&(k.current=!0),E({type:4,id:d})})),ye=m(d=>(E({type:5,id:d}),()=>E({type:5,id:null}))),ae=m(d=>W(n.mode,{[0](){return V==null?void 0:V(d)},[1](){let b=n.value.slice(),P=b.findIndex(G=>x(G,d));return P===-1?b.push(d):b.splice(P,1),V==null?void 0:V(b)}})),Re=m(d=>{E({type:6,trigger:d})}),le=U(()=>({onChange:ae,registerOption:N,registerLabel:ye,goToOption:D,closeCombobox:C,openCombobox:h,setActivationTrigger:Re,selectActiveOption:A}),[]),Ae=r===null?{}:{ref:r},ne=B(null),Se=se();return Ve(()=>{ne.current&&a!==void 0&&Se.addEventListener(ne.current,\"reset\",()=>{V==null||V(a)})},[ne,V]),w.createElement(be.Provider,{value:le},w.createElement(ce.Provider,{value:n},w.createElement(Ne,{value:W(n.comboboxState,{[0]:re.Open,[1]:re.Closed})},p!=null&&I!=null&&Je({[p]:I}).map(([d,b],P)=>w.createElement(He,{features:Ue.Hidden,ref:P===0?G=>{var Y;ne.current=(Y=G==null?void 0:G.closest(\"form\"))!=null?Y:null}:void 0,...We({key:d,as:\"input\",type:\"hidden\",hidden:!0,readOnly:!0,form:u,disabled:e,name:d,value:b})})),q({ourProps:Ae,theirProps:S,slot:F,defaultTag:tt,name:\"Combobox\"}))))}let nt=\"input\";function rt(t,r){var X,x,O,L,oe;let o=Q(),{id:a=`headlessui-combobox-input-${o}`,onChange:i,displayValue:u,type:p=\"text\",...c}=t,e=j(\"Combobox.Input\"),l=ee(\"Combobox.Input\"),T=Z(e.inputRef,r),g=he(e.inputRef),f=B(!1),v=se(),S=m(()=>{l.onChange(null),e.optionsRef.current&&(e.optionsRef.current.scrollTop=0),l.goToOption(y.Nothing)}),R=function(){var n;return typeof u==\"function\"&&e.value!==void 0?(n=u(e.value))!=null?n:\"\":typeof e.value==\"string\"?e.value:\"\"}();Te(([n,F],[A,h])=>{if(f.current)return;let C=e.inputRef.current;C&&((h===0&&F===1||n!==A)&&(C.value=n),requestAnimationFrame(()=>{if(f.current||!C||(g==null?void 0:g.activeElement)!==C)return;let{selectionStart:D,selectionEnd:N}=C;Math.abs((N!=null?N:0)-(D!=null?D:0))===0&&D===0&&C.setSelectionRange(C.value.length,C.value.length)}))},[R,e.comboboxState,g]),Te(([n],[F])=>{if(n===0&&F===1){if(f.current)return;let A=e.inputRef.current;if(!A)return;let h=A.value,{selectionStart:C,selectionEnd:D,selectionDirection:N}=A;A.value=\"\",A.value=h,N!==null?A.setSelectionRange(C,D,N):A.setSelectionRange(C,D)}},[e.comboboxState]);let s=B(!1),I=m(()=>{s.current=!0}),V=m(()=>{v.nextFrame(()=>{s.current=!1})}),_=m(n=>{switch(f.current=!0,n.key){case M.Enter:if(f.current=!1,e.comboboxState!==0||s.current)return;if(n.preventDefault(),n.stopPropagation(),e.activeOptionIndex===null){l.closeCombobox();return}l.selectActiveOption(),e.mode===0&&l.closeCombobox();break;case M.ArrowDown:return f.current=!1,n.preventDefault(),n.stopPropagation(),W(e.comboboxState,{[0]:()=>l.goToOption(y.Next),[1]:()=>l.openCombobox()});case M.ArrowUp:return f.current=!1,n.preventDefault(),n.stopPropagation(),W(e.comboboxState,{[0]:()=>l.goToOption(y.Previous),[1]:()=>{l.openCombobox(),v.nextFrame(()=>{e.value||l.goToOption(y.Last)})}});case M.Home:if(n.shiftKey)break;return f.current=!1,n.preventDefault(),n.stopPropagation(),l.goToOption(y.First);case M.PageUp:return f.current=!1,n.preventDefault(),n.stopPropagation(),l.goToOption(y.First);case M.End:if(n.shiftKey)break;return f.current=!1,n.preventDefault(),n.stopPropagation(),l.goToOption(y.Last);case M.PageDown:return f.current=!1,n.preventDefault(),n.stopPropagation(),l.goToOption(y.Last);case M.Escape:return f.current=!1,e.comboboxState!==0?void 0:(n.preventDefault(),e.optionsRef.current&&!e.optionsPropsRef.current.static&&n.stopPropagation(),e.nullable&&e.mode===0&&e.value===null&&S(),l.closeCombobox());case M.Tab:if(f.current=!1,e.comboboxState!==0)return;e.mode===0&&e.activationTrigger!==1&&l.selectActiveOption(),l.closeCombobox();break}}),E=m(n=>{i==null||i(n),e.nullable&&e.mode===0&&n.target.value===\"\"&&S(),l.openCombobox()}),k=m(n=>{var A,h,C;let F=(A=n.relatedTarget)!=null?A:xe.find(D=>D!==n.currentTarget);if(f.current=!1,!((h=e.optionsRef.current)!=null&&h.contains(F))&&!((C=e.buttonRef.current)!=null&&C.contains(F))&&e.comboboxState===0)return n.preventDefault(),e.mode===0&&(e.nullable&&e.value===null?S():e.activationTrigger!==1&&l.selectActiveOption()),l.closeCombobox()}),J=m(n=>{var A,h,C;let F=(A=n.relatedTarget)!=null?A:xe.find(D=>D!==n.currentTarget);(h=e.buttonRef.current)!=null&&h.contains(F)||(C=e.optionsRef.current)!=null&&C.contains(F)||e.disabled||e.immediate&&e.comboboxState!==0&&(l.openCombobox(),v.nextFrame(()=>{l.setActivationTrigger(1)}))}),K=pe(()=>{if(e.labelId)return[e.labelId].join(\" \")},[e.labelId]),z=U(()=>({open:e.comboboxState===0,disabled:e.disabled}),[e]),te={ref:T,id:a,role:\"combobox\",type:p,\"aria-controls\":(X=e.optionsRef.current)==null?void 0:X.id,\"aria-expanded\":e.comboboxState===0,\"aria-activedescendant\":e.activeOptionIndex===null?void 0:e.virtual?(x=e.options.find(n=>{var F;return!((F=e.virtual)!=null&&F.disabled(n.dataRef.current.value))&&e.compare(n.dataRef.current.value,e.virtual.options[e.activeOptionIndex])}))==null?void 0:x.id:(O=e.options[e.activeOptionIndex])==null?void 0:O.id,\"aria-labelledby\":K,\"aria-autocomplete\":\"list\",defaultValue:(oe=(L=t.defaultValue)!=null?L:e.defaultValue!==void 0?u==null?void 0:u(e.defaultValue):null)!=null?oe:e.defaultValue,disabled:e.disabled,onCompositionStart:I,onCompositionEnd:V,onKeyDown:_,onChange:E,onFocus:J,onBlur:k};return q({ourProps:te,theirProps:c,slot:z,defaultTag:nt,name:\"Combobox.Input\"})}let at=\"button\";function lt(t,r){var S;let o=j(\"Combobox.Button\"),a=ee(\"Combobox.Button\"),i=Z(o.buttonRef,r),u=Q(),{id:p=`headlessui-combobox-button-${u}`,...c}=t,e=se(),l=m(R=>{switch(R.key){case M.ArrowDown:return R.preventDefault(),R.stopPropagation(),o.comboboxState===1&&a.openCombobox(),e.nextFrame(()=>{var s;return(s=o.inputRef.current)==null?void 0:s.focus({preventScroll:!0})});case M.ArrowUp:return R.preventDefault(),R.stopPropagation(),o.comboboxState===1&&(a.openCombobox(),e.nextFrame(()=>{o.value||a.goToOption(y.Last)})),e.nextFrame(()=>{var s;return(s=o.inputRef.current)==null?void 0:s.focus({preventScroll:!0})});case M.Escape:return o.comboboxState!==0?void 0:(R.preventDefault(),o.optionsRef.current&&!o.optionsPropsRef.current.static&&R.stopPropagation(),a.closeCombobox(),e.nextFrame(()=>{var s;return(s=o.inputRef.current)==null?void 0:s.focus({preventScroll:!0})}));default:return}}),T=m(R=>{if(Xe(R.currentTarget))return R.preventDefault();o.comboboxState===0?a.closeCombobox():(R.preventDefault(),a.openCombobox()),e.nextFrame(()=>{var s;return(s=o.inputRef.current)==null?void 0:s.focus({preventScroll:!0})})}),g=pe(()=>{if(o.labelId)return[o.labelId,p].join(\" \")},[o.labelId,p]),f=U(()=>({open:o.comboboxState===0,disabled:o.disabled,value:o.value}),[o]),v={ref:i,id:p,type:Be(t,o.buttonRef),tabIndex:-1,\"aria-haspopup\":\"listbox\",\"aria-controls\":(S=o.optionsRef.current)==null?void 0:S.id,\"aria-expanded\":o.comboboxState===0,\"aria-labelledby\":g,disabled:o.disabled,onClick:T,onKeyDown:l};return q({ourProps:v,theirProps:c,slot:f,defaultTag:at,name:\"Combobox.Button\"})}let it=\"label\";function ut(t,r){let o=Q(),{id:a=`headlessui-combobox-label-${o}`,...i}=t,u=j(\"Combobox.Label\"),p=ee(\"Combobox.Label\"),c=Z(u.labelRef,r);H(()=>p.registerLabel(a),[a]);let e=m(()=>{var g;return(g=u.inputRef.current)==null?void 0:g.focus({preventScroll:!0})}),l=U(()=>({open:u.comboboxState===0,disabled:u.disabled}),[u]);return q({ourProps:{ref:c,id:a,onClick:e},theirProps:i,slot:l,defaultTag:it,name:\"Combobox.Label\"})}let pt=\"ul\",st=Oe.RenderStrategy|Oe.Static;function dt(t,r){let o=Q(),{id:a=`headlessui-combobox-options-${o}`,hold:i=!1,...u}=t,p=j(\"Combobox.Options\"),c=Z(p.optionsRef,r),e=Ge(),l=(()=>e!==null?(e&re.Open)===re.Open:p.comboboxState===0)();H(()=>{var v;p.optionsPropsRef.current.static=(v=t.static)!=null?v:!1},[p.optionsPropsRef,t.static]),H(()=>{p.optionsPropsRef.current.hold=i},[p.optionsPropsRef,i]),we({container:p.optionsRef.current,enabled:p.comboboxState===0,accept(v){return v.getAttribute(\"role\")===\"option\"?NodeFilter.FILTER_REJECT:v.hasAttribute(\"role\")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(v){v.setAttribute(\"role\",\"none\")}});let T=pe(()=>{var v,S;return(S=p.labelId)!=null?S:(v=p.buttonRef.current)==null?void 0:v.id},[p.labelId,p.buttonRef.current]),g=U(()=>({open:p.comboboxState===0,option:void 0}),[p]),f={\"aria-labelledby\":T,role:\"listbox\",\"aria-multiselectable\":p.mode===1?!0:void 0,id:a,ref:c};return p.virtual&&p.comboboxState===0&&Object.assign(u,{children:w.createElement(Ze,null,u.children)}),q({ourProps:f,theirProps:u,slot:g,defaultTag:pt,features:st,visible:l,name:\"Combobox.Options\"})}let bt=\"li\";function ct(t,r){var X;let o=Q(),{id:a=`headlessui-combobox-option-${o}`,disabled:i=!1,value:u,order:p=null,...c}=t,e=j(\"Combobox.Option\"),l=ee(\"Combobox.Option\"),T=e.virtual?e.activeOptionIndex===e.calculateIndex(u):e.activeOptionIndex===null?!1:((X=e.options[e.activeOptionIndex])==null?void 0:X.id)===a,g=e.isSelected(u),f=B(null),v=De({disabled:i,value:u,domRef:f,order:p}),S=ue(Ce),R=Z(r,f,S?S.measureElement:null),s=m(()=>l.onChange(u));H(()=>l.registerOption(a,v),[v,a]);let I=B(!(e.virtual||e.__demoMode));H(()=>{if(!e.virtual||!e.__demoMode)return;let x=ve();return x.requestAnimationFrame(()=>{I.current=!0}),x.dispose},[e.virtual,e.__demoMode]),H(()=>{if(!I.current||e.comboboxState!==0||!T||e.activationTrigger===0)return;let x=ve();return x.requestAnimationFrame(()=>{var O,L;(L=(O=f.current)==null?void 0:O.scrollIntoView)==null||L.call(O,{block:\"nearest\"})}),x.dispose},[f,T,e.comboboxState,e.activationTrigger,e.activeOptionIndex]);let V=m(x=>{var O;if(i||(O=e.virtual)!=null&&O.disabled(u))return x.preventDefault();s(),Ke()||requestAnimationFrame(()=>{var L;return(L=e.inputRef.current)==null?void 0:L.focus({preventScroll:!0})}),e.mode===0&&requestAnimationFrame(()=>l.closeCombobox())}),_=m(()=>{var O;if(i||(O=e.virtual)!=null&&O.disabled(u))return l.goToOption(y.Nothing);let x=e.calculateIndex(u);l.goToOption(y.Specific,x)}),E=ke(),k=m(x=>E.update(x)),J=m(x=>{var L;if(!E.wasMoved(x)||i||(L=e.virtual)!=null&&L.disabled(u)||T)return;let O=e.calculateIndex(u);l.goToOption(y.Specific,O,0)}),K=m(x=>{var O;E.wasMoved(x)&&(i||(O=e.virtual)!=null&&O.disabled(u)||T&&(e.optionsPropsRef.current.hold||l.goToOption(y.Nothing)))}),z=U(()=>({active:T,selected:g,disabled:i}),[T,g,i]);return q({ourProps:{id:a,ref:R,role:\"option\",tabIndex:i===!0?void 0:-1,\"aria-disabled\":i===!0?!0:void 0,\"aria-selected\":g,disabled:void 0,onClick:V,onFocus:_,onPointerEnter:k,onMouseEnter:k,onPointerMove:J,onMouseMove:J,onPointerLeave:K,onMouseLeave:K},theirProps:c,slot:z,defaultTag:bt,name:\"Combobox.Option\"})}let ft=$(ot),mt=$(lt),Tt=$(rt),xt=$(ut),gt=$(dt),vt=$(ct),qt=Object.assign(ft,{Input:Tt,Button:mt,Label:xt,Options:gt,Option:vt});export{qt as Combobox};\n", "import{useState as s}from\"react\";import{useIsoMorphicEffect as f}from'./use-iso-morphic-effect.js';import{useLatestValue as m}from'./use-latest-value.js';function i(e,o){let[u,t]=s(e),r=m(e);return f(()=>t(r.current),[r,t,...o]),u}export{i as useComputed};\n", "import{useEffect as t,useLayoutEffect as c}from\"react\";import{env as i}from'../utils/env.js';let l=(e,f)=>{i.isServer?t(e,f):c(e,f)};export{l as useIsoMorphicEffect};\n", "var i=Object.defineProperty;var d=(t,e,n)=>e in t?i(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var r=(t,e,n)=>(d(t,typeof e!=\"symbol\"?e+\"\":e,n),n);class o{constructor(){r(this,\"current\",this.detect());r(this,\"handoffState\",\"pending\");r(this,\"currentId\",0)}set(e){this.current!==e&&(this.handoffState=\"pending\",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current===\"server\"}get isClient(){return this.current===\"client\"}detect(){return typeof window==\"undefined\"||typeof document==\"undefined\"?\"server\":\"client\"}handoff(){this.handoffState===\"pending\"&&(this.handoffState=\"complete\")}get isHandoffComplete(){return this.handoffState===\"complete\"}}let s=new o;export{s as env};\n", "import{useRef as t}from\"react\";import{useIsoMorphicEffect as o}from'./use-iso-morphic-effect.js';function s(e){let r=t(e);return o(()=>{r.current=e},[e]),r}export{s as useLatestValue};\n", "import{useRef as o,useState as f}from\"react\";import{useEvent as a}from'./use-event.js';function T(l,r,c){let[i,s]=f(c),e=l!==void 0,t=o(e),u=o(!1),d=o(!1);return e&&!t.current&&!u.current?(u.current=!0,t.current=e,console.error(\"A component is changing from uncontrolled to controlled. This may be caused by the value changing from undefined to a defined value, which should not happen.\")):!e&&t.current&&!d.current&&(d.current=!0,t.current=e,console.error(\"A component is changing from controlled to uncontrolled. This may be caused by the value changing from a defined value to undefined, which should not happen.\")),[e?l:i,a(n=>(e||s(n),r==null?void 0:r(n)))]}export{T as useControllable};\n", "import a from\"react\";import{useLatestValue as n}from'./use-latest-value.js';let o=function(t){let e=n(t);return a.useCallback((...r)=>e.current(...r),[e])};export{o as useEvent};\n", "import{useEffect as s,useState as o}from\"react\";import{disposables as t}from'../utils/disposables.js';function p(){let[e]=o(t);return s(()=>()=>e.dispose(),[e]),e}export{p as useDisposables};\n", "function t(e){typeof queueMicrotask==\"function\"?queueMicrotask(e):Promise.resolve().then(e).catch(o=>setTimeout(()=>{throw o}))}export{t as microTask};\n", "import{microTask as i}from'./micro-task.js';function o(){let n=[],r={addEventListener(e,t,s,a){return e.addEventListener(t,s,a),r.add(()=>e.removeEventListener(t,s,a))},requestAnimationFrame(...e){let t=requestAnimationFrame(...e);return r.add(()=>cancelAnimationFrame(t))},nextFrame(...e){return r.requestAnimationFrame(()=>r.requestAnimationFrame(...e))},setTimeout(...e){let t=setTimeout(...e);return r.add(()=>clearTimeout(t))},microTask(...e){let t={current:!0};return i(()=>{t.current&&e[0]()}),r.add(()=>{t.current=!1})},style(e,t,s){let a=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:s}),this.add(()=>{Object.assign(e.style,{[t]:a})})},group(e){let t=o();return e(t),this.add(()=>t.dispose())},add(e){return n.push(e),()=>{let t=n.indexOf(e);if(t>=0)for(let s of n.splice(t,1))s()}},dispose(){for(let e of n.splice(0))e()}};return r}export{o as disposables};\n", "var o;import t from\"react\";import{env as r}from'../utils/env.js';import{useIsoMorphicEffect as d}from'./use-iso-morphic-effect.js';import{useServerHandoffComplete as f}from'./use-server-handoff-complete.js';let I=(o=t.useId)!=null?o:function(){let n=f(),[e,u]=t.useState(n?()=>r.nextId():null);return d(()=>{e===null&&u(r.nextId())},[e]),e!=null?\"\"+e:void 0};export{I as useId};\n", "import*as t from\"react\";import{env as f}from'../utils/env.js';function s(){let r=typeof document==\"undefined\";return\"useSyncExternalStore\"in t?(o=>o.useSyncExternalStore)(t)(()=>()=>{},()=>!1,()=>!r):!1}function l(){let r=s(),[e,n]=t.useState(f.isHandoffComplete);return e&&f.isHandoffComplete===!1&&n(!1),t.useEffect(()=>{e!==!0&&n(!0)},[e]),t.useEffect(()=>f.handoff(),[]),r?!1:e}export{l as useServerHandoffComplete};\n", "import{useEffect as d,useRef as f}from\"react\";import{FocusableMode as p,isFocusableElement as C}from'../utils/focus-management.js';import{isMobile as M}from'../utils/platform.js';import{useDocumentEvent as l}from'./use-document-event.js';import{useWindowEvent as T}from'./use-window-event.js';function y(s,m,a=!0){let i=f(!1);d(()=>{requestAnimationFrame(()=>{i.current=a})},[a]);function c(e,r){if(!i.current||e.defaultPrevented)return;let t=r(e);if(t===null||!t.getRootNode().contains(t)||!t.isConnected)return;let E=function u(n){return typeof n==\"function\"?u(n()):Array.isArray(n)||n instanceof Set?n:[n]}(s);for(let u of E){if(u===null)continue;let n=u instanceof HTMLElement?u:u.current;if(n!=null&&n.contains(t)||e.composed&&e.composedPath().includes(n))return}return!C(t,p.Loose)&&t.tabIndex!==-1&&e.preventDefault(),m(e,t)}let o=f(null);l(\"pointerdown\",e=>{var r,t;i.current&&(o.current=((t=(r=e.composedPath)==null?void 0:r.call(e))==null?void 0:t[0])||e.target)},!0),l(\"mousedown\",e=>{var r,t;i.current&&(o.current=((t=(r=e.composedPath)==null?void 0:r.call(e))==null?void 0:t[0])||e.target)},!0),l(\"click\",e=>{M()||o.current&&(c(e,()=>o.current),o.current=null)},!0),l(\"touchend\",e=>c(e,()=>e.target instanceof HTMLElement?e.target:null),!0),T(\"blur\",e=>c(e,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}export{y as useOutsideClick};\n", "function u(r,n,...a){if(r in n){let e=n[r];return typeof e==\"function\"?e(...a):e}let t=new Error(`Tried to handle \"${r}\" but there is no handler defined. Only defined handlers are: ${Object.keys(n).map(e=>`\"${e}\"`).join(\", \")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,u),t}export{u as match};\n", "import{env as n}from'./env.js';function o(r){return n.isServer?null:r instanceof Node?r.ownerDocument:r!=null&&r.hasOwnProperty(\"current\")&&r.current instanceof Node?r.current.ownerDocument:document}export{o as getOwnerDocument};\n", "import{disposables as b}from'./disposables.js';import{match as L}from'./match.js';import{getOwnerDocument as m}from'./owner.js';let c=[\"[contentEditable=true]\",\"[tabindex]\",\"a[href]\",\"area[href]\",\"button:not([disabled])\",\"iframe\",\"input:not([disabled])\",\"select:not([disabled])\",\"textarea:not([disabled])\"].map(e=>`${e}:not([tabindex='-1'])`).join(\",\");var M=(n=>(n[n.First=1]=\"First\",n[n.Previous=2]=\"Previous\",n[n.Next=4]=\"Next\",n[n.Last=8]=\"Last\",n[n.WrapAround=16]=\"WrapAround\",n[n.NoScroll=32]=\"NoScroll\",n))(M||{}),N=(o=>(o[o.Error=0]=\"Error\",o[o.Overflow=1]=\"Overflow\",o[o.Success=2]=\"Success\",o[o.Underflow=3]=\"Underflow\",o))(N||{}),F=(t=>(t[t.Previous=-1]=\"Previous\",t[t.Next=1]=\"Next\",t))(F||{});function f(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(c)).sort((r,t)=>Math.sign((r.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}var T=(t=>(t[t.Strict=0]=\"Strict\",t[t.Loose=1]=\"Loose\",t))(T||{});function h(e,r=0){var t;return e===((t=m(e))==null?void 0:t.body)?!1:L(r,{[0](){return e.matches(c)},[1](){let l=e;for(;l!==null;){if(l.matches(c))return!0;l=l.parentElement}return!1}})}function D(e){let r=m(e);b().nextFrame(()=>{r&&!h(r.activeElement,0)&&y(e)})}var w=(t=>(t[t.Keyboard=0]=\"Keyboard\",t[t.Mouse=1]=\"Mouse\",t))(w||{});typeof window!=\"undefined\"&&typeof document!=\"undefined\"&&(document.addEventListener(\"keydown\",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible=\"\")},!0),document.addEventListener(\"click\",e=>{e.detail===1?delete document.documentElement.dataset.headlessuiFocusVisible:e.detail===0&&(document.documentElement.dataset.headlessuiFocusVisible=\"\")},!0));function y(e){e==null||e.focus({preventScroll:!0})}let S=[\"textarea\",\"input\"].join(\",\");function H(e){var r,t;return(t=(r=e==null?void 0:e.matches)==null?void 0:r.call(e,S))!=null?t:!1}function I(e,r=t=>t){return e.slice().sort((t,l)=>{let o=r(t),i=r(l);if(o===null||i===null)return 0;let n=o.compareDocumentPosition(i);return n&Node.DOCUMENT_POSITION_FOLLOWING?-1:n&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function _(e,r){return O(f(),r,{relativeTo:e})}function O(e,r,{sorted:t=!0,relativeTo:l=null,skipElements:o=[]}={}){let i=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,n=Array.isArray(e)?t?I(e):e:f(e);o.length>0&&n.length>1&&(n=n.filter(s=>!o.includes(s))),l=l!=null?l:i.activeElement;let E=(()=>{if(r&5)return 1;if(r&10)return-1;throw new Error(\"Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last\")})(),x=(()=>{if(r&1)return 0;if(r&2)return Math.max(0,n.indexOf(l))-1;if(r&4)return Math.max(0,n.indexOf(l))+1;if(r&8)return n.length-1;throw new Error(\"Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last\")})(),p=r&32?{preventScroll:!0}:{},d=0,a=n.length,u;do{if(d>=a||d+a<=0)return 0;let s=x+d;if(r&16)s=(s+a)%a;else{if(s<0)return 3;if(s>=a)return 1}u=n[s],u==null||u.focus(p),d+=E}while(u!==i.activeElement);return r&6&&H(u)&&u.select(),2}export{M as Focus,N as FocusResult,T as FocusableMode,y as focusElement,_ as focusFrom,O as focusIn,f as getFocusableElements,h as isFocusableElement,D as restoreFocusIfNecessary,I as sortByDomNode};\n", "function t(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function i(){return/Android/gi.test(window.navigator.userAgent)}function n(){return t()||i()}export{i as isAndroid,t as isIOS,n as isMobile};\n", "import{useEffect as m}from\"react\";import{useLatestValue as c}from'./use-latest-value.js';function d(e,r,n){let o=c(r);m(()=>{function t(u){o.current(u)}return document.addEventListener(e,t,n),()=>document.removeEventListener(e,t,n)},[e,n])}export{d as useDocumentEvent};\n", "import{useEffect as d}from\"react\";import{useLatestValue as a}from'./use-latest-value.js';function s(e,r,n){let o=a(r);d(()=>{function t(i){o.current(i)}return window.addEventListener(e,t,n),()=>window.removeEventListener(e,t,n)},[e,n])}export{s as useWindowEvent};\n", "import{useMemo as t}from\"react\";import{getOwnerDocument as o}from'../utils/owner.js';function n(...e){return t(()=>o(...e),[...e])}export{n as useOwnerDocument};\n", "import{useState as o}from\"react\";import{useIsoMorphicEffect as r}from'./use-iso-morphic-effect.js';function i(t){var n;if(t.type)return t.type;let e=(n=t.as)!=null?n:\"button\";if(typeof e==\"string\"&&e.toLowerCase()===\"button\")return\"button\"}function T(t,e){let[n,u]=o(()=>i(t));return r(()=>{u(i(t))},[t.type,t.as]),r(()=>{n||e.current&&e.current instanceof HTMLButtonElement&&!e.current.hasAttribute(\"type\")&&u(\"button\")},[n,e]),n}export{T as useResolveButtonType};\n", "import{useEffect as l,useRef as i}from\"react\";import{useEvent as r}from'./use-event.js';let u=Symbol();function T(t,n=!0){return Object.assign(t,{[u]:n})}function y(...t){let n=i(t);l(()=>{n.current=t},[t]);let c=r(e=>{for(let o of n.current)o!=null&&(typeof o==\"function\"?o(e):o.current=e)});return t.every(e=>e==null||(e==null?void 0:e[u]))?void 0:c}export{T as optionalRef,y as useSyncRefs};\n", "import{useRef as o}from\"react\";function t(e){return[e.screenX,e.screenY]}function u(){let e=o([-1,-1]);return{wasMoved(r){let n=t(r);return e.current[0]===n[0]&&e.current[1]===n[1]?!1:(e.current=n,!0)},update(r){e.current=t(r)}}}export{u as useTrackedPointer};\n", "import{useEffect as m,useRef as E}from\"react\";import{getOwnerDocument as T}from'../utils/owner.js';import{useIsoMorphicEffect as N}from'./use-iso-morphic-effect.js';function F({container:e,accept:t,walk:r,enabled:c=!0}){let o=E(t),l=E(r);m(()=>{o.current=t,l.current=r},[t,r]),N(()=>{if(!e||!c)return;let n=T(e);if(!n)return;let f=o.current,p=l.current,d=Object.assign(i=>f(i),{acceptNode:f}),u=n.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,d,!1);for(;u.nextNode();)p(u.currentNode)},[e,c,o,l])}export{F as useTreeWalker};\n", "import{useEffect as s,useRef as f}from\"react\";import{useEvent as i}from'./use-event.js';function m(u,t){let e=f([]),r=i(u);s(()=>{let o=[...e.current];for(let[n,a]of t.entries())if(e.current[n]!==a){let l=r(t,o);return e.current=t,l}},[r,...t])}export{m as useWatch};\n", "import{cloneElement as N,createElement as E,forwardRef as h,Fragment as g,isValidElement as P,use<PERSON><PERSON>back as j,useRef as S}from\"react\";import{classNames as b}from'./class-names.js';import{match as w}from'./match.js';var O=(n=>(n[n.None=0]=\"None\",n[n.RenderStrategy=1]=\"RenderStrategy\",n[n.Static=2]=\"Static\",n))(O||{}),v=(e=>(e[e.Unmount=0]=\"Unmount\",e[e.Hidden=1]=\"Hidden\",e))(v||{});function C({ourProps:r,theirProps:t,slot:e,defaultTag:n,features:o,visible:a=!0,name:f,mergeRefs:l}){l=l!=null?l:k;let s=R(t,r);if(a)return m(s,e,n,f,l);let y=o!=null?o:0;if(y&2){let{static:u=!1,...d}=s;if(u)return m(d,e,n,f,l)}if(y&1){let{unmount:u=!0,...d}=s;return w(u?0:1,{[0](){return null},[1](){return m({...d,hidden:!0,style:{display:\"none\"}},e,n,f,l)}})}return m(s,e,n,f,l)}function m(r,t={},e,n,o){let{as:a=e,children:f,refName:l=\"ref\",...s}=F(r,[\"unmount\",\"static\"]),y=r.ref!==void 0?{[l]:r.ref}:{},u=typeof f==\"function\"?f(t):f;\"className\"in s&&s.className&&typeof s.className==\"function\"&&(s.className=s.className(t));let d={};if(t){let i=!1,c=[];for(let[T,p]of Object.entries(t))typeof p==\"boolean\"&&(i=!0),p===!0&&c.push(T);i&&(d[\"data-headlessui-state\"]=c.join(\" \"))}if(a===g&&Object.keys(x(s)).length>0){if(!P(u)||Array.isArray(u)&&u.length>1)throw new Error(['Passing props on \"Fragment\"!',\"\",`The current component <${n} /> is rendering a \"Fragment\".`,\"However we need to passthrough the following props:\",Object.keys(s).map(p=>`  - ${p}`).join(`\n`),\"\",\"You can apply a few solutions:\",['Add an `as=\"...\"` prop, to ensure that we render an actual element instead of a \"Fragment\".',\"Render a single element as the child so that we can forward the props onto that element.\"].map(p=>`  - ${p}`).join(`\n`)].join(`\n`));let i=u.props,c=typeof(i==null?void 0:i.className)==\"function\"?(...p)=>b(i==null?void 0:i.className(...p),s.className):b(i==null?void 0:i.className,s.className),T=c?{className:c}:{};return N(u,Object.assign({},R(u.props,x(F(s,[\"ref\"]))),d,y,{ref:o(u.ref,y.ref)},T))}return E(a,Object.assign({},F(s,[\"ref\"]),a!==g&&y,a!==g&&d),u)}function I(){let r=S([]),t=j(e=>{for(let n of r.current)n!=null&&(typeof n==\"function\"?n(e):n.current=e)},[]);return(...e)=>{if(!e.every(n=>n==null))return r.current=e,t}}function k(...r){return r.every(t=>t==null)?void 0:t=>{for(let e of r)e!=null&&(typeof e==\"function\"?e(t):e.current=t)}}function R(...r){var n;if(r.length===0)return{};if(r.length===1)return r[0];let t={},e={};for(let o of r)for(let a in o)a.startsWith(\"on\")&&typeof o[a]==\"function\"?((n=e[a])!=null||(e[a]=[]),e[a].push(o[a])):t[a]=o[a];if(t.disabled||t[\"aria-disabled\"])return Object.assign(t,Object.fromEntries(Object.keys(e).map(o=>[o,void 0])));for(let o in e)Object.assign(t,{[o](a,...f){let l=e[o];for(let s of l){if((a instanceof Event||(a==null?void 0:a.nativeEvent)instanceof Event)&&a.defaultPrevented)return;s(a,...f)}}});return t}function U(r){var t;return Object.assign(h(r),{displayName:(t=r.displayName)!=null?t:r.name})}function x(r){let t=Object.assign({},r);for(let e in t)t[e]===void 0&&delete t[e];return t}function F(r,t=[]){let e=Object.assign({},r);for(let n of t)n in e&&delete e[n];return e}export{O as Features,v as RenderStrategy,x as compact,U as forwardRefWithAs,C as render,I as useMergeRefsFn};\n", "function t(...r){return Array.from(new Set(r.flatMap(n=>typeof n==\"string\"?n.split(\" \"):[]))).filter(Boolean).join(\" \")}export{t as classNames};\n", "import{forwardRefWithAs as i,render as a}from'../utils/render.js';let p=\"div\";var s=(e=>(e[e.None=1]=\"None\",e[e.Focusable=2]=\"Focusable\",e[e.Hidden=4]=\"Hidden\",e))(s||{});function l(d,o){var n;let{features:t=1,...e}=d,r={ref:o,\"aria-hidden\":(t&2)===2?!0:(n=e[\"aria-hidden\"])!=null?n:void 0,hidden:(t&4)===4?!0:void 0,style:{position:\"fixed\",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:\"hidden\",clip:\"rect(0, 0, 0, 0)\",whiteSpace:\"nowrap\",borderWidth:\"0\",...(t&4)===4&&(t&2)!==2&&{display:\"none\"}}};return a({ourProps:r,theirProps:e,slot:{},defaultTag:p,name:\"Hidden\"})}let u=i(l);export{s as Features,u as Hidden};\n", "import t,{createContext as l,useContext as p}from\"react\";let n=l(null);n.displayName=\"OpenClosedContext\";var d=(e=>(e[e.Open=1]=\"Open\",e[e.Closed=2]=\"Closed\",e[e.Closing=4]=\"Closing\",e[e.Opening=8]=\"Opening\",e))(d||{});function u(){return p(n)}function s({value:o,children:r}){return t.createElement(n.Provider,{value:o},r)}export{s as OpenClosedProvider,d as State,u as useOpenClosed};\n", "function t(n){function e(){document.readyState!==\"loading\"&&(n(),document.removeEventListener(\"DOMContentLoaded\",e))}typeof window!=\"undefined\"&&typeof document!=\"undefined\"&&(document.addEventListener(\"DOMContentLoaded\",e),e())}export{t as onDocumentReady};\n", "import{onDocumentReady as d}from'./document-ready.js';let t=[];d(()=>{function e(n){n.target instanceof HTMLElement&&n.target!==document.body&&t[0]!==n.target&&(t.unshift(n.target),t=t.filter(r=>r!=null&&r.isConnected),t.splice(10))}window.addEventListener(\"click\",e,{capture:!0}),window.addEventListener(\"mousedown\",e,{capture:!0}),window.addEventListener(\"focus\",e,{capture:!0}),document.body.addEventListener(\"click\",e,{capture:!0}),document.body.addEventListener(\"mousedown\",e,{capture:!0}),document.body.addEventListener(\"focus\",e,{capture:!0})});export{t as history};\n", "function r(n){let e=n.parentElement,l=null;for(;e&&!(e instanceof HTMLFieldSetElement);)e instanceof HTMLLegendElement&&(l=e),e=e.parentElement;let t=(e==null?void 0:e.getAttribute(\"disabled\"))===\"\";return t&&i(l)?!1:t}function i(n){if(!n)return!1;let e=n.previousElementSibling;for(;e!==null;){if(e instanceof HTMLLegendElement)return!1;e=e.previousElementSibling}return!0}export{r as isDisabledReactIssue7711};\n", "function u(l){throw new Error(\"Unexpected object: \"+l)}var c=(i=>(i[i.First=0]=\"First\",i[i.Previous=1]=\"Previous\",i[i.Next=2]=\"Next\",i[i.Last=3]=\"Last\",i[i.Specific=4]=\"Specific\",i[i.Nothing=5]=\"Nothing\",i))(c||{});function f(l,n){let t=n.resolveItems();if(t.length<=0)return null;let r=n.resolveActiveIndex(),s=r!=null?r:-1;switch(l.focus){case 0:{for(let e=0;e<t.length;++e)if(!n.resolveDisabled(t[e],e,t))return e;return r}case 1:{for(let e=s-1;e>=0;--e)if(!n.resolveDisabled(t[e],e,t))return e;return r}case 2:{for(let e=s+1;e<t.length;++e)if(!n.resolveDisabled(t[e],e,t))return e;return r}case 3:{for(let e=t.length-1;e>=0;--e)if(!n.resolveDisabled(t[e],e,t))return e;return r}case 4:{for(let e=0;e<t.length;++e)if(n.resolveId(t[e],e,t)===l.id)return e;return r}case 5:return null;default:u(l)}}export{c as Focus,f as calculateActiveIndex};\n", "function e(i={},s=null,t=[]){for(let[r,n]of Object.entries(i))o(t,f(s,r),n);return t}function f(i,s){return i?i+\"[\"+s+\"]\":s}function o(i,s,t){if(Array.isArray(t))for(let[r,n]of t.entries())o(i,f(s,r.toString()),n);else t instanceof Date?i.push([s,t.toISOString()]):typeof t==\"boolean\"?i.push([s,t?\"1\":\"0\"]):typeof t==\"string\"?i.push([s,t]):typeof t==\"number\"?i.push([s,`${t}`]):t==null?i.push([s,\"\"]):e(t,s,i)}function p(i){var t,r;let s=(t=i==null?void 0:i.form)!=null?t:i.closest(\"form\");if(s){for(let n of s.elements)if(n!==i&&(n.tagName===\"INPUT\"&&n.type===\"submit\"||n.tagName===\"BUTTON\"&&n.type===\"submit\"||n.nodeName===\"INPUT\"&&n.type===\"image\")){n.click();return}(r=s.requestSubmit)==null||r.call(s)}}export{p as attemptSubmit,e as objectToFormEntries};\n", "var o=(r=>(r.<PERSON>=\" \",r.<PERSON><PERSON>=\"Enter\",r.<PERSON>=\"Escape\",r.<PERSON>space=\"Backspace\",r.Delete=\"Delete\",r.<PERSON>=\"ArrowLeft\",r.<PERSON>p=\"ArrowUp\",r.<PERSON>=\"ArrowRight\",r.<PERSON>=\"ArrowDown\",r.Home=\"Home\",r.End=\"End\",r.PageUp=\"PageUp\",r.PageDown=\"PageDown\",r.Tab=\"Tab\",r))(o||{});export{o as Keys};\n", "import u,{createContext as Pe,createRef as ye,use<PERSON><PERSON>back as K,useContext as V,useEffect as H,useMemo as y,useReducer as Ee,useRef as q,useState as Ae}from\"react\";import{FocusTrap as A}from'../../components/focus-trap/focus-trap.js';import{Portal as B,useNestedPortals as Re}from'../../components/portal/portal.js';import{useDocumentOverflowLockedEffect as Ce}from'../../hooks/document-overflow/use-document-overflow.js';import{useEvent as R}from'../../hooks/use-event.js';import{useEventListener as ve}from'../../hooks/use-event-listener.js';import{useId as C}from'../../hooks/use-id.js';import{useInert as z}from'../../hooks/use-inert.js';import{useOutsideClick as _e}from'../../hooks/use-outside-click.js';import{useOwnerDocument as Oe}from'../../hooks/use-owner.js';import{useRootContainers as be}from'../../hooks/use-root-containers.js';import{useServerHandoffComplete as he}from'../../hooks/use-server-handoff-complete.js';import{useSyncRefs as v}from'../../hooks/use-sync-refs.js';import{State as k,useOpenClosed as Se}from'../../internal/open-closed.js';import{ForcePortalRoot as G}from'../../internal/portal-force-root.js';import{StackMessage as Q,StackProvider as xe}from'../../internal/stack-context.js';import{isDisabledReactIssue7711 as Le}from'../../utils/bugs.js';import{match as N}from'../../utils/match.js';import{Features as Z,forwardRefWithAs as _,render as O}from'../../utils/render.js';import{Description as Fe,useDescriptions as ke}from'../description/description.js';import{Keys as Ie}from'../keyboard.js';var Me=(r=>(r[r.Open=0]=\"Open\",r[r.Closed=1]=\"Closed\",r))(Me||{}),we=(e=>(e[e.SetTitleId=0]=\"SetTitleId\",e))(we||{});let He={[0](o,e){return o.titleId===e.id?o:{...o,titleId:e.id}}},I=Pe(null);I.displayName=\"DialogContext\";function b(o){let e=V(I);if(e===null){let r=new Error(`<${o} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,b),r}return e}function Be(o,e,r=()=>[document.body]){Ce(o,e,i=>{var n;return{containers:[...(n=i.containers)!=null?n:[],r]}})}function Ge(o,e){return N(e.type,He,o,e)}let Ne=\"div\",Ue=Z.RenderStrategy|Z.Static;function We(o,e){let r=C(),{id:i=`headlessui-dialog-${r}`,open:n,onClose:l,initialFocus:s,role:a=\"dialog\",__demoMode:T=!1,...m}=o,[M,f]=Ae(0),U=q(!1);a=function(){return a===\"dialog\"||a===\"alertdialog\"?a:(U.current||(U.current=!0,console.warn(`Invalid role [${a}] passed to <Dialog />. Only \\`dialog\\` and and \\`alertdialog\\` are supported. Using \\`dialog\\` instead.`)),\"dialog\")}();let E=Se();n===void 0&&E!==null&&(n=(E&k.Open)===k.Open);let D=q(null),ee=v(D,e),g=Oe(D),W=o.hasOwnProperty(\"open\")||E!==null,$=o.hasOwnProperty(\"onClose\");if(!W&&!$)throw new Error(\"You have to provide an `open` and an `onClose` prop to the `Dialog` component.\");if(!W)throw new Error(\"You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.\");if(!$)throw new Error(\"You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.\");if(typeof n!=\"boolean\")throw new Error(`You provided an \\`open\\` prop to the \\`Dialog\\`, but the value is not a boolean. Received: ${n}`);if(typeof l!=\"function\")throw new Error(`You provided an \\`onClose\\` prop to the \\`Dialog\\`, but the value is not a function. Received: ${l}`);let p=n?0:1,[h,te]=Ee(Ge,{titleId:null,descriptionId:null,panelRef:ye()}),P=R(()=>l(!1)),Y=R(t=>te({type:0,id:t})),S=he()?T?!1:p===0:!1,x=M>1,j=V(I)!==null,[oe,re]=Re(),ne={get current(){var t;return(t=h.panelRef.current)!=null?t:D.current}},{resolveContainers:w,mainTreeNodeRef:L,MainTreeNode:le}=be({portals:oe,defaultContainers:[ne]}),ae=x?\"parent\":\"leaf\",J=E!==null?(E&k.Closing)===k.Closing:!1,ie=(()=>j||J?!1:S)(),se=K(()=>{var t,c;return(c=Array.from((t=g==null?void 0:g.querySelectorAll(\"body > *\"))!=null?t:[]).find(d=>d.id===\"headlessui-portal-root\"?!1:d.contains(L.current)&&d instanceof HTMLElement))!=null?c:null},[L]);z(se,ie);let pe=(()=>x?!0:S)(),de=K(()=>{var t,c;return(c=Array.from((t=g==null?void 0:g.querySelectorAll(\"[data-headlessui-portal]\"))!=null?t:[]).find(d=>d.contains(L.current)&&d instanceof HTMLElement))!=null?c:null},[L]);z(de,pe);let ue=(()=>!(!S||x))();_e(w,t=>{t.preventDefault(),P()},ue);let fe=(()=>!(x||p!==0))();ve(g==null?void 0:g.defaultView,\"keydown\",t=>{fe&&(t.defaultPrevented||t.key===Ie.Escape&&(t.preventDefault(),t.stopPropagation(),P()))});let ge=(()=>!(J||p!==0||j))();Be(g,ge,w),H(()=>{if(p!==0||!D.current)return;let t=new ResizeObserver(c=>{for(let d of c){let F=d.target.getBoundingClientRect();F.x===0&&F.y===0&&F.width===0&&F.height===0&&P()}});return t.observe(D.current),()=>t.disconnect()},[p,D,P]);let[Te,ce]=ke(),De=y(()=>[{dialogState:p,close:P,setTitleId:Y},h],[p,h,P,Y]),X=y(()=>({open:p===0}),[p]),me={ref:ee,id:i,role:a,\"aria-modal\":p===0?!0:void 0,\"aria-labelledby\":h.titleId,\"aria-describedby\":Te};return u.createElement(xe,{type:\"Dialog\",enabled:p===0,element:D,onUpdate:R((t,c)=>{c===\"Dialog\"&&N(t,{[Q.Add]:()=>f(d=>d+1),[Q.Remove]:()=>f(d=>d-1)})})},u.createElement(G,{force:!0},u.createElement(B,null,u.createElement(I.Provider,{value:De},u.createElement(B.Group,{target:D},u.createElement(G,{force:!1},u.createElement(ce,{slot:X,name:\"Dialog.Description\"},u.createElement(A,{initialFocus:s,containers:w,features:S?N(ae,{parent:A.features.RestoreFocus,leaf:A.features.All&~A.features.FocusLock}):A.features.None},u.createElement(re,null,O({ourProps:me,theirProps:m,slot:X,defaultTag:Ne,features:Ue,visible:p===0,name:\"Dialog\"}))))))))),u.createElement(le,null))}let $e=\"div\";function Ye(o,e){let r=C(),{id:i=`headlessui-dialog-overlay-${r}`,...n}=o,[{dialogState:l,close:s}]=b(\"Dialog.Overlay\"),a=v(e),T=R(f=>{if(f.target===f.currentTarget){if(Le(f.currentTarget))return f.preventDefault();f.preventDefault(),f.stopPropagation(),s()}}),m=y(()=>({open:l===0}),[l]);return O({ourProps:{ref:a,id:i,\"aria-hidden\":!0,onClick:T},theirProps:n,slot:m,defaultTag:$e,name:\"Dialog.Overlay\"})}let je=\"div\";function Je(o,e){let r=C(),{id:i=`headlessui-dialog-backdrop-${r}`,...n}=o,[{dialogState:l},s]=b(\"Dialog.Backdrop\"),a=v(e);H(()=>{if(s.panelRef.current===null)throw new Error(\"A <Dialog.Backdrop /> component is being used, but a <Dialog.Panel /> component is missing.\")},[s.panelRef]);let T=y(()=>({open:l===0}),[l]);return u.createElement(G,{force:!0},u.createElement(B,null,O({ourProps:{ref:a,id:i,\"aria-hidden\":!0},theirProps:n,slot:T,defaultTag:je,name:\"Dialog.Backdrop\"})))}let Xe=\"div\";function Ke(o,e){let r=C(),{id:i=`headlessui-dialog-panel-${r}`,...n}=o,[{dialogState:l},s]=b(\"Dialog.Panel\"),a=v(e,s.panelRef),T=y(()=>({open:l===0}),[l]),m=R(f=>{f.stopPropagation()});return O({ourProps:{ref:a,id:i,onClick:m},theirProps:n,slot:T,defaultTag:Xe,name:\"Dialog.Panel\"})}let Ve=\"h2\";function qe(o,e){let r=C(),{id:i=`headlessui-dialog-title-${r}`,...n}=o,[{dialogState:l,setTitleId:s}]=b(\"Dialog.Title\"),a=v(e);H(()=>(s(i),()=>s(null)),[i,s]);let T=y(()=>({open:l===0}),[l]);return O({ourProps:{ref:a,id:i},theirProps:n,slot:T,defaultTag:Ve,name:\"Dialog.Title\"})}let ze=_(We),Qe=_(Je),Ze=_(Ke),et=_(Ye),tt=_(qe),_t=Object.assign(ze,{Backdrop:Qe,Panel:Ze,Overlay:et,Title:tt,Description:Fe});export{_t as Dialog};\n", "import E,{useRef as d}from\"react\";import{useDisposables as U}from'../../hooks/use-disposables.js';import{useEvent as v}from'../../hooks/use-event.js';import{useEventListener as x}from'../../hooks/use-event-listener.js';import{useIsMounted as g}from'../../hooks/use-is-mounted.js';import{useOnUnmount as N}from'../../hooks/use-on-unmount.js';import{useOwnerDocument as I}from'../../hooks/use-owner.js';import{useServerHandoffComplete as G}from'../../hooks/use-server-handoff-complete.js';import{useSyncRefs as K}from'../../hooks/use-sync-refs.js';import{Direction as L,useTabDirection as W}from'../../hooks/use-tab-direction.js';import{useWatch as b}from'../../hooks/use-watch.js';import{Features as A,Hidden as O}from'../../internal/hidden.js';import{history as F}from'../../utils/active-element-history.js';import{Focus as p,focusElement as f,focusIn as M,FocusResult as V}from'../../utils/focus-management.js';import{match as k}from'../../utils/match.js';import{microTask as C}from'../../utils/micro-task.js';import{forwardRefWithAs as q,render as J}from'../../utils/render.js';function P(t){if(!t)return new Set;if(typeof t==\"function\")return new Set(t());let n=new Set;for(let e of t.current)e.current instanceof HTMLElement&&n.add(e.current);return n}let X=\"div\";var _=(r=>(r[r.None=1]=\"None\",r[r.InitialFocus=2]=\"InitialFocus\",r[r.TabLock=4]=\"TabLock\",r[r.FocusLock=8]=\"FocusLock\",r[r.RestoreFocus=16]=\"RestoreFocus\",r[r.All=30]=\"All\",r))(_||{});function z(t,n){let e=d(null),o=K(e,n),{initialFocus:l,containers:c,features:r=30,...s}=t;G()||(r=1);let i=I(e);Y({ownerDocument:i},Boolean(r&16));let u=Z({ownerDocument:i,container:e,initialFocus:l},Boolean(r&2));$({ownerDocument:i,container:e,containers:c,previousActiveElement:u},Boolean(r&8));let y=W(),R=v(a=>{let m=e.current;if(!m)return;(B=>B())(()=>{k(y.current,{[L.Forwards]:()=>{M(m,p.First,{skipElements:[a.relatedTarget]})},[L.Backwards]:()=>{M(m,p.Last,{skipElements:[a.relatedTarget]})}})})}),h=U(),H=d(!1),j={ref:o,onKeyDown(a){a.key==\"Tab\"&&(H.current=!0,h.requestAnimationFrame(()=>{H.current=!1}))},onBlur(a){let m=P(c);e.current instanceof HTMLElement&&m.add(e.current);let T=a.relatedTarget;T instanceof HTMLElement&&T.dataset.headlessuiFocusGuard!==\"true\"&&(S(m,T)||(H.current?M(e.current,k(y.current,{[L.Forwards]:()=>p.Next,[L.Backwards]:()=>p.Previous})|p.WrapAround,{relativeTo:a.target}):a.target instanceof HTMLElement&&f(a.target)))}};return E.createElement(E.Fragment,null,Boolean(r&4)&&E.createElement(O,{as:\"button\",type:\"button\",\"data-headlessui-focus-guard\":!0,onFocus:R,features:A.Focusable}),J({ourProps:j,theirProps:s,defaultTag:X,name:\"FocusTrap\"}),Boolean(r&4)&&E.createElement(O,{as:\"button\",type:\"button\",\"data-headlessui-focus-guard\":!0,onFocus:R,features:A.Focusable}))}let D=q(z),de=Object.assign(D,{features:_});function Q(t=!0){let n=d(F.slice());return b(([e],[o])=>{o===!0&&e===!1&&C(()=>{n.current.splice(0)}),o===!1&&e===!0&&(n.current=F.slice())},[t,F,n]),v(()=>{var e;return(e=n.current.find(o=>o!=null&&o.isConnected))!=null?e:null})}function Y({ownerDocument:t},n){let e=Q(n);b(()=>{n||(t==null?void 0:t.activeElement)===(t==null?void 0:t.body)&&f(e())},[n]),N(()=>{n&&f(e())})}function Z({ownerDocument:t,container:n,initialFocus:e},o){let l=d(null),c=g();return b(()=>{if(!o)return;let r=n.current;r&&C(()=>{if(!c.current)return;let s=t==null?void 0:t.activeElement;if(e!=null&&e.current){if((e==null?void 0:e.current)===s){l.current=s;return}}else if(r.contains(s)){l.current=s;return}e!=null&&e.current?f(e.current):M(r,p.First)===V.Error&&console.warn(\"There are no focusable elements inside the <FocusTrap />\"),l.current=t==null?void 0:t.activeElement})},[o]),l}function $({ownerDocument:t,container:n,containers:e,previousActiveElement:o},l){let c=g();x(t==null?void 0:t.defaultView,\"focus\",r=>{if(!l||!c.current)return;let s=P(e);n.current instanceof HTMLElement&&s.add(n.current);let i=o.current;if(!i)return;let u=r.target;u&&u instanceof HTMLElement?S(s,u)?(o.current=u,f(u)):(r.preventDefault(),r.stopPropagation(),f(i)):f(o.current)},!0)}function S(t,n){for(let e of t)if(e.contains(n))return!0;return!1}export{de as FocusTrap};\n", "import{useEffect as d}from\"react\";import{useLatestValue as s}from'./use-latest-value.js';function E(n,e,a,t){let i=s(a);d(()=>{n=n!=null?n:window;function r(o){i.current(o)}return n.addEventListener(e,r,t),()=>n.removeEventListener(e,r,t)},[n,e,t])}export{E as useEventListener};\n", "import{useRef as r}from\"react\";import{useIsoMorphicEffect as t}from'./use-iso-morphic-effect.js';function f(){let e=r(!1);return t(()=>(e.current=!0,()=>{e.current=!1}),[]),e}export{f as useIsMounted};\n", "import{useEffect as u,useRef as n}from\"react\";import{microTask as o}from'../utils/micro-task.js';import{useEvent as f}from'./use-event.js';function c(t){let r=f(t),e=n(!1);u(()=>(e.current=!1,()=>{e.current=!0,o(()=>{e.current&&r()})}),[r])}export{c as useOnUnmount};\n", "import{useRef as t}from\"react\";import{useWindowEvent as a}from'./use-window-event.js';var s=(r=>(r[r.Forwards=0]=\"Forwards\",r[r.Backwards=1]=\"Backwards\",r))(s||{});function n(){let e=t(0);return a(\"keydown\",o=>{o.key===\"Tab\"&&(e.current=o.shiftKey?1:0)},!0),e}export{s as Direction,n as useTabDirection};\n", "import T,{createContext as P,Fragment as m,useContext as s,useEffect as d,useMemo as g,useRef as R,useState as E}from\"react\";import{createPortal as C}from\"react-dom\";import{useEvent as c}from'../../hooks/use-event.js';import{useIsoMorphicEffect as y}from'../../hooks/use-iso-morphic-effect.js';import{useOnUnmount as H}from'../../hooks/use-on-unmount.js';import{useOwnerDocument as x}from'../../hooks/use-owner.js';import{useServerHandoffComplete as b}from'../../hooks/use-server-handoff-complete.js';import{optionalRef as h,useSyncRefs as L}from'../../hooks/use-sync-refs.js';import{usePortalRoot as O}from'../../internal/portal-force-root.js';import{env as A}from'../../utils/env.js';import{forwardRefWithAs as G,render as M}from'../../utils/render.js';function F(p){let n=O(),l=s(_),e=x(p),[a,o]=E(()=>{if(!n&&l!==null||A.isServer)return null;let t=e==null?void 0:e.getElementById(\"headlessui-portal-root\");if(t)return t;if(e===null)return null;let r=e.createElement(\"div\");return r.setAttribute(\"id\",\"headlessui-portal-root\"),e.body.appendChild(r)});return d(()=>{a!==null&&(e!=null&&e.body.contains(a)||e==null||e.body.appendChild(a))},[a,e]),d(()=>{n||l!==null&&o(l.current)},[l,o,n]),a}let U=m;function N(p,n){let l=p,e=R(null),a=L(h(u=>{e.current=u}),n),o=x(e),t=F(e),[r]=E(()=>{var u;return A.isServer?null:(u=o==null?void 0:o.createElement(\"div\"))!=null?u:null}),i=s(f),v=b();return y(()=>{!t||!r||t.contains(r)||(r.setAttribute(\"data-headlessui-portal\",\"\"),t.appendChild(r))},[t,r]),y(()=>{if(r&&i)return i.register(r)},[i,r]),H(()=>{var u;!t||!r||(r instanceof Node&&t.contains(r)&&t.removeChild(r),t.childNodes.length<=0&&((u=t.parentElement)==null||u.removeChild(t)))}),v?!t||!r?null:C(M({ourProps:{ref:a},theirProps:l,defaultTag:U,name:\"Portal\"}),r):null}let S=m,_=P(null);function j(p,n){let{target:l,...e}=p,o={ref:L(n)};return T.createElement(_.Provider,{value:l},M({ourProps:o,theirProps:e,defaultTag:S,name:\"Popover.Group\"}))}let f=P(null);function ee(){let p=s(f),n=R([]),l=c(o=>(n.current.push(o),p&&p.register(o),()=>e(o))),e=c(o=>{let t=n.current.indexOf(o);t!==-1&&n.current.splice(t,1),p&&p.unregister(o)}),a=g(()=>({register:l,unregister:e,portals:n}),[l,e,n]);return[n,g(()=>function({children:t}){return T.createElement(f.Provider,{value:a},t)},[a])]}let D=G(N),I=G(j),te=Object.assign(D,{Group:I});export{te as Portal,ee as useNestedPortals};\n", "import t,{createContext as r,useContext as c}from\"react\";let e=r(!1);function a(){return c(e)}function l(o){return t.createElement(e.Provider,{value:o.force},o.children)}export{l as ForcePortalRoot,a as usePortalRoot};\n", "import*as e from\"react\";import{useSyncExternalStore as t}from'./useSyncExternalStoreShimClient.js';import{useSyncExternalStore as o}from'./useSyncExternalStoreShimServer.js';const r=typeof window!=\"undefined\"&&typeof window.document!=\"undefined\"&&typeof window.document.createElement!=\"undefined\",s=!r,c=s?o:t,a=\"useSyncExternalStore\"in e?(n=>n.useSyncExternalStore)(e):c;export{a as useSyncExternalStore};\n", "import*as l from\"react\";function i(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}const d=typeof Object.is==\"function\"?Object.is:i,{useState:u,useEffect:h,useLayoutEffect:f,useDebugValue:p}=l;let S=!1,_=!1;function y(e,t,c){const a=t(),[{inst:n},o]=u({inst:{value:a,getSnapshot:t}});return f(()=>{n.value=a,n.getSnapshot=t,r(n)&&o({inst:n})},[e,a,t]),h(()=>(r(n)&&o({inst:n}),e(()=>{r(n)&&o({inst:n})})),[e]),p(a),a}function r(e){const t=e.getSnapshot,c=e.value;try{const a=t();return!d(c,a)}catch{return!0}}export{y as useSyncExternalStore};\n", "function t(r,e,n){return e()}export{t as useSyncExternalStore};\n", "import{useSyncExternalStore as r}from'../use-sync-external-store-shim/index.js';function S(t){return r(t.subscribe,t.getSnapshot,t.getSnapshot)}export{S as useStore};\n", "function a(o,r){let t=o(),n=new Set;return{getSnapshot(){return t},subscribe(e){return n.add(e),()=>n.delete(e)},dispatch(e,...s){let i=r[e].call(t,...s);i&&(t=i,n.forEach(c=>c()))}}}export{a as createStore};\n", "function c(){let o;return{before({doc:e}){var l;let n=e.documentElement;o=((l=e.defaultView)!=null?l:window).innerWidth-n.clientWidth},after({doc:e,d:n}){let t=e.documentElement,l=t.clientWidth-t.offsetWidth,r=o-l;n.style(t,\"paddingRight\",`${r}px`)}}}export{c as adjustScrollbarPadding};\n", "import{disposables as m}from'../../utils/disposables.js';import{isIOS as u}from'../../utils/platform.js';function d(){return u()?{before({doc:r,d:l,meta:c}){function o(a){return c.containers.flatMap(n=>n()).some(n=>n.contains(a))}l.microTask(()=>{var s;if(window.getComputedStyle(r.documentElement).scrollBehavior!==\"auto\"){let t=m();t.style(r.documentElement,\"scrollBehavior\",\"auto\"),l.add(()=>l.microTask(()=>t.dispose()))}let a=(s=window.scrollY)!=null?s:window.pageYOffset,n=null;l.addEventListener(r,\"click\",t=>{if(t.target instanceof HTMLElement)try{let e=t.target.closest(\"a\");if(!e)return;let{hash:f}=new URL(e.href),i=r.querySelector(f);i&&!o(i)&&(n=i)}catch{}},!0),l.addEventListener(r,\"touchstart\",t=>{if(t.target instanceof HTMLElement)if(o(t.target)){let e=t.target;for(;e.parentElement&&o(e.parentElement);)e=e.parentElement;l.style(e,\"overscrollBehavior\",\"contain\")}else l.style(t.target,\"touchAction\",\"none\")}),l.addEventListener(r,\"touchmove\",t=>{if(t.target instanceof HTMLElement)if(o(t.target)){let e=t.target;for(;e.parentElement&&e.dataset.headlessuiPortal!==\"\"&&!(e.scrollHeight>e.clientHeight||e.scrollWidth>e.clientWidth);)e=e.parentElement;e.dataset.headlessuiPortal===\"\"&&t.preventDefault()}else t.preventDefault()},{passive:!1}),l.add(()=>{var e;let t=(e=window.scrollY)!=null?e:window.pageYOffset;a!==t&&window.scrollTo(0,a),n&&n.isConnected&&(n.scrollIntoView({block:\"nearest\"}),n=null)})})}}:{}}export{d as handleIOSLocking};\n", "function l(){return{before({doc:e,d:o}){o.style(e.documentElement,\"overflow\",\"hidden\")}}}export{l as preventScroll};\n", "import{disposables as s}from'../../utils/disposables.js';import{createStore as i}from'../../utils/store.js';import{adjustScrollbarPadding as l}from'./adjust-scrollbar-padding.js';import{handleIOSLocking as d}from'./handle-ios-locking.js';import{preventScroll as p}from'./prevent-scroll.js';function m(e){let n={};for(let t of e)Object.assign(n,t(n));return n}let a=i(()=>new Map,{PUSH(e,n){var o;let t=(o=this.get(e))!=null?o:{doc:e,count:0,d:s(),meta:new Set};return t.count++,t.meta.add(n),this.set(e,t),this},POP(e,n){let t=this.get(e);return t&&(t.count--,t.meta.delete(n)),this},SCROLL_PREVENT({doc:e,d:n,meta:t}){let o={doc:e,d:n,meta:m(t)},c=[d(),l(),p()];c.forEach(({before:r})=>r==null?void 0:r(o)),c.forEach(({after:r})=>r==null?void 0:r(o))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});a.subscribe(()=>{let e=a.getSnapshot(),n=new Map;for(let[t]of e)n.set(t,t.documentElement.style.overflow);for(let t of e.values()){let o=n.get(t.doc)===\"hidden\",c=t.count!==0;(c&&!o||!c&&o)&&a.dispatch(t.count>0?\"SCROLL_PREVENT\":\"SCROLL_ALLOW\",t),t.count===0&&a.dispatch(\"TEARDOWN\",t)}});export{a as overflows};\n", "import{useStore as u}from'../../hooks/use-store.js';import{useIsoMorphicEffect as s}from'../use-iso-morphic-effect.js';import{overflows as t}from'./overflow-store.js';function p(e,r,n){let f=u(t),o=e?f.get(e):void 0,i=o?o.count>0:!1;return s(()=>{if(!(!e||!r))return t.dispatch(\"PUSH\",e,n),()=>t.dispatch(\"POP\",e,n)},[r,e]),i}export{p as useDocumentOverflowLockedEffect};\n", "import{useIsoMorphicEffect as s}from'./use-iso-morphic-effect.js';let u=new Map,t=new Map;function b(r,l=!0){s(()=>{var o;if(!l)return;let e=typeof r==\"function\"?r():r.current;if(!e)return;function a(){var d;if(!e)return;let i=(d=t.get(e))!=null?d:1;if(i===1?t.delete(e):t.set(e,i-1),i!==1)return;let n=u.get(e);n&&(n[\"aria-hidden\"]===null?e.removeAttribute(\"aria-hidden\"):e.setAttribute(\"aria-hidden\",n[\"aria-hidden\"]),e.inert=n.inert,u.delete(e))}let f=(o=t.get(e))!=null?o:0;return t.set(e,f+1),f!==0||(u.set(e,{\"aria-hidden\":e.getAttribute(\"aria-hidden\"),inert:e.inert}),e.setAttribute(\"aria-hidden\",\"true\"),e.inert=!0),a},[r,l])}export{b as useInert};\n", "import m,{useMemo as d,useRef as M}from\"react\";import{Features as H,Hidden as T}from'../internal/hidden.js';import{useEvent as E}from'./use-event.js';import{useOwnerDocument as b}from'./use-owner.js';function N({defaultContainers:o=[],portals:r,mainTreeNodeRef:u}={}){var f;let t=M((f=u==null?void 0:u.current)!=null?f:null),l=b(t),c=E(()=>{var i,s,a;let n=[];for(let e of o)e!==null&&(e instanceof HTMLElement?n.push(e):\"current\"in e&&e.current instanceof HTMLElement&&n.push(e.current));if(r!=null&&r.current)for(let e of r.current)n.push(e);for(let e of(i=l==null?void 0:l.querySelectorAll(\"html > *, body > *\"))!=null?i:[])e!==document.body&&e!==document.head&&e instanceof HTMLElement&&e.id!==\"headlessui-portal-root\"&&(e.contains(t.current)||e.contains((a=(s=t.current)==null?void 0:s.getRootNode())==null?void 0:a.host)||n.some(L=>e.contains(L))||n.push(e));return n});return{resolveContainers:c,contains:E(n=>c().some(i=>i.contains(n))),mainTreeNodeRef:t,MainTreeNode:d(()=>function(){return u!=null?null:m.createElement(T,{features:H.Hidden,ref:t})},[t,u])}}function y(){let o=M(null);return{mainTreeNodeRef:o,MainTreeNode:d(()=>function(){return m.createElement(T,{features:H.Hidden,ref:o})},[o])}}export{y as useMainTreeNode,N as useRootContainers};\n", "import d,{createContext as c,useContext as m}from\"react\";import{useEvent as p}from'../hooks/use-event.js';import{useIsoMorphicEffect as f}from'../hooks/use-iso-morphic-effect.js';let a=c(()=>{});a.displayName=\"StackContext\";var s=(e=>(e[e.Add=0]=\"Add\",e[e.Remove=1]=\"Remove\",e))(s||{});function x(){return m(a)}function b({children:i,onUpdate:r,type:e,element:n,enabled:u}){let l=x(),o=p((...t)=>{r==null||r(...t),l(...t)});return f(()=>{let t=u===void 0||u===!0;return t&&o(0,e,n),()=>{t&&o(1,e,n)}},[o,e,n,u]),d.createElement(a.Provider,{value:o},i)}export{s as StackMessage,b as StackProvider,x as useStackContext};\n", "import u,{createContext as m,useContext as D,useMemo as l,useState as T}from\"react\";import{useEvent as P}from'../../hooks/use-event.js';import{useId as g}from'../../hooks/use-id.js';import{useIsoMorphicEffect as E}from'../../hooks/use-iso-morphic-effect.js';import{useSyncRefs as x}from'../../hooks/use-sync-refs.js';import{forwardRefWithAs as y,render as R}from'../../utils/render.js';let d=m(null);function f(){let r=D(d);if(r===null){let t=new Error(\"You used a <Description /> component, but it is not inside a relevant parent.\");throw Error.captureStackTrace&&Error.captureStackTrace(t,f),t}return r}function w(){let[r,t]=T([]);return[r.length>0?r.join(\" \"):void 0,l(()=>function(e){let i=P(s=>(t(o=>[...o,s]),()=>t(o=>{let p=o.slice(),c=p.indexOf(s);return c!==-1&&p.splice(c,1),p}))),n=l(()=>({register:i,slot:e.slot,name:e.name,props:e.props}),[i,e.slot,e.name,e.props]);return u.createElement(d.Provider,{value:n},e.children)},[t])]}let I=\"p\";function S(r,t){let a=g(),{id:e=`headlessui-description-${a}`,...i}=r,n=f(),s=x(t);E(()=>n.register(e),[e,n.register]);let o={ref:s,...n.props,id:e};return R({ourProps:o,theirProps:i,slot:n.slot||{},defaultTag:I,name:n.name||\"Description\"})}let h=y(S),G=Object.assign(h,{});export{G as Description,w as useDescriptions};\n", "import E,{createContext as I,Fragment as H,useContext as x,useEffect as h,useMemo as S,useReducer as G,useRef as R}from\"react\";import{useEvent as A}from'../../hooks/use-event.js';import{useId as U}from'../../hooks/use-id.js';import{useResolveButtonType as j}from'../../hooks/use-resolve-button-type.js';import{optionalRef as W,useSyncRefs as L}from'../../hooks/use-sync-refs.js';import{OpenClosedProvider as $,State as b,useOpenClosed as J}from'../../internal/open-closed.js';import{isDisabledReactIssue7711 as X}from'../../utils/bugs.js';import{match as O}from'../../utils/match.js';import{getOwnerDocument as q}from'../../utils/owner.js';import{Features as w,forwardRefWithAs as B,render as k,useMergeRefsFn as N}from'../../utils/render.js';import{startTransition as z}from'../../utils/start-transition.js';import{Keys as g}from'../keyboard.js';var Q=(o=>(o[o.Open=0]=\"Open\",o[o.Closed=1]=\"Closed\",o))(Q||{}),V=(t=>(t[t.ToggleDisclosure=0]=\"ToggleDisclosure\",t[t.CloseDisclosure=1]=\"CloseDisclosure\",t[t.SetButtonId=2]=\"SetButtonId\",t[t.SetPanelId=3]=\"SetPanelId\",t[t.LinkPanel=4]=\"LinkPanel\",t[t.UnlinkPanel=5]=\"UnlinkPanel\",t))(V||{});let Y={[0]:e=>({...e,disclosureState:O(e.disclosureState,{[0]:1,[1]:0})}),[1]:e=>e.disclosureState===1?e:{...e,disclosureState:1},[4](e){return e.linkedPanel===!0?e:{...e,linkedPanel:!0}},[5](e){return e.linkedPanel===!1?e:{...e,linkedPanel:!1}},[2](e,n){return e.buttonId===n.buttonId?e:{...e,buttonId:n.buttonId}},[3](e,n){return e.panelId===n.panelId?e:{...e,panelId:n.panelId}}},M=I(null);M.displayName=\"DisclosureContext\";function _(e){let n=x(M);if(n===null){let o=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,_),o}return n}let v=I(null);v.displayName=\"DisclosureAPIContext\";function K(e){let n=x(v);if(n===null){let o=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,K),o}return n}let F=I(null);F.displayName=\"DisclosurePanelContext\";function Z(){return x(F)}function ee(e,n){return O(n.type,Y,e,n)}let te=H;function ne(e,n){let{defaultOpen:o=!1,...i}=e,f=R(null),l=L(n,W(u=>{f.current=u},e.as===void 0||e.as===H)),t=R(null),d=R(null),s=G(ee,{disclosureState:o?0:1,linkedPanel:!1,buttonRef:d,panelRef:t,buttonId:null,panelId:null}),[{disclosureState:c,buttonId:a},D]=s,p=A(u=>{D({type:1});let y=q(f);if(!y||!a)return;let r=(()=>u?u instanceof HTMLElement?u:u.current instanceof HTMLElement?u.current:y.getElementById(a):y.getElementById(a))();r==null||r.focus()}),P=S(()=>({close:p}),[p]),T=S(()=>({open:c===0,close:p}),[c,p]),C={ref:l};return E.createElement(M.Provider,{value:s},E.createElement(v.Provider,{value:P},E.createElement($,{value:O(c,{[0]:b.Open,[1]:b.Closed})},k({ourProps:C,theirProps:i,slot:T,defaultTag:te,name:\"Disclosure\"}))))}let le=\"button\";function oe(e,n){let o=U(),{id:i=`headlessui-disclosure-button-${o}`,...f}=e,[l,t]=_(\"Disclosure.Button\"),d=Z(),s=d===null?!1:d===l.panelId,c=R(null),a=L(c,n,s?null:l.buttonRef),D=N();h(()=>{if(!s)return t({type:2,buttonId:i}),()=>{t({type:2,buttonId:null})}},[i,t,s]);let p=A(r=>{var m;if(s){if(l.disclosureState===1)return;switch(r.key){case g.Space:case g.Enter:r.preventDefault(),r.stopPropagation(),t({type:0}),(m=l.buttonRef.current)==null||m.focus();break}}else switch(r.key){case g.Space:case g.Enter:r.preventDefault(),r.stopPropagation(),t({type:0});break}}),P=A(r=>{switch(r.key){case g.Space:r.preventDefault();break}}),T=A(r=>{var m;X(r.currentTarget)||e.disabled||(s?(t({type:0}),(m=l.buttonRef.current)==null||m.focus()):t({type:0}))}),C=S(()=>({open:l.disclosureState===0}),[l]),u=j(e,c),y=s?{ref:a,type:u,onKeyDown:p,onClick:T}:{ref:a,id:i,type:u,\"aria-expanded\":l.disclosureState===0,\"aria-controls\":l.linkedPanel?l.panelId:void 0,onKeyDown:p,onKeyUp:P,onClick:T};return k({mergeRefs:D,ourProps:y,theirProps:f,slot:C,defaultTag:le,name:\"Disclosure.Button\"})}let re=\"div\",se=w.RenderStrategy|w.Static;function ue(e,n){let o=U(),{id:i=`headlessui-disclosure-panel-${o}`,...f}=e,[l,t]=_(\"Disclosure.Panel\"),{close:d}=K(\"Disclosure.Panel\"),s=N(),c=L(n,l.panelRef,T=>{z(()=>t({type:T?4:5}))});h(()=>(t({type:3,panelId:i}),()=>{t({type:3,panelId:null})}),[i,t]);let a=J(),D=(()=>a!==null?(a&b.Open)===b.Open:l.disclosureState===0)(),p=S(()=>({open:l.disclosureState===0,close:d}),[l,d]),P={ref:c,id:i};return E.createElement(F.Provider,{value:l.panelId},k({mergeRefs:s,ourProps:P,theirProps:f,slot:p,defaultTag:re,features:se,visible:D,name:\"Disclosure.Panel\"}))}let ie=B(ne),ae=B(oe),pe=B(ue),Ae=Object.assign(ie,{Button:ae,Panel:pe});export{Ae as Disclosure};\n", "var t;import r from\"react\";let a=(t=r.startTransition)!=null?t:function(i){i()};export{a as startTransition};\n", "import N,{createContext as Z,createRef as xe,Fragment as ye,use<PERSON><PERSON>back as ge,useContext as ee,useEffect as te,useMemo as E,useReducer as Le,useRef as h}from\"react\";import{useComputed as oe}from'../../hooks/use-computed.js';import{useControllable as Oe}from'../../hooks/use-controllable.js';import{useDisposables as j}from'../../hooks/use-disposables.js';import{useEvent as f}from'../../hooks/use-event.js';import{useId as V}from'../../hooks/use-id.js';import{useIsoMorphicEffect as K}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as me}from'../../hooks/use-latest-value.js';import{useOutsideClick as Re}from'../../hooks/use-outside-click.js';import{useResolveButtonType as ve}from'../../hooks/use-resolve-button-type.js';import{useSyncRefs as _}from'../../hooks/use-sync-refs.js';import{useTextValue as Ae}from'../../hooks/use-text-value.js';import{useTrackedPointer as Se}from'../../hooks/use-tracked-pointer.js';import{Features as Pe,Hidden as Ee}from'../../internal/hidden.js';import{OpenClosedProvider as he,State as Q,useOpenClosed as De}from'../../internal/open-closed.js';import{isDisabledReactIssue7711 as Ie}from'../../utils/bugs.js';import{calculateActiveIndex as Ce,Focus as v}from'../../utils/calculate-active-index.js';import{disposables as $}from'../../utils/disposables.js';import{FocusableMode as _e,isFocusableElement as Fe,sortByDomNode as Me}from'../../utils/focus-management.js';import{objectToFormEntries as ke}from'../../utils/form.js';import{match as D}from'../../utils/match.js';import{getOwnerDocument as we}from'../../utils/owner.js';import{compact as Ue,Features as ne,forwardRefWithAs as F,render as M}from'../../utils/render.js';import{Keys as y}from'../keyboard.js';var Be=(n=>(n[n.Open=0]=\"Open\",n[n.Closed=1]=\"Closed\",n))(Be||{}),He=(n=>(n[n.Single=0]=\"Single\",n[n.Multi=1]=\"Multi\",n))(He||{}),Ge=(n=>(n[n.Pointer=0]=\"Pointer\",n[n.Other=1]=\"Other\",n))(Ge||{}),Ne=(i=>(i[i.OpenListbox=0]=\"OpenListbox\",i[i.CloseListbox=1]=\"CloseListbox\",i[i.GoToOption=2]=\"GoToOption\",i[i.Search=3]=\"Search\",i[i.ClearSearch=4]=\"ClearSearch\",i[i.RegisterOption=5]=\"RegisterOption\",i[i.UnregisterOption=6]=\"UnregisterOption\",i[i.RegisterLabel=7]=\"RegisterLabel\",i))(Ne||{});function z(e,a=n=>n){let n=e.activeOptionIndex!==null?e.options[e.activeOptionIndex]:null,r=Me(a(e.options.slice()),t=>t.dataRef.current.domRef.current),l=n?r.indexOf(n):null;return l===-1&&(l=null),{options:r,activeOptionIndex:l}}let je={[1](e){return e.dataRef.current.disabled||e.listboxState===1?e:{...e,activeOptionIndex:null,listboxState:1}},[0](e){if(e.dataRef.current.disabled||e.listboxState===0)return e;let a=e.activeOptionIndex,{isSelected:n}=e.dataRef.current,r=e.options.findIndex(l=>n(l.dataRef.current.value));return r!==-1&&(a=r),{...e,listboxState:0,activeOptionIndex:a}},[2](e,a){var l;if(e.dataRef.current.disabled||e.listboxState===1)return e;let n=z(e),r=Ce(a,{resolveItems:()=>n.options,resolveActiveIndex:()=>n.activeOptionIndex,resolveId:t=>t.id,resolveDisabled:t=>t.dataRef.current.disabled});return{...e,...n,searchQuery:\"\",activeOptionIndex:r,activationTrigger:(l=a.trigger)!=null?l:1}},[3]:(e,a)=>{if(e.dataRef.current.disabled||e.listboxState===1)return e;let r=e.searchQuery!==\"\"?0:1,l=e.searchQuery+a.value.toLowerCase(),p=(e.activeOptionIndex!==null?e.options.slice(e.activeOptionIndex+r).concat(e.options.slice(0,e.activeOptionIndex+r)):e.options).find(i=>{var b;return!i.dataRef.current.disabled&&((b=i.dataRef.current.textValue)==null?void 0:b.startsWith(l))}),u=p?e.options.indexOf(p):-1;return u===-1||u===e.activeOptionIndex?{...e,searchQuery:l}:{...e,searchQuery:l,activeOptionIndex:u,activationTrigger:1}},[4](e){return e.dataRef.current.disabled||e.listboxState===1||e.searchQuery===\"\"?e:{...e,searchQuery:\"\"}},[5]:(e,a)=>{let n={id:a.id,dataRef:a.dataRef},r=z(e,l=>[...l,n]);return e.activeOptionIndex===null&&e.dataRef.current.isSelected(a.dataRef.current.value)&&(r.activeOptionIndex=r.options.indexOf(n)),{...e,...r}},[6]:(e,a)=>{let n=z(e,r=>{let l=r.findIndex(t=>t.id===a.id);return l!==-1&&r.splice(l,1),r});return{...e,...n,activationTrigger:1}},[7]:(e,a)=>({...e,labelId:a.id})},J=Z(null);J.displayName=\"ListboxActionsContext\";function k(e){let a=ee(J);if(a===null){let n=new Error(`<${e} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,k),n}return a}let q=Z(null);q.displayName=\"ListboxDataContext\";function w(e){let a=ee(q);if(a===null){let n=new Error(`<${e} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,w),n}return a}function Ve(e,a){return D(a.type,je,e,a)}let Ke=ye;function Qe(e,a){let{value:n,defaultValue:r,form:l,name:t,onChange:p,by:u=(s,c)=>s===c,disabled:i=!1,horizontal:b=!1,multiple:R=!1,...m}=e;const P=b?\"horizontal\":\"vertical\";let S=_(a),[g=R?[]:void 0,x]=Oe(n,p,r),[T,o]=Le(Ve,{dataRef:xe(),listboxState:1,options:[],searchQuery:\"\",labelId:null,activeOptionIndex:null,activationTrigger:1}),L=h({static:!1,hold:!1}),U=h(null),B=h(null),W=h(null),I=f(typeof u==\"string\"?(s,c)=>{let O=u;return(s==null?void 0:s[O])===(c==null?void 0:c[O])}:u),A=ge(s=>D(d.mode,{[1]:()=>g.some(c=>I(c,s)),[0]:()=>I(g,s)}),[g]),d=E(()=>({...T,value:g,disabled:i,mode:R?1:0,orientation:P,compare:I,isSelected:A,optionsPropsRef:L,labelRef:U,buttonRef:B,optionsRef:W}),[g,i,R,T]);K(()=>{T.dataRef.current=d},[d]),Re([d.buttonRef,d.optionsRef],(s,c)=>{var O;o({type:1}),Fe(c,_e.Loose)||(s.preventDefault(),(O=d.buttonRef.current)==null||O.focus())},d.listboxState===0);let H=E(()=>({open:d.listboxState===0,disabled:i,value:g}),[d,i,g]),ie=f(s=>{let c=d.options.find(O=>O.id===s);c&&X(c.dataRef.current.value)}),re=f(()=>{if(d.activeOptionIndex!==null){let{dataRef:s,id:c}=d.options[d.activeOptionIndex];X(s.current.value),o({type:2,focus:v.Specific,id:c})}}),ae=f(()=>o({type:0})),le=f(()=>o({type:1})),se=f((s,c,O)=>s===v.Specific?o({type:2,focus:v.Specific,id:c,trigger:O}):o({type:2,focus:s,trigger:O})),pe=f((s,c)=>(o({type:5,id:s,dataRef:c}),()=>o({type:6,id:s}))),ue=f(s=>(o({type:7,id:s}),()=>o({type:7,id:null}))),X=f(s=>D(d.mode,{[0](){return x==null?void 0:x(s)},[1](){let c=d.value.slice(),O=c.findIndex(C=>I(C,s));return O===-1?c.push(s):c.splice(O,1),x==null?void 0:x(c)}})),de=f(s=>o({type:3,value:s})),ce=f(()=>o({type:4})),fe=E(()=>({onChange:X,registerOption:pe,registerLabel:ue,goToOption:se,closeListbox:le,openListbox:ae,selectActiveOption:re,selectOption:ie,search:de,clearSearch:ce}),[]),Te={ref:S},G=h(null),be=j();return te(()=>{G.current&&r!==void 0&&be.addEventListener(G.current,\"reset\",()=>{x==null||x(r)})},[G,x]),N.createElement(J.Provider,{value:fe},N.createElement(q.Provider,{value:d},N.createElement(he,{value:D(d.listboxState,{[0]:Q.Open,[1]:Q.Closed})},t!=null&&g!=null&&ke({[t]:g}).map(([s,c],O)=>N.createElement(Ee,{features:Pe.Hidden,ref:O===0?C=>{var Y;G.current=(Y=C==null?void 0:C.closest(\"form\"))!=null?Y:null}:void 0,...Ue({key:s,as:\"input\",type:\"hidden\",hidden:!0,readOnly:!0,form:l,disabled:i,name:s,value:c})})),M({ourProps:Te,theirProps:m,slot:H,defaultTag:Ke,name:\"Listbox\"}))))}let We=\"button\";function Xe(e,a){var x;let n=V(),{id:r=`headlessui-listbox-button-${n}`,...l}=e,t=w(\"Listbox.Button\"),p=k(\"Listbox.Button\"),u=_(t.buttonRef,a),i=j(),b=f(T=>{switch(T.key){case y.Space:case y.Enter:case y.ArrowDown:T.preventDefault(),p.openListbox(),i.nextFrame(()=>{t.value||p.goToOption(v.First)});break;case y.ArrowUp:T.preventDefault(),p.openListbox(),i.nextFrame(()=>{t.value||p.goToOption(v.Last)});break}}),R=f(T=>{switch(T.key){case y.Space:T.preventDefault();break}}),m=f(T=>{if(Ie(T.currentTarget))return T.preventDefault();t.listboxState===0?(p.closeListbox(),i.nextFrame(()=>{var o;return(o=t.buttonRef.current)==null?void 0:o.focus({preventScroll:!0})})):(T.preventDefault(),p.openListbox())}),P=oe(()=>{if(t.labelId)return[t.labelId,r].join(\" \")},[t.labelId,r]),S=E(()=>({open:t.listboxState===0,disabled:t.disabled,value:t.value}),[t]),g={ref:u,id:r,type:ve(e,t.buttonRef),\"aria-haspopup\":\"listbox\",\"aria-controls\":(x=t.optionsRef.current)==null?void 0:x.id,\"aria-expanded\":t.listboxState===0,\"aria-labelledby\":P,disabled:t.disabled,onKeyDown:b,onKeyUp:R,onClick:m};return M({ourProps:g,theirProps:l,slot:S,defaultTag:We,name:\"Listbox.Button\"})}let $e=\"label\";function ze(e,a){let n=V(),{id:r=`headlessui-listbox-label-${n}`,...l}=e,t=w(\"Listbox.Label\"),p=k(\"Listbox.Label\"),u=_(t.labelRef,a);K(()=>p.registerLabel(r),[r]);let i=f(()=>{var m;return(m=t.buttonRef.current)==null?void 0:m.focus({preventScroll:!0})}),b=E(()=>({open:t.listboxState===0,disabled:t.disabled}),[t]);return M({ourProps:{ref:u,id:r,onClick:i},theirProps:l,slot:b,defaultTag:$e,name:\"Listbox.Label\"})}let Je=\"ul\",qe=ne.RenderStrategy|ne.Static;function Ye(e,a){var T;let n=V(),{id:r=`headlessui-listbox-options-${n}`,...l}=e,t=w(\"Listbox.Options\"),p=k(\"Listbox.Options\"),u=_(t.optionsRef,a),i=j(),b=j(),R=De(),m=(()=>R!==null?(R&Q.Open)===Q.Open:t.listboxState===0)();te(()=>{var L;let o=t.optionsRef.current;o&&t.listboxState===0&&o!==((L=we(o))==null?void 0:L.activeElement)&&o.focus({preventScroll:!0})},[t.listboxState,t.optionsRef]);let P=f(o=>{switch(b.dispose(),o.key){case y.Space:if(t.searchQuery!==\"\")return o.preventDefault(),o.stopPropagation(),p.search(o.key);case y.Enter:if(o.preventDefault(),o.stopPropagation(),t.activeOptionIndex!==null){let{dataRef:L}=t.options[t.activeOptionIndex];p.onChange(L.current.value)}t.mode===0&&(p.closeListbox(),$().nextFrame(()=>{var L;return(L=t.buttonRef.current)==null?void 0:L.focus({preventScroll:!0})}));break;case D(t.orientation,{vertical:y.ArrowDown,horizontal:y.ArrowRight}):return o.preventDefault(),o.stopPropagation(),p.goToOption(v.Next);case D(t.orientation,{vertical:y.ArrowUp,horizontal:y.ArrowLeft}):return o.preventDefault(),o.stopPropagation(),p.goToOption(v.Previous);case y.Home:case y.PageUp:return o.preventDefault(),o.stopPropagation(),p.goToOption(v.First);case y.End:case y.PageDown:return o.preventDefault(),o.stopPropagation(),p.goToOption(v.Last);case y.Escape:return o.preventDefault(),o.stopPropagation(),p.closeListbox(),i.nextFrame(()=>{var L;return(L=t.buttonRef.current)==null?void 0:L.focus({preventScroll:!0})});case y.Tab:o.preventDefault(),o.stopPropagation();break;default:o.key.length===1&&(p.search(o.key),b.setTimeout(()=>p.clearSearch(),350));break}}),S=oe(()=>{var o;return(o=t.buttonRef.current)==null?void 0:o.id},[t.buttonRef.current]),g=E(()=>({open:t.listboxState===0}),[t]),x={\"aria-activedescendant\":t.activeOptionIndex===null||(T=t.options[t.activeOptionIndex])==null?void 0:T.id,\"aria-multiselectable\":t.mode===1?!0:void 0,\"aria-labelledby\":S,\"aria-orientation\":t.orientation,id:r,onKeyDown:P,role:\"listbox\",tabIndex:0,ref:u};return M({ourProps:x,theirProps:l,slot:g,defaultTag:Je,features:qe,visible:m,name:\"Listbox.Options\"})}let Ze=\"li\";function et(e,a){let n=V(),{id:r=`headlessui-listbox-option-${n}`,disabled:l=!1,value:t,...p}=e,u=w(\"Listbox.Option\"),i=k(\"Listbox.Option\"),b=u.activeOptionIndex!==null?u.options[u.activeOptionIndex].id===r:!1,R=u.isSelected(t),m=h(null),P=Ae(m),S=me({disabled:l,value:t,domRef:m,get textValue(){return P()}}),g=_(a,m);K(()=>{if(u.listboxState!==0||!b||u.activationTrigger===0)return;let A=$();return A.requestAnimationFrame(()=>{var d,H;(H=(d=m.current)==null?void 0:d.scrollIntoView)==null||H.call(d,{block:\"nearest\"})}),A.dispose},[m,b,u.listboxState,u.activationTrigger,u.activeOptionIndex]),K(()=>i.registerOption(r,S),[S,r]);let x=f(A=>{if(l)return A.preventDefault();i.onChange(t),u.mode===0&&(i.closeListbox(),$().nextFrame(()=>{var d;return(d=u.buttonRef.current)==null?void 0:d.focus({preventScroll:!0})}))}),T=f(()=>{if(l)return i.goToOption(v.Nothing);i.goToOption(v.Specific,r)}),o=Se(),L=f(A=>o.update(A)),U=f(A=>{o.wasMoved(A)&&(l||b||i.goToOption(v.Specific,r,0))}),B=f(A=>{o.wasMoved(A)&&(l||b&&i.goToOption(v.Nothing))}),W=E(()=>({active:b,selected:R,disabled:l}),[b,R,l]);return M({ourProps:{id:r,ref:g,role:\"option\",tabIndex:l===!0?void 0:-1,\"aria-disabled\":l===!0?!0:void 0,\"aria-selected\":R,disabled:void 0,onClick:x,onFocus:T,onPointerEnter:L,onMouseEnter:L,onPointerMove:U,onMouseMove:U,onPointerLeave:B,onMouseLeave:B},theirProps:p,slot:W,defaultTag:Ze,name:\"Listbox.Option\"})}let tt=F(Qe),ot=F(Xe),nt=F(ze),it=F(Ye),rt=F(et),It=Object.assign(tt,{Button:ot,Label:nt,Options:it,Option:rt});export{It as Listbox};\n", "import{useRef as l}from\"react\";import{getTextValue as i}from'../utils/get-text-value.js';import{useEvent as o}from'./use-event.js';function s(c){let t=l(\"\"),r=l(\"\");return o(()=>{let e=c.current;if(!e)return\"\";let u=e.innerText;if(t.current===u)return r.current;let n=i(e).trim().toLowerCase();return t.current=u,r.current=n,n})}export{s as useTextValue};\n", "let a=/([\\u2700-\\u27BF]|[\\uE000-\\uF8FF]|\\uD83C[\\uDC00-\\uDFFF]|\\uD83D[\\uDC00-\\uDFFF]|[\\u2011-\\u26FF]|\\uD83E[\\uDD10-\\uDDFF])/g;function o(e){var r,i;let n=(r=e.innerText)!=null?r:\"\",t=e.cloneNode(!0);if(!(t instanceof HTMLElement))return n;let u=!1;for(let f of t.querySelectorAll('[hidden],[aria-hidden],[role=\"img\"]'))f.remove(),u=!0;let l=u?(i=t.innerText)!=null?i:\"\":n;return a.test(l)&&(l=l.replace(a,\"\")),l}function g(e){let n=e.getAttribute(\"aria-label\");if(typeof n==\"string\")return n.trim();let t=e.getAttribute(\"aria-labelledby\");if(t){let u=t.split(\" \").map(l=>{let r=document.getElementById(l);if(r){let i=r.getAttribute(\"aria-label\");return typeof i==\"string\"?i.trim():o(r).trim()}return null}).filter(Boolean);if(u.length>0)return u.join(\", \")}return o(e).trim()}export{g as getTextValue};\n", "import G,{createContext as X,createRef as N,Fragment as H,useContext as $,useEffect as q,useMemo as v,useReducer as z,useRef as K}from\"react\";import{useDisposables as j}from'../../hooks/use-disposables.js';import{useEvent as d}from'../../hooks/use-event.js';import{useId as O}from'../../hooks/use-id.js';import{useIsoMorphicEffect as L}from'../../hooks/use-iso-morphic-effect.js';import{useOutsideClick as Y}from'../../hooks/use-outside-click.js';import{useOwnerDocument as Z}from'../../hooks/use-owner.js';import{useResolveButtonType as ee}from'../../hooks/use-resolve-button-type.js';import{useSyncRefs as h}from'../../hooks/use-sync-refs.js';import{useTextValue as te}from'../../hooks/use-text-value.js';import{useTrackedPointer as ne}from'../../hooks/use-tracked-pointer.js';import{useTreeWalker as re}from'../../hooks/use-tree-walker.js';import{OpenClosedProvider as oe,State as D,useOpenClosed as ae}from'../../internal/open-closed.js';import{isDisabledReactIssue7711 as ie}from'../../utils/bugs.js';import{calculateActiveIndex as se,Focus as y}from'../../utils/calculate-active-index.js';import{disposables as k}from'../../utils/disposables.js';import{Focus as Q,FocusableMode as ue,focusFrom as le,isFocusableElement as pe,restoreFocusIfNecessary as W,sortByDomNode as ce}from'../../utils/focus-management.js';import{match as V}from'../../utils/match.js';import{Features as J,forwardRefWithAs as F,render as _}from'../../utils/render.js';import{Keys as c}from'../keyboard.js';var me=(r=>(r[r.Open=0]=\"Open\",r[r.Closed=1]=\"Closed\",r))(me||{}),de=(r=>(r[r.Pointer=0]=\"Pointer\",r[r.Other=1]=\"Other\",r))(de||{}),fe=(a=>(a[a.OpenMenu=0]=\"OpenMenu\",a[a.CloseMenu=1]=\"CloseMenu\",a[a.GoToItem=2]=\"GoToItem\",a[a.Search=3]=\"Search\",a[a.ClearSearch=4]=\"ClearSearch\",a[a.RegisterItem=5]=\"RegisterItem\",a[a.UnregisterItem=6]=\"UnregisterItem\",a))(fe||{});function w(e,u=r=>r){let r=e.activeItemIndex!==null?e.items[e.activeItemIndex]:null,s=ce(u(e.items.slice()),t=>t.dataRef.current.domRef.current),i=r?s.indexOf(r):null;return i===-1&&(i=null),{items:s,activeItemIndex:i}}let Te={[1](e){return e.menuState===1?e:{...e,activeItemIndex:null,menuState:1}},[0](e){return e.menuState===0?e:{...e,__demoMode:!1,menuState:0}},[2]:(e,u)=>{var i;let r=w(e),s=se(u,{resolveItems:()=>r.items,resolveActiveIndex:()=>r.activeItemIndex,resolveId:t=>t.id,resolveDisabled:t=>t.dataRef.current.disabled});return{...e,...r,searchQuery:\"\",activeItemIndex:s,activationTrigger:(i=u.trigger)!=null?i:1}},[3]:(e,u)=>{let s=e.searchQuery!==\"\"?0:1,i=e.searchQuery+u.value.toLowerCase(),o=(e.activeItemIndex!==null?e.items.slice(e.activeItemIndex+s).concat(e.items.slice(0,e.activeItemIndex+s)):e.items).find(l=>{var m;return((m=l.dataRef.current.textValue)==null?void 0:m.startsWith(i))&&!l.dataRef.current.disabled}),a=o?e.items.indexOf(o):-1;return a===-1||a===e.activeItemIndex?{...e,searchQuery:i}:{...e,searchQuery:i,activeItemIndex:a,activationTrigger:1}},[4](e){return e.searchQuery===\"\"?e:{...e,searchQuery:\"\",searchActiveItemIndex:null}},[5]:(e,u)=>{let r=w(e,s=>[...s,{id:u.id,dataRef:u.dataRef}]);return{...e,...r}},[6]:(e,u)=>{let r=w(e,s=>{let i=s.findIndex(t=>t.id===u.id);return i!==-1&&s.splice(i,1),s});return{...e,...r,activationTrigger:1}}},U=X(null);U.displayName=\"MenuContext\";function C(e){let u=$(U);if(u===null){let r=new Error(`<${e} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,C),r}return u}function ye(e,u){return V(u.type,Te,e,u)}let Ie=H;function Me(e,u){let{__demoMode:r=!1,...s}=e,i=z(ye,{__demoMode:r,menuState:r?0:1,buttonRef:N(),itemsRef:N(),items:[],searchQuery:\"\",activeItemIndex:null,activationTrigger:1}),[{menuState:t,itemsRef:o,buttonRef:a},l]=i,m=h(u);Y([a,o],(g,R)=>{var p;l({type:1}),pe(R,ue.Loose)||(g.preventDefault(),(p=a.current)==null||p.focus())},t===0);let I=d(()=>{l({type:1})}),A=v(()=>({open:t===0,close:I}),[t,I]),f={ref:m};return G.createElement(U.Provider,{value:i},G.createElement(oe,{value:V(t,{[0]:D.Open,[1]:D.Closed})},_({ourProps:f,theirProps:s,slot:A,defaultTag:Ie,name:\"Menu\"})))}let ge=\"button\";function Re(e,u){var R;let r=O(),{id:s=`headlessui-menu-button-${r}`,...i}=e,[t,o]=C(\"Menu.Button\"),a=h(t.buttonRef,u),l=j(),m=d(p=>{switch(p.key){case c.Space:case c.Enter:case c.ArrowDown:p.preventDefault(),p.stopPropagation(),o({type:0}),l.nextFrame(()=>o({type:2,focus:y.First}));break;case c.ArrowUp:p.preventDefault(),p.stopPropagation(),o({type:0}),l.nextFrame(()=>o({type:2,focus:y.Last}));break}}),I=d(p=>{switch(p.key){case c.Space:p.preventDefault();break}}),A=d(p=>{if(ie(p.currentTarget))return p.preventDefault();e.disabled||(t.menuState===0?(o({type:1}),l.nextFrame(()=>{var M;return(M=t.buttonRef.current)==null?void 0:M.focus({preventScroll:!0})})):(p.preventDefault(),o({type:0})))}),f=v(()=>({open:t.menuState===0}),[t]),g={ref:a,id:s,type:ee(e,t.buttonRef),\"aria-haspopup\":\"menu\",\"aria-controls\":(R=t.itemsRef.current)==null?void 0:R.id,\"aria-expanded\":t.menuState===0,onKeyDown:m,onKeyUp:I,onClick:A};return _({ourProps:g,theirProps:i,slot:f,defaultTag:ge,name:\"Menu.Button\"})}let Ae=\"div\",be=J.RenderStrategy|J.Static;function Ee(e,u){var M,b;let r=O(),{id:s=`headlessui-menu-items-${r}`,...i}=e,[t,o]=C(\"Menu.Items\"),a=h(t.itemsRef,u),l=Z(t.itemsRef),m=j(),I=ae(),A=(()=>I!==null?(I&D.Open)===D.Open:t.menuState===0)();q(()=>{let n=t.itemsRef.current;n&&t.menuState===0&&n!==(l==null?void 0:l.activeElement)&&n.focus({preventScroll:!0})},[t.menuState,t.itemsRef,l]),re({container:t.itemsRef.current,enabled:t.menuState===0,accept(n){return n.getAttribute(\"role\")===\"menuitem\"?NodeFilter.FILTER_REJECT:n.hasAttribute(\"role\")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(n){n.setAttribute(\"role\",\"none\")}});let f=d(n=>{var E,x;switch(m.dispose(),n.key){case c.Space:if(t.searchQuery!==\"\")return n.preventDefault(),n.stopPropagation(),o({type:3,value:n.key});case c.Enter:if(n.preventDefault(),n.stopPropagation(),o({type:1}),t.activeItemIndex!==null){let{dataRef:S}=t.items[t.activeItemIndex];(x=(E=S.current)==null?void 0:E.domRef.current)==null||x.click()}W(t.buttonRef.current);break;case c.ArrowDown:return n.preventDefault(),n.stopPropagation(),o({type:2,focus:y.Next});case c.ArrowUp:return n.preventDefault(),n.stopPropagation(),o({type:2,focus:y.Previous});case c.Home:case c.PageUp:return n.preventDefault(),n.stopPropagation(),o({type:2,focus:y.First});case c.End:case c.PageDown:return n.preventDefault(),n.stopPropagation(),o({type:2,focus:y.Last});case c.Escape:n.preventDefault(),n.stopPropagation(),o({type:1}),k().nextFrame(()=>{var S;return(S=t.buttonRef.current)==null?void 0:S.focus({preventScroll:!0})});break;case c.Tab:n.preventDefault(),n.stopPropagation(),o({type:1}),k().nextFrame(()=>{le(t.buttonRef.current,n.shiftKey?Q.Previous:Q.Next)});break;default:n.key.length===1&&(o({type:3,value:n.key}),m.setTimeout(()=>o({type:4}),350));break}}),g=d(n=>{switch(n.key){case c.Space:n.preventDefault();break}}),R=v(()=>({open:t.menuState===0}),[t]),p={\"aria-activedescendant\":t.activeItemIndex===null||(M=t.items[t.activeItemIndex])==null?void 0:M.id,\"aria-labelledby\":(b=t.buttonRef.current)==null?void 0:b.id,id:s,onKeyDown:f,onKeyUp:g,role:\"menu\",tabIndex:0,ref:a};return _({ourProps:p,theirProps:i,slot:R,defaultTag:Ae,features:be,visible:A,name:\"Menu.Items\"})}let Se=H;function xe(e,u){let r=O(),{id:s=`headlessui-menu-item-${r}`,disabled:i=!1,...t}=e,[o,a]=C(\"Menu.Item\"),l=o.activeItemIndex!==null?o.items[o.activeItemIndex].id===s:!1,m=K(null),I=h(u,m);L(()=>{if(o.__demoMode||o.menuState!==0||!l||o.activationTrigger===0)return;let T=k();return T.requestAnimationFrame(()=>{var P,B;(B=(P=m.current)==null?void 0:P.scrollIntoView)==null||B.call(P,{block:\"nearest\"})}),T.dispose},[o.__demoMode,m,l,o.menuState,o.activationTrigger,o.activeItemIndex]);let A=te(m),f=K({disabled:i,domRef:m,get textValue(){return A()}});L(()=>{f.current.disabled=i},[f,i]),L(()=>(a({type:5,id:s,dataRef:f}),()=>a({type:6,id:s})),[f,s]);let g=d(()=>{a({type:1})}),R=d(T=>{if(i)return T.preventDefault();a({type:1}),W(o.buttonRef.current)}),p=d(()=>{if(i)return a({type:2,focus:y.Nothing});a({type:2,focus:y.Specific,id:s})}),M=ne(),b=d(T=>M.update(T)),n=d(T=>{M.wasMoved(T)&&(i||l||a({type:2,focus:y.Specific,id:s,trigger:0}))}),E=d(T=>{M.wasMoved(T)&&(i||l&&a({type:2,focus:y.Nothing}))}),x=v(()=>({active:l,disabled:i,close:g}),[l,i,g]);return _({ourProps:{id:s,ref:I,role:\"menuitem\",tabIndex:i===!0?void 0:-1,\"aria-disabled\":i===!0?!0:void 0,disabled:void 0,onClick:R,onFocus:p,onPointerEnter:b,onMouseEnter:b,onPointerMove:n,onMouseMove:n,onPointerLeave:E,onMouseLeave:E},theirProps:t,slot:x,defaultTag:Se,name:\"Menu.Item\"})}let Pe=F(Me),ve=F(Re),he=F(Ee),De=F(xe),qe=Object.assign(Pe,{Button:ve,Items:he,Item:De});export{qe as Menu};\n", "import C,{createContext as Q,createRef as de,useContext as Z,useEffect as ee,useMemo as h,useReducer as ge,useRef as J,useState as ce}from\"react\";import{useNestedPortals as Se}from'../../components/portal/portal.js';import{useEvent as R}from'../../hooks/use-event.js';import{useEventListener as Re}from'../../hooks/use-event-listener.js';import{useId as K}from'../../hooks/use-id.js';import{useIsoMorphicEffect as Ae}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as ve}from'../../hooks/use-latest-value.js';import{useOutsideClick as Oe}from'../../hooks/use-outside-click.js';import{useOwnerDocument as ne}from'../../hooks/use-owner.js';import{useResolveButtonType as Ce}from'../../hooks/use-resolve-button-type.js';import{useMainTreeNode as Me,useRootContainers as xe}from'../../hooks/use-root-containers.js';import{optionalRef as Fe,useSyncRefs as j}from'../../hooks/use-sync-refs.js';import{Direction as H,useTabDirection as Te}from'../../hooks/use-tab-direction.js';import{Features as le,Hidden as ae}from'../../internal/hidden.js';import{OpenClosedProvider as Ie,State as V,useOpenClosed as me}from'../../internal/open-closed.js';import{isDisabledReactIssue7711 as ye}from'../../utils/bugs.js';import{Focus as G,FocusableMode as _e,focusIn as N,FocusResult as pe,getFocusableElements as se,isFocusableElement as Le}from'../../utils/focus-management.js';import{match as k}from'../../utils/match.js';import'../../utils/micro-task.js';import{getOwnerDocument as Be}from'../../utils/owner.js';import{Features as te,forwardRefWithAs as X,render as Y,useMergeRefsFn as De}from'../../utils/render.js';import{Keys as w}from'../keyboard.js';var he=(u=>(u[u.Open=0]=\"Open\",u[u.Closed=1]=\"Closed\",u))(he||{}),He=(e=>(e[e.TogglePopover=0]=\"TogglePopover\",e[e.ClosePopover=1]=\"ClosePopover\",e[e.SetButton=2]=\"SetButton\",e[e.SetButtonId=3]=\"SetButtonId\",e[e.SetPanel=4]=\"SetPanel\",e[e.SetPanelId=5]=\"SetPanelId\",e))(He||{});let Ge={[0]:t=>{let o={...t,popoverState:k(t.popoverState,{[0]:1,[1]:0})};return o.popoverState===0&&(o.__demoMode=!1),o},[1](t){return t.popoverState===1?t:{...t,popoverState:1}},[2](t,o){return t.button===o.button?t:{...t,button:o.button}},[3](t,o){return t.buttonId===o.buttonId?t:{...t,buttonId:o.buttonId}},[4](t,o){return t.panel===o.panel?t:{...t,panel:o.panel}},[5](t,o){return t.panelId===o.panelId?t:{...t,panelId:o.panelId}}},ue=Q(null);ue.displayName=\"PopoverContext\";function oe(t){let o=Z(ue);if(o===null){let u=new Error(`<${t} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(u,oe),u}return o}let ie=Q(null);ie.displayName=\"PopoverAPIContext\";function fe(t){let o=Z(ie);if(o===null){let u=new Error(`<${t} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(u,fe),u}return o}let Pe=Q(null);Pe.displayName=\"PopoverGroupContext\";function Ee(){return Z(Pe)}let re=Q(null);re.displayName=\"PopoverPanelContext\";function Ne(){return Z(re)}function ke(t,o){return k(o.type,Ge,t,o)}let we=\"div\";function Ue(t,o){var B;let{__demoMode:u=!1,...M}=t,x=J(null),n=j(o,Fe(l=>{x.current=l})),e=J([]),c=ge(ke,{__demoMode:u,popoverState:u?0:1,buttons:e,button:null,buttonId:null,panel:null,panelId:null,beforePanelSentinel:de(),afterPanelSentinel:de()}),[{popoverState:f,button:s,buttonId:I,panel:a,panelId:v,beforePanelSentinel:y,afterPanelSentinel:A},P]=c,p=ne((B=x.current)!=null?B:s),E=h(()=>{if(!s||!a)return!1;for(let W of document.querySelectorAll(\"body > *\"))if(Number(W==null?void 0:W.contains(s))^Number(W==null?void 0:W.contains(a)))return!0;let l=se(),S=l.indexOf(s),q=(S+l.length-1)%l.length,U=(S+1)%l.length,z=l[q],be=l[U];return!a.contains(z)&&!a.contains(be)},[s,a]),F=ve(I),D=ve(v),_=h(()=>({buttonId:F,panelId:D,close:()=>P({type:1})}),[F,D,P]),O=Ee(),L=O==null?void 0:O.registerPopover,$=R(()=>{var l;return(l=O==null?void 0:O.isFocusWithinPopoverGroup())!=null?l:(p==null?void 0:p.activeElement)&&((s==null?void 0:s.contains(p.activeElement))||(a==null?void 0:a.contains(p.activeElement)))});ee(()=>L==null?void 0:L(_),[L,_]);let[i,b]=Se(),T=xe({mainTreeNodeRef:O==null?void 0:O.mainTreeNodeRef,portals:i,defaultContainers:[s,a]});Re(p==null?void 0:p.defaultView,\"focus\",l=>{var S,q,U,z;l.target!==window&&l.target instanceof HTMLElement&&f===0&&($()||s&&a&&(T.contains(l.target)||(q=(S=y.current)==null?void 0:S.contains)!=null&&q.call(S,l.target)||(z=(U=A.current)==null?void 0:U.contains)!=null&&z.call(U,l.target)||P({type:1})))},!0),Oe(T.resolveContainers,(l,S)=>{P({type:1}),Le(S,_e.Loose)||(l.preventDefault(),s==null||s.focus())},f===0);let d=R(l=>{P({type:1});let S=(()=>l?l instanceof HTMLElement?l:\"current\"in l&&l.current instanceof HTMLElement?l.current:s:s)();S==null||S.focus()}),r=h(()=>({close:d,isPortalled:E}),[d,E]),m=h(()=>({open:f===0,close:d}),[f,d]),g={ref:n};return C.createElement(re.Provider,{value:null},C.createElement(ue.Provider,{value:c},C.createElement(ie.Provider,{value:r},C.createElement(Ie,{value:k(f,{[0]:V.Open,[1]:V.Closed})},C.createElement(b,null,Y({ourProps:g,theirProps:M,slot:m,defaultTag:we,name:\"Popover\"}),C.createElement(T.MainTreeNode,null))))))}let We=\"button\";function Ke(t,o){let u=K(),{id:M=`headlessui-popover-button-${u}`,...x}=t,[n,e]=oe(\"Popover.Button\"),{isPortalled:c}=fe(\"Popover.Button\"),f=J(null),s=`headlessui-focus-sentinel-${K()}`,I=Ee(),a=I==null?void 0:I.closeOthers,y=Ne()!==null;ee(()=>{if(!y)return e({type:3,buttonId:M}),()=>{e({type:3,buttonId:null})}},[y,M,e]);let[A]=ce(()=>Symbol()),P=j(f,o,y?null:r=>{if(r)n.buttons.current.push(A);else{let m=n.buttons.current.indexOf(A);m!==-1&&n.buttons.current.splice(m,1)}n.buttons.current.length>1&&console.warn(\"You are already using a <Popover.Button /> but only 1 <Popover.Button /> is supported.\"),r&&e({type:2,button:r})}),p=j(f,o),E=ne(f),F=R(r=>{var m,g,B;if(y){if(n.popoverState===1)return;switch(r.key){case w.Space:case w.Enter:r.preventDefault(),(g=(m=r.target).click)==null||g.call(m),e({type:1}),(B=n.button)==null||B.focus();break}}else switch(r.key){case w.Space:case w.Enter:r.preventDefault(),r.stopPropagation(),n.popoverState===1&&(a==null||a(n.buttonId)),e({type:0});break;case w.Escape:if(n.popoverState!==0)return a==null?void 0:a(n.buttonId);if(!f.current||E!=null&&E.activeElement&&!f.current.contains(E.activeElement))return;r.preventDefault(),r.stopPropagation(),e({type:1});break}}),D=R(r=>{y||r.key===w.Space&&r.preventDefault()}),_=R(r=>{var m,g;ye(r.currentTarget)||t.disabled||(y?(e({type:1}),(m=n.button)==null||m.focus()):(r.preventDefault(),r.stopPropagation(),n.popoverState===1&&(a==null||a(n.buttonId)),e({type:0}),(g=n.button)==null||g.focus()))}),O=R(r=>{r.preventDefault(),r.stopPropagation()}),L=n.popoverState===0,$=h(()=>({open:L}),[L]),i=Ce(t,f),b=y?{ref:p,type:i,onKeyDown:F,onClick:_}:{ref:P,id:n.buttonId,type:i,\"aria-expanded\":n.popoverState===0,\"aria-controls\":n.panel?n.panelId:void 0,onKeyDown:F,onKeyUp:D,onClick:_,onMouseDown:O},T=Te(),d=R(()=>{let r=n.panel;if(!r)return;function m(){k(T.current,{[H.Forwards]:()=>N(r,G.First),[H.Backwards]:()=>N(r,G.Last)})===pe.Error&&N(se().filter(B=>B.dataset.headlessuiFocusGuard!==\"true\"),k(T.current,{[H.Forwards]:G.Next,[H.Backwards]:G.Previous}),{relativeTo:n.button})}m()});return C.createElement(C.Fragment,null,Y({ourProps:b,theirProps:x,slot:$,defaultTag:We,name:\"Popover.Button\"}),L&&!y&&c&&C.createElement(ae,{id:s,features:le.Focusable,\"data-headlessui-focus-guard\":!0,as:\"button\",type:\"button\",onFocus:d}))}let je=\"div\",Ve=te.RenderStrategy|te.Static;function $e(t,o){let u=K(),{id:M=`headlessui-popover-overlay-${u}`,...x}=t,[{popoverState:n},e]=oe(\"Popover.Overlay\"),c=j(o),f=me(),s=(()=>f!==null?(f&V.Open)===V.Open:n===0)(),I=R(y=>{if(ye(y.currentTarget))return y.preventDefault();e({type:1})}),a=h(()=>({open:n===0}),[n]);return Y({ourProps:{ref:c,id:M,\"aria-hidden\":!0,onClick:I},theirProps:x,slot:a,defaultTag:je,features:Ve,visible:s,name:\"Popover.Overlay\"})}let Je=\"div\",Xe=te.RenderStrategy|te.Static;function Ye(t,o){let u=K(),{id:M=`headlessui-popover-panel-${u}`,focus:x=!1,...n}=t,[e,c]=oe(\"Popover.Panel\"),{close:f,isPortalled:s}=fe(\"Popover.Panel\"),I=`headlessui-focus-sentinel-before-${K()}`,a=`headlessui-focus-sentinel-after-${K()}`,v=J(null),y=j(v,o,i=>{c({type:4,panel:i})}),A=ne(v),P=De();Ae(()=>(c({type:5,panelId:M}),()=>{c({type:5,panelId:null})}),[M,c]);let p=me(),E=(()=>p!==null?(p&V.Open)===V.Open:e.popoverState===0)(),F=R(i=>{var b;switch(i.key){case w.Escape:if(e.popoverState!==0||!v.current||A!=null&&A.activeElement&&!v.current.contains(A.activeElement))return;i.preventDefault(),i.stopPropagation(),c({type:1}),(b=e.button)==null||b.focus();break}});ee(()=>{var i;t.static||e.popoverState===1&&((i=t.unmount)==null||i)&&c({type:4,panel:null})},[e.popoverState,t.unmount,t.static,c]),ee(()=>{if(e.__demoMode||!x||e.popoverState!==0||!v.current)return;let i=A==null?void 0:A.activeElement;v.current.contains(i)||N(v.current,G.First)},[e.__demoMode,x,v,e.popoverState]);let D=h(()=>({open:e.popoverState===0,close:f}),[e,f]),_={ref:y,id:M,onKeyDown:F,onBlur:x&&e.popoverState===0?i=>{var T,d,r,m,g;let b=i.relatedTarget;b&&v.current&&((T=v.current)!=null&&T.contains(b)||(c({type:1}),((r=(d=e.beforePanelSentinel.current)==null?void 0:d.contains)!=null&&r.call(d,b)||(g=(m=e.afterPanelSentinel.current)==null?void 0:m.contains)!=null&&g.call(m,b))&&b.focus({preventScroll:!0})))}:void 0,tabIndex:-1},O=Te(),L=R(()=>{let i=v.current;if(!i)return;function b(){k(O.current,{[H.Forwards]:()=>{var d;N(i,G.First)===pe.Error&&((d=e.afterPanelSentinel.current)==null||d.focus())},[H.Backwards]:()=>{var T;(T=e.button)==null||T.focus({preventScroll:!0})}})}b()}),$=R(()=>{let i=v.current;if(!i)return;function b(){k(O.current,{[H.Forwards]:()=>{var B;if(!e.button)return;let T=se(),d=T.indexOf(e.button),r=T.slice(0,d+1),g=[...T.slice(d+1),...r];for(let l of g.slice())if(l.dataset.headlessuiFocusGuard===\"true\"||(B=e.panel)!=null&&B.contains(l)){let S=g.indexOf(l);S!==-1&&g.splice(S,1)}N(g,G.First,{sorted:!1})},[H.Backwards]:()=>{var d;N(i,G.Previous)===pe.Error&&((d=e.button)==null||d.focus())}})}b()});return C.createElement(re.Provider,{value:M},E&&s&&C.createElement(ae,{id:I,ref:e.beforePanelSentinel,features:le.Focusable,\"data-headlessui-focus-guard\":!0,as:\"button\",type:\"button\",onFocus:L}),Y({mergeRefs:P,ourProps:_,theirProps:n,slot:D,defaultTag:Je,features:Xe,visible:E,name:\"Popover.Panel\"}),E&&s&&C.createElement(ae,{id:a,ref:e.afterPanelSentinel,features:le.Focusable,\"data-headlessui-focus-guard\":!0,as:\"button\",type:\"button\",onFocus:$}))}let qe=\"div\";function ze(t,o){let u=J(null),M=j(u,o),[x,n]=ce([]),e=Me(),c=R(P=>{n(p=>{let E=p.indexOf(P);if(E!==-1){let F=p.slice();return F.splice(E,1),F}return p})}),f=R(P=>(n(p=>[...p,P]),()=>c(P))),s=R(()=>{var E;let P=Be(u);if(!P)return!1;let p=P.activeElement;return(E=u.current)!=null&&E.contains(p)?!0:x.some(F=>{var D,_;return((D=P.getElementById(F.buttonId.current))==null?void 0:D.contains(p))||((_=P.getElementById(F.panelId.current))==null?void 0:_.contains(p))})}),I=R(P=>{for(let p of x)p.buttonId.current!==P&&p.close()}),a=h(()=>({registerPopover:f,unregisterPopover:c,isFocusWithinPopoverGroup:s,closeOthers:I,mainTreeNodeRef:e.mainTreeNodeRef}),[f,c,s,I,e.mainTreeNodeRef]),v=h(()=>({}),[]),y=t,A={ref:M};return C.createElement(Pe.Provider,{value:a},Y({ourProps:A,theirProps:y,slot:v,defaultTag:qe,name:\"Popover.Group\"}),C.createElement(e.MainTreeNode,null))}let Qe=X(Ue),Ze=X(Ke),et=X($e),tt=X(Ye),ot=X(ze),Ct=Object.assign(Qe,{Button:Ze,Overlay:et,Panel:tt,Group:ot});export{Ct as Popover};\n", "import O,{createContext as J,useContext as V,useEffect as se,useMemo as D,useReducer as ue,useRef as j}from\"react\";import{Description as de,useDescriptions as X}from'../../components/description/description.js';import{Keys as _}from'../../components/keyboard.js';import{Label as ce,useLabels as q}from'../../components/label/label.js';import{useControllable as fe}from'../../hooks/use-controllable.js';import{useDisposables as Te}from'../../hooks/use-disposables.js';import{useEvent as E}from'../../hooks/use-event.js';import{useFlags as me}from'../../hooks/use-flags.js';import{useId as Q}from'../../hooks/use-id.js';import{useIsoMorphicEffect as ye}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as Re}from'../../hooks/use-latest-value.js';import{useSyncRefs as Y}from'../../hooks/use-sync-refs.js';import{useTreeWalker as be}from'../../hooks/use-tree-walker.js';import{Features as ge,Hidden as Oe}from'../../internal/hidden.js';import{isDisabledReactIssue7711 as Z}from'../../utils/bugs.js';import{Focus as S,focusIn as z,FocusResult as ee,sortByDomNode as Ee}from'../../utils/focus-management.js';import{attemptSubmit as ve,objectToFormEntries as Pe}from'../../utils/form.js';import{match as Ae}from'../../utils/match.js';import{getOwnerDocument as De}from'../../utils/owner.js';import{compact as _e,forwardRefWithAs as te,render as re}from'../../utils/render.js';var Ge=(t=>(t[t.RegisterOption=0]=\"RegisterOption\",t[t.UnregisterOption=1]=\"UnregisterOption\",t))(Ge||{});let Ce={[0](o,r){let t=[...o.options,{id:r.id,element:r.element,propsRef:r.propsRef}];return{...o,options:Ee(t,p=>p.element.current)}},[1](o,r){let t=o.options.slice(),p=o.options.findIndex(T=>T.id===r.id);return p===-1?o:(t.splice(p,1),{...o,options:t})}},B=J(null);B.displayName=\"RadioGroupDataContext\";function oe(o){let r=V(B);if(r===null){let t=new Error(`<${o} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,oe),t}return r}let $=J(null);$.displayName=\"RadioGroupActionsContext\";function ne(o){let r=V($);if(r===null){let t=new Error(`<${o} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,ne),t}return r}function ke(o,r){return Ae(r.type,Ce,o,r)}let Le=\"div\";function he(o,r){let t=Q(),{id:p=`headlessui-radiogroup-${t}`,value:T,defaultValue:v,form:M,name:m,onChange:H,by:G=(e,i)=>e===i,disabled:P=!1,...N}=o,y=E(typeof G==\"string\"?(e,i)=>{let n=G;return(e==null?void 0:e[n])===(i==null?void 0:i[n])}:G),[A,L]=ue(ke,{options:[]}),a=A.options,[h,R]=q(),[C,U]=X(),k=j(null),W=Y(k,r),[l,s]=fe(T,H,v),b=D(()=>a.find(e=>!e.propsRef.current.disabled),[a]),x=D(()=>a.some(e=>y(e.propsRef.current.value,l)),[a,l]),d=E(e=>{var n;if(P||y(e,l))return!1;let i=(n=a.find(f=>y(f.propsRef.current.value,e)))==null?void 0:n.propsRef.current;return i!=null&&i.disabled?!1:(s==null||s(e),!0)});be({container:k.current,accept(e){return e.getAttribute(\"role\")===\"radio\"?NodeFilter.FILTER_REJECT:e.hasAttribute(\"role\")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(e){e.setAttribute(\"role\",\"none\")}});let F=E(e=>{let i=k.current;if(!i)return;let n=De(i),f=a.filter(u=>u.propsRef.current.disabled===!1).map(u=>u.element.current);switch(e.key){case _.Enter:ve(e.currentTarget);break;case _.ArrowLeft:case _.ArrowUp:if(e.preventDefault(),e.stopPropagation(),z(f,S.Previous|S.WrapAround)===ee.Success){let g=a.find(K=>K.element.current===(n==null?void 0:n.activeElement));g&&d(g.propsRef.current.value)}break;case _.ArrowRight:case _.ArrowDown:if(e.preventDefault(),e.stopPropagation(),z(f,S.Next|S.WrapAround)===ee.Success){let g=a.find(K=>K.element.current===(n==null?void 0:n.activeElement));g&&d(g.propsRef.current.value)}break;case _.Space:{e.preventDefault(),e.stopPropagation();let u=a.find(g=>g.element.current===(n==null?void 0:n.activeElement));u&&d(u.propsRef.current.value)}break}}),c=E(e=>(L({type:0,...e}),()=>L({type:1,id:e.id}))),w=D(()=>({value:l,firstOption:b,containsCheckedOption:x,disabled:P,compare:y,...A}),[l,b,x,P,y,A]),ie=D(()=>({registerOption:c,change:d}),[c,d]),ae={ref:W,id:p,role:\"radiogroup\",\"aria-labelledby\":h,\"aria-describedby\":C,onKeyDown:F},pe=D(()=>({value:l}),[l]),I=j(null),le=Te();return se(()=>{I.current&&v!==void 0&&le.addEventListener(I.current,\"reset\",()=>{d(v)})},[I,d]),O.createElement(U,{name:\"RadioGroup.Description\"},O.createElement(R,{name:\"RadioGroup.Label\"},O.createElement($.Provider,{value:ie},O.createElement(B.Provider,{value:w},m!=null&&l!=null&&Pe({[m]:l}).map(([e,i],n)=>O.createElement(Oe,{features:ge.Hidden,ref:n===0?f=>{var u;I.current=(u=f==null?void 0:f.closest(\"form\"))!=null?u:null}:void 0,..._e({key:e,as:\"input\",type:\"radio\",checked:i!=null,hidden:!0,readOnly:!0,form:M,disabled:P,name:e,value:i})})),re({ourProps:ae,theirProps:N,slot:pe,defaultTag:Le,name:\"RadioGroup\"})))))}var xe=(t=>(t[t.Empty=1]=\"Empty\",t[t.Active=2]=\"Active\",t))(xe||{});let Fe=\"div\";function we(o,r){var F;let t=Q(),{id:p=`headlessui-radiogroup-option-${t}`,value:T,disabled:v=!1,...M}=o,m=j(null),H=Y(m,r),[G,P]=q(),[N,y]=X(),{addFlag:A,removeFlag:L,hasFlag:a}=me(1),h=Re({value:T,disabled:v}),R=oe(\"RadioGroup.Option\"),C=ne(\"RadioGroup.Option\");ye(()=>C.registerOption({id:p,element:m,propsRef:h}),[p,C,m,h]);let U=E(c=>{var w;if(Z(c.currentTarget))return c.preventDefault();C.change(T)&&(A(2),(w=m.current)==null||w.focus())}),k=E(c=>{if(Z(c.currentTarget))return c.preventDefault();A(2)}),W=E(()=>L(2)),l=((F=R.firstOption)==null?void 0:F.id)===p,s=R.disabled||v,b=R.compare(R.value,T),x={ref:H,id:p,role:\"radio\",\"aria-checked\":b?\"true\":\"false\",\"aria-labelledby\":G,\"aria-describedby\":N,\"aria-disabled\":s?!0:void 0,tabIndex:(()=>s?-1:b||!R.containsCheckedOption&&l?0:-1)(),onClick:s?void 0:U,onFocus:s?void 0:k,onBlur:s?void 0:W},d=D(()=>({checked:b,disabled:s,active:a(2)}),[b,s,a]);return O.createElement(y,{name:\"RadioGroup.Description\"},O.createElement(P,{name:\"RadioGroup.Label\"},re({ourProps:x,theirProps:M,slot:d,defaultTag:Fe,name:\"RadioGroup.Option\"})))}let Ie=te(he),Se=te(we),it=Object.assign(Ie,{Option:Se,Label:ce,Description:de});export{it as RadioGroup};\n", "import c,{createContext as m,useContext as L,useMemo as f,useState as b}from\"react\";import{useEvent as T}from'../../hooks/use-event.js';import{useId as y}from'../../hooks/use-id.js';import{useIsoMorphicEffect as E}from'../../hooks/use-iso-morphic-effect.js';import{useSyncRefs as g}from'../../hooks/use-sync-refs.js';import{forwardRefWithAs as x,render as P}from'../../utils/render.js';let d=m(null);function u(){let a=L(d);if(a===null){let t=new Error(\"You used a <Label /> component, but it is not inside a relevant parent.\");throw Error.captureStackTrace&&Error.captureStackTrace(t,u),t}return a}function F(){let[a,t]=b([]);return[a.length>0?a.join(\" \"):void 0,f(()=>function(e){let s=T(r=>(t(l=>[...l,r]),()=>t(l=>{let n=l.slice(),p=n.indexOf(r);return p!==-1&&n.splice(p,1),n}))),o=f(()=>({register:s,slot:e.slot,name:e.name,props:e.props}),[s,e.slot,e.name,e.props]);return c.createElement(d.Provider,{value:o},e.children)},[t])]}let A=\"label\";function h(a,t){let i=y(),{id:e=`headlessui-label-${i}`,passive:s=!1,...o}=a,r=u(),l=g(t);E(()=>r.register(e),[e,r.register]);let n={ref:l,...r.props,id:e};return s&&(\"onClick\"in n&&(delete n.htmlFor,delete n.onClick),\"onClick\"in o&&delete o.onClick),P({ourProps:n,theirProps:o,slot:r.slot||{},defaultTag:A,name:r.name||\"Label\"})}let v=x(h),B=Object.assign(v,{});export{B as Label,F as useLabels};\n", "import{useCallback as n,useState as f}from\"react\";import{useIsMounted as i}from'./use-is-mounted.js';function c(a=0){let[l,r]=f(a),t=i(),o=n(e=>{t.current&&r(u=>u|e)},[l,t]),m=n(e=>Boolean(l&e),[l]),s=n(e=>{t.current&&r(u=>u&~e)},[r,t]),g=n(e=>{t.current&&r(u=>u^e)},[r]);return{flags:l,addFlag:o,hasFlag:m,removeFlag:s,toggleFlag:g}}export{c as useFlags};\n", "import l,{createContext as A,Fragment as H,useContext as F,useEffect as M,useMemo as P,useRef as U,useState as I}from\"react\";import{useControllable as K}from'../../hooks/use-controllable.js';import{useDisposables as B}from'../../hooks/use-disposables.js';import{useEvent as h}from'../../hooks/use-event.js';import{useId as O}from'../../hooks/use-id.js';import{useResolveButtonType as W}from'../../hooks/use-resolve-button-type.js';import{useSyncRefs as N}from'../../hooks/use-sync-refs.js';import{Features as J,Hidden as X}from'../../internal/hidden.js';import{isDisabledReactIssue7711 as j}from'../../utils/bugs.js';import{attemptSubmit as $}from'../../utils/form.js';import{compact as q,forwardRefWithAs as z,render as g}from'../../utils/render.js';import{Description as Q,useDescriptions as V}from'../description/description.js';import{Keys as D}from'../keyboard.js';import{Label as Y,useLabels as Z}from'../label/label.js';let S=A(null);S.displayName=\"GroupContext\";let ee=H;function te(r){var u;let[n,p]=I(null),[c,T]=Z(),[o,b]=V(),a=P(()=>({switch:n,setSwitch:p,labelledby:c,describedby:o}),[n,p,c,o]),d={},y=r;return l.createElement(b,{name:\"Switch.Description\"},l.createElement(T,{name:\"Switch.Label\",props:{htmlFor:(u=a.switch)==null?void 0:u.id,onClick(m){n&&(m.currentTarget.tagName===\"LABEL\"&&m.preventDefault(),n.click(),n.focus({preventScroll:!0}))}}},l.createElement(S.Provider,{value:a},g({ourProps:d,theirProps:y,defaultTag:ee,name:\"Switch.Group\"}))))}let ne=\"button\";function re(r,n){var E;let p=O(),{id:c=`headlessui-switch-${p}`,checked:T,defaultChecked:o=!1,onChange:b,disabled:a=!1,name:d,value:y,form:u,...m}=r,t=F(S),f=U(null),C=N(f,n,t===null?null:t.setSwitch),[i,s]=K(T,b,o),w=h(()=>s==null?void 0:s(!i)),L=h(e=>{if(j(e.currentTarget))return e.preventDefault();e.preventDefault(),w()}),x=h(e=>{e.key===D.Space?(e.preventDefault(),w()):e.key===D.Enter&&$(e.currentTarget)}),v=h(e=>e.preventDefault()),G=P(()=>({checked:i}),[i]),R={id:c,ref:C,role:\"switch\",type:W(r,f),tabIndex:r.tabIndex===-1?0:(E=r.tabIndex)!=null?E:0,\"aria-checked\":i,\"aria-labelledby\":t==null?void 0:t.labelledby,\"aria-describedby\":t==null?void 0:t.describedby,disabled:a,onClick:L,onKeyUp:x,onKeyPress:v},k=B();return M(()=>{var _;let e=(_=f.current)==null?void 0:_.closest(\"form\");e&&o!==void 0&&k.addEventListener(e,\"reset\",()=>{s(o)})},[f,s]),l.createElement(l.Fragment,null,d!=null&&i&&l.createElement(X,{features:J.Hidden,...q({as:\"input\",type:\"checkbox\",hidden:!0,readOnly:!0,disabled:a,form:u,checked:i,name:d,value:y})}),g({ourProps:R,theirProps:m,slot:G,defaultTag:ne,name:\"Switch\"}))}let oe=z(re),ie=te,_e=Object.assign(oe,{Group:ie,Label:Y,Description:Q});export{_e as Switch};\n", "import C,{createContext as V,Fragment as ne,useContext as Q,useMemo as I,useReducer as re,useRef as J}from\"react\";import{Keys as y}from'../../components/keyboard.js';import{useEvent as _}from'../../hooks/use-event.js';import{useId as Y}from'../../hooks/use-id.js';import{useIsoMorphicEffect as N}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as B}from'../../hooks/use-latest-value.js';import{useResolveButtonType as ae}from'../../hooks/use-resolve-button-type.js';import{useSyncRefs as w}from'../../hooks/use-sync-refs.js';import{FocusSentinel as le}from'../../internal/focus-sentinel.js';import{Hidden as oe}from'../../internal/hidden.js';import{Focus as x,focusIn as D,FocusResult as j,sortByDomNode as v}from'../../utils/focus-management.js';import{match as G}from'../../utils/match.js';import{microTask as se}from'../../utils/micro-task.js';import{getOwnerDocument as ie}from'../../utils/owner.js';import{Features as Z,forwardRefWithAs as H,render as U}from'../../utils/render.js';import{StableCollection as pe,useStableCollectionIndex as ee}from'../../utils/stable-collection.js';var ue=(t=>(t[t.Forwards=0]=\"Forwards\",t[t.Backwards=1]=\"Backwards\",t))(ue||{}),Te=(l=>(l[l.Less=-1]=\"Less\",l[l.Equal=0]=\"Equal\",l[l.Greater=1]=\"Greater\",l))(Te||{}),de=(a=>(a[a.SetSelectedIndex=0]=\"SetSelectedIndex\",a[a.RegisterTab=1]=\"RegisterTab\",a[a.UnregisterTab=2]=\"UnregisterTab\",a[a.RegisterPanel=3]=\"RegisterPanel\",a[a.UnregisterPanel=4]=\"UnregisterPanel\",a))(de||{});let ce={[0](e,n){var i;let t=v(e.tabs,c=>c.current),l=v(e.panels,c=>c.current),o=t.filter(c=>{var p;return!((p=c.current)!=null&&p.hasAttribute(\"disabled\"))}),a={...e,tabs:t,panels:l};if(n.index<0||n.index>t.length-1){let c=G(Math.sign(n.index-e.selectedIndex),{[-1]:()=>1,[0]:()=>G(Math.sign(n.index),{[-1]:()=>0,[0]:()=>0,[1]:()=>1}),[1]:()=>0});if(o.length===0)return a;let p=G(c,{[0]:()=>t.indexOf(o[0]),[1]:()=>t.indexOf(o[o.length-1])});return{...a,selectedIndex:p===-1?e.selectedIndex:p}}let T=t.slice(0,n.index),m=[...t.slice(n.index),...T].find(c=>o.includes(c));if(!m)return a;let b=(i=t.indexOf(m))!=null?i:e.selectedIndex;return b===-1&&(b=e.selectedIndex),{...a,selectedIndex:b}},[1](e,n){if(e.tabs.includes(n.tab))return e;let t=e.tabs[e.selectedIndex],l=v([...e.tabs,n.tab],a=>a.current),o=e.selectedIndex;return e.info.current.isControlled||(o=l.indexOf(t),o===-1&&(o=e.selectedIndex)),{...e,tabs:l,selectedIndex:o}},[2](e,n){return{...e,tabs:e.tabs.filter(t=>t!==n.tab)}},[3](e,n){return e.panels.includes(n.panel)?e:{...e,panels:v([...e.panels,n.panel],t=>t.current)}},[4](e,n){return{...e,panels:e.panels.filter(t=>t!==n.panel)}}},X=V(null);X.displayName=\"TabsDataContext\";function F(e){let n=Q(X);if(n===null){let t=new Error(`<${e} /> is missing a parent <Tab.Group /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,F),t}return n}let $=V(null);$.displayName=\"TabsActionsContext\";function q(e){let n=Q($);if(n===null){let t=new Error(`<${e} /> is missing a parent <Tab.Group /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,q),t}return n}function fe(e,n){return G(n.type,ce,e,n)}let be=ne;function me(e,n){let{defaultIndex:t=0,vertical:l=!1,manual:o=!1,onChange:a,selectedIndex:T=null,...R}=e;const m=l?\"vertical\":\"horizontal\",b=o?\"manual\":\"auto\";let i=T!==null,c=B({isControlled:i}),p=w(n),[u,f]=re(fe,{info:c,selectedIndex:T!=null?T:t,tabs:[],panels:[]}),P=I(()=>({selectedIndex:u.selectedIndex}),[u.selectedIndex]),g=B(a||(()=>{})),E=B(u.tabs),L=I(()=>({orientation:m,activation:b,...u}),[m,b,u]),A=_(s=>(f({type:1,tab:s}),()=>f({type:2,tab:s}))),S=_(s=>(f({type:3,panel:s}),()=>f({type:4,panel:s}))),k=_(s=>{h.current!==s&&g.current(s),i||f({type:0,index:s})}),h=B(i?e.selectedIndex:u.selectedIndex),W=I(()=>({registerTab:A,registerPanel:S,change:k}),[]);N(()=>{f({type:0,index:T!=null?T:t})},[T]),N(()=>{if(h.current===void 0||u.tabs.length<=0)return;let s=v(u.tabs,d=>d.current);s.some((d,M)=>u.tabs[M]!==d)&&k(s.indexOf(u.tabs[h.current]))});let O={ref:p};return C.createElement(pe,null,C.createElement($.Provider,{value:W},C.createElement(X.Provider,{value:L},L.tabs.length<=0&&C.createElement(le,{onFocus:()=>{var s,r;for(let d of E.current)if(((s=d.current)==null?void 0:s.tabIndex)===0)return(r=d.current)==null||r.focus(),!0;return!1}}),U({ourProps:O,theirProps:R,slot:P,defaultTag:be,name:\"Tabs\"}))))}let Pe=\"div\";function ye(e,n){let{orientation:t,selectedIndex:l}=F(\"Tab.List\"),o=w(n);return U({ourProps:{ref:o,role:\"tablist\",\"aria-orientation\":t},theirProps:e,slot:{selectedIndex:l},defaultTag:Pe,name:\"Tabs.List\"})}let xe=\"button\";function ge(e,n){var O,s;let t=Y(),{id:l=`headlessui-tabs-tab-${t}`,...o}=e,{orientation:a,activation:T,selectedIndex:R,tabs:m,panels:b}=F(\"Tab\"),i=q(\"Tab\"),c=F(\"Tab\"),p=J(null),u=w(p,n);N(()=>i.registerTab(p),[i,p]);let f=ee(\"tabs\"),P=m.indexOf(p);P===-1&&(P=f);let g=P===R,E=_(r=>{var M;let d=r();if(d===j.Success&&T===\"auto\"){let K=(M=ie(p))==null?void 0:M.activeElement,z=c.tabs.findIndex(te=>te.current===K);z!==-1&&i.change(z)}return d}),L=_(r=>{let d=m.map(K=>K.current).filter(Boolean);if(r.key===y.Space||r.key===y.Enter){r.preventDefault(),r.stopPropagation(),i.change(P);return}switch(r.key){case y.Home:case y.PageUp:return r.preventDefault(),r.stopPropagation(),E(()=>D(d,x.First));case y.End:case y.PageDown:return r.preventDefault(),r.stopPropagation(),E(()=>D(d,x.Last))}if(E(()=>G(a,{vertical(){return r.key===y.ArrowUp?D(d,x.Previous|x.WrapAround):r.key===y.ArrowDown?D(d,x.Next|x.WrapAround):j.Error},horizontal(){return r.key===y.ArrowLeft?D(d,x.Previous|x.WrapAround):r.key===y.ArrowRight?D(d,x.Next|x.WrapAround):j.Error}}))===j.Success)return r.preventDefault()}),A=J(!1),S=_(()=>{var r;A.current||(A.current=!0,(r=p.current)==null||r.focus({preventScroll:!0}),i.change(P),se(()=>{A.current=!1}))}),k=_(r=>{r.preventDefault()}),h=I(()=>{var r;return{selected:g,disabled:(r=e.disabled)!=null?r:!1}},[g,e.disabled]),W={ref:u,onKeyDown:L,onMouseDown:k,onClick:S,id:l,role:\"tab\",type:ae(e,p),\"aria-controls\":(s=(O=b[P])==null?void 0:O.current)==null?void 0:s.id,\"aria-selected\":g,tabIndex:g?0:-1};return U({ourProps:W,theirProps:o,slot:h,defaultTag:xe,name:\"Tabs.Tab\"})}let Ee=\"div\";function Ae(e,n){let{selectedIndex:t}=F(\"Tab.Panels\"),l=w(n),o=I(()=>({selectedIndex:t}),[t]);return U({ourProps:{ref:l},theirProps:e,slot:o,defaultTag:Ee,name:\"Tabs.Panels\"})}let Re=\"div\",Le=Z.RenderStrategy|Z.Static;function _e(e,n){var E,L,A,S;let t=Y(),{id:l=`headlessui-tabs-panel-${t}`,tabIndex:o=0,...a}=e,{selectedIndex:T,tabs:R,panels:m}=F(\"Tab.Panel\"),b=q(\"Tab.Panel\"),i=J(null),c=w(i,n);N(()=>b.registerPanel(i),[b,i,l]);let p=ee(\"panels\"),u=m.indexOf(i);u===-1&&(u=p);let f=u===T,P=I(()=>({selected:f}),[f]),g={ref:c,id:l,role:\"tabpanel\",\"aria-labelledby\":(L=(E=R[u])==null?void 0:E.current)==null?void 0:L.id,tabIndex:f?o:-1};return!f&&((A=a.unmount)==null||A)&&!((S=a.static)!=null&&S)?C.createElement(oe,{as:\"span\",\"aria-hidden\":\"true\",...g}):U({ourProps:g,theirProps:a,slot:P,defaultTag:Re,features:Le,visible:f,name:\"Tabs.Panel\"})}let Se=H(ge),Ie=H(me),De=H(ye),Fe=H(Ae),he=H(_e),$e=Object.assign(Se,{Group:Ie,List:De,Panels:Fe,Panel:he});export{$e as Tab};\n", "import s,{useState as c}from\"react\";import{useIsMounted as m}from'../hooks/use-is-mounted.js';import{Features as f,Hidden as l}from'./hidden.js';function b({onFocus:n}){let[r,o]=c(!0),u=m();return r?s.createElement(l,{as:\"button\",type:\"button\",features:f.Focusable,onFocus:a=>{a.preventDefault();let e,i=50;function t(){if(i--<=0){e&&cancelAnimationFrame(e);return}if(n()){if(cancelAnimationFrame(e),!u.current)return;o(!1);return}e=requestAnimationFrame(t)}e=requestAnimationFrame(t)}}):null}export{b as FocusSentinel};\n", "import*as r from\"react\";const s=r.createContext(null);function a(){return{groups:new Map,get(n,t){var c;let e=this.groups.get(n);e||(e=new Map,this.groups.set(n,e));let l=(c=e.get(t))!=null?c:0;e.set(t,l+1);let o=Array.from(e.keys()).indexOf(t);function i(){let u=e.get(t);u>1?e.set(t,u-1):e.delete(t)}return[o,i]}}}function C({children:n}){let t=r.useRef(a());return r.createElement(s.Provider,{value:t},n)}function d(n){let t=r.useContext(s);if(!t)throw new Error(\"You must wrap your component in a <StableCollection>\");let e=f(),[l,o]=t.current.get(n,e);return r.useEffect(()=>o,[]),l}function f(){var l,o,i;let n=(i=(o=(l=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED)==null?void 0:l.ReactCurrentOwner)==null?void 0:o.current)!=null?i:null;if(!n)return Symbol();let t=[],e=n;for(;e;)t.push(e.index),e=e.return;return\"$.\"+t.join(\".\")}export{C as StableCollection,d as useStableCollectionIndex};\n", "import m,{createContext as Z,Fragment as $,useContext as J,useEffect as F,useMemo as ee,useRef as c,useState as X}from\"react\";import{useDisposables as pe}from'../../hooks/use-disposables.js';import{useEvent as E}from'../../hooks/use-event.js';import{useFlags as he}from'../../hooks/use-flags.js';import{useIsMounted as ve}from'../../hooks/use-is-mounted.js';import{useIsoMorphicEffect as ge}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as A}from'../../hooks/use-latest-value.js';import{useServerHandoffComplete as te}from'../../hooks/use-server-handoff-complete.js';import{useSyncRefs as ne}from'../../hooks/use-sync-refs.js';import{useTransition as Ce}from'../../hooks/use-transition.js';import{OpenClosedProvider as Ee,State as b,useOpenClosed as re}from'../../internal/open-closed.js';import{classNames as ie}from'../../utils/class-names.js';import{match as _}from'../../utils/match.js';import{Features as be,forwardRefWithAs as W,render as oe,RenderStrategy as y}from'../../utils/render.js';function S(t=\"\"){return t.split(/\\s+/).filter(n=>n.length>1)}let I=Z(null);I.displayName=\"TransitionContext\";var Se=(r=>(r.Visible=\"visible\",r.Hidden=\"hidden\",r))(Se||{});function ye(){let t=J(I);if(t===null)throw new Error(\"A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.\");return t}function xe(){let t=J(M);if(t===null)throw new Error(\"A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.\");return t}let M=Z(null);M.displayName=\"NestingContext\";function U(t){return\"children\"in t?U(t.children):t.current.filter(({el:n})=>n.current!==null).filter(({state:n})=>n===\"visible\").length>0}function se(t,n){let r=A(t),s=c([]),R=ve(),D=pe(),p=E((i,e=y.Hidden)=>{let a=s.current.findIndex(({el:o})=>o===i);a!==-1&&(_(e,{[y.Unmount](){s.current.splice(a,1)},[y.Hidden](){s.current[a].state=\"hidden\"}}),D.microTask(()=>{var o;!U(s)&&R.current&&((o=r.current)==null||o.call(r))}))}),x=E(i=>{let e=s.current.find(({el:a})=>a===i);return e?e.state!==\"visible\"&&(e.state=\"visible\"):s.current.push({el:i,state:\"visible\"}),()=>p(i,y.Unmount)}),h=c([]),v=c(Promise.resolve()),u=c({enter:[],leave:[],idle:[]}),g=E((i,e,a)=>{h.current.splice(0),n&&(n.chains.current[e]=n.chains.current[e].filter(([o])=>o!==i)),n==null||n.chains.current[e].push([i,new Promise(o=>{h.current.push(o)})]),n==null||n.chains.current[e].push([i,new Promise(o=>{Promise.all(u.current[e].map(([f,N])=>N)).then(()=>o())})]),e===\"enter\"?v.current=v.current.then(()=>n==null?void 0:n.wait.current).then(()=>a(e)):a(e)}),d=E((i,e,a)=>{Promise.all(u.current[e].splice(0).map(([o,f])=>f)).then(()=>{var o;(o=h.current.shift())==null||o()}).then(()=>a(e))});return ee(()=>({children:s,register:x,unregister:p,onStart:g,onStop:d,wait:v,chains:u}),[x,p,s,g,d,u,v])}function Ne(){}let Pe=[\"beforeEnter\",\"afterEnter\",\"beforeLeave\",\"afterLeave\"];function ae(t){var r;let n={};for(let s of Pe)n[s]=(r=t[s])!=null?r:Ne;return n}function Re(t){let n=c(ae(t));return F(()=>{n.current=ae(t)},[t]),n}let De=\"div\",le=be.RenderStrategy;function He(t,n){var Q,Y;let{beforeEnter:r,afterEnter:s,beforeLeave:R,afterLeave:D,enter:p,enterFrom:x,enterTo:h,entered:v,leave:u,leaveFrom:g,leaveTo:d,...i}=t,e=c(null),a=ne(e,n),o=(Q=i.unmount)==null||Q?y.Unmount:y.Hidden,{show:f,appear:N,initial:T}=ye(),[l,j]=X(f?\"visible\":\"hidden\"),z=xe(),{register:L,unregister:O}=z;F(()=>L(e),[L,e]),F(()=>{if(o===y.Hidden&&e.current){if(f&&l!==\"visible\"){j(\"visible\");return}return _(l,{[\"hidden\"]:()=>O(e),[\"visible\"]:()=>L(e)})}},[l,e,L,O,f,o]);let k=A({base:S(i.className),enter:S(p),enterFrom:S(x),enterTo:S(h),entered:S(v),leave:S(u),leaveFrom:S(g),leaveTo:S(d)}),V=Re({beforeEnter:r,afterEnter:s,beforeLeave:R,afterLeave:D}),G=te();F(()=>{if(G&&l===\"visible\"&&e.current===null)throw new Error(\"Did you forget to passthrough the `ref` to the actual DOM node?\")},[e,l,G]);let Te=T&&!N,K=N&&f&&T,de=(()=>!G||Te?\"idle\":f?\"enter\":\"leave\")(),H=he(0),fe=E(C=>_(C,{enter:()=>{H.addFlag(b.Opening),V.current.beforeEnter()},leave:()=>{H.addFlag(b.Closing),V.current.beforeLeave()},idle:()=>{}})),me=E(C=>_(C,{enter:()=>{H.removeFlag(b.Opening),V.current.afterEnter()},leave:()=>{H.removeFlag(b.Closing),V.current.afterLeave()},idle:()=>{}})),w=se(()=>{j(\"hidden\"),O(e)},z),B=c(!1);Ce({immediate:K,container:e,classes:k,direction:de,onStart:A(C=>{B.current=!0,w.onStart(e,C,fe)}),onStop:A(C=>{B.current=!1,w.onStop(e,C,me),C===\"leave\"&&!U(w)&&(j(\"hidden\"),O(e))})});let P=i,ce={ref:a};return K?P={...P,className:ie(i.className,...k.current.enter,...k.current.enterFrom)}:B.current&&(P.className=ie(i.className,(Y=e.current)==null?void 0:Y.className),P.className===\"\"&&delete P.className),m.createElement(M.Provider,{value:w},m.createElement(Ee,{value:_(l,{[\"visible\"]:b.Open,[\"hidden\"]:b.Closed})|H.flags},oe({ourProps:ce,theirProps:P,defaultTag:De,features:le,visible:l===\"visible\",name:\"Transition.Child\"})))}function Fe(t,n){let{show:r,appear:s=!1,unmount:R=!0,...D}=t,p=c(null),x=ne(p,n);te();let h=re();if(r===void 0&&h!==null&&(r=(h&b.Open)===b.Open),![!0,!1].includes(r))throw new Error(\"A <Transition /> is used but it is missing a `show={true | false}` prop.\");let[v,u]=X(r?\"visible\":\"hidden\"),g=se(()=>{u(\"hidden\")}),[d,i]=X(!0),e=c([r]);ge(()=>{d!==!1&&e.current[e.current.length-1]!==r&&(e.current.push(r),i(!1))},[e,r]);let a=ee(()=>({show:r,appear:s,initial:d}),[r,s,d]);F(()=>{if(r)u(\"visible\");else if(!U(g))u(\"hidden\");else{let T=p.current;if(!T)return;let l=T.getBoundingClientRect();l.x===0&&l.y===0&&l.width===0&&l.height===0&&u(\"hidden\")}},[r,g]);let o={unmount:R},f=E(()=>{var T;d&&i(!1),(T=t.beforeEnter)==null||T.call(t)}),N=E(()=>{var T;d&&i(!1),(T=t.beforeLeave)==null||T.call(t)});return m.createElement(M.Provider,{value:g},m.createElement(I.Provider,{value:a},oe({ourProps:{...o,as:$,children:m.createElement(ue,{ref:x,...o,...D,beforeEnter:f,beforeLeave:N})},theirProps:{},defaultTag:$,features:le,visible:v===\"visible\",name:\"Transition\"})))}function _e(t,n){let r=J(I)!==null,s=re()!==null;return m.createElement(m.Fragment,null,!r&&s?m.createElement(q,{ref:n,...t}):m.createElement(ue,{ref:n,...t}))}let q=W(Fe),ue=W(He),Le=W(_e),qe=Object.assign(q,{Child:Le,Root:q});export{qe as Transition};\n", "function l(r){let e={called:!1};return(...t)=>{if(!e.called)return e.called=!0,r(...t)}}export{l as once};\n", "import{disposables as f}from'../../../utils/disposables.js';import{match as d}from'../../../utils/match.js';import{once as s}from'../../../utils/once.js';function g(t,...e){t&&e.length>0&&t.classList.add(...e)}function v(t,...e){t&&e.length>0&&t.classList.remove(...e)}function b(t,e){let n=f();if(!t)return n.dispose;let{transitionDuration:m,transitionDelay:a}=getComputedStyle(t),[u,p]=[m,a].map(l=>{let[r=0]=l.split(\",\").filter(Boolean).map(i=>i.includes(\"ms\")?parseFloat(i):parseFloat(i)*1e3).sort((i,T)=>T-i);return r}),o=u+p;if(o!==0){n.group(r=>{r.setTimeout(()=>{e(),r.dispose()},o),r.addEventListener(t,\"transitionrun\",i=>{i.target===i.currentTarget&&r.dispose()})});let l=n.addEventListener(t,\"transitionend\",r=>{r.target===r.currentTarget&&(e(),l())})}else e();return n.add(()=>e()),n.dispose}function M(t,e,n,m){let a=n?\"enter\":\"leave\",u=f(),p=m!==void 0?s(m):()=>{};a===\"enter\"&&(t.removeAttribute(\"hidden\"),t.style.display=\"\");let o=d(a,{enter:()=>e.enter,leave:()=>e.leave}),l=d(a,{enter:()=>e.enterTo,leave:()=>e.leaveTo}),r=d(a,{enter:()=>e.enterFrom,leave:()=>e.leaveFrom});return v(t,...e.base,...e.enter,...e.enterTo,...e.enterFrom,...e.leave,...e.leaveFrom,...e.leaveTo,...e.entered),g(t,...e.base,...o,...r),u.nextFrame(()=>{v(t,...e.base,...o,...r),g(t,...e.base,...o,...l),b(t,()=>(v(t,...e.base,...o),g(t,...e.base,...e.entered),p()))}),u.dispose}export{M as transition};\n", "import{transition as f}from'../components/transitions/utils/transition.js';import{disposables as m}from'../utils/disposables.js';import{useDisposables as p}from'./use-disposables.js';import{useIsMounted as b}from'./use-is-mounted.js';import{useIsoMorphicEffect as o}from'./use-iso-morphic-effect.js';import{useLatestValue as g}from'./use-latest-value.js';function D({immediate:t,container:s,direction:n,classes:u,onStart:a,onStop:c}){let l=b(),d=p(),e=g(n);o(()=>{t&&(e.current=\"enter\")},[t]),o(()=>{let r=m();d.add(r.dispose);let i=s.current;if(i&&e.current!==\"idle\"&&l.current)return r.dispose(),a.current(e.current),r.add(f(i,u.current,e.current===\"enter\",()=>{r.dispose(),c.current(e.current)})),r.dispose},[n])}export{D as useTransition};\n"], "mappings": ";;;;;;;;;;;;;;;AAIgB,SAAA,KACd,SACA,IACA,MAMA;AACI,MAAA,OAAO,KAAK,eAAe,CAAC;AAC5B,MAAA;AAEJ,WAAS,mBAA4B;AAbvB,QAAA,IAAA,IAAA,IAAA;AAcR,QAAA;AACJ,QAAI,KAAK,SAAO,KAAA,KAAK,UAAL,OAAA,SAAA,GAAA,KAAA,IAAA,GAAgB,WAAU,KAAK,IAAI;AAEnD,UAAM,UAAU,QAAQ;AAExB,UAAM,cACJ,QAAQ,WAAW,KAAK,UACxB,QAAQ,KAAK,CAAC,KAAU,UAAkB,KAAK,KAAK,MAAM,GAAG;AAE/D,QAAI,CAAC,aAAa;AACT,aAAA;IAAA;AAGF,WAAA;AAEH,QAAA;AACJ,QAAI,KAAK,SAAO,KAAA,KAAK,UAAL,OAAA,SAAA,GAAA,KAAA,IAAA,GAAgB,cAAa,KAAK,IAAI;AAE7C,aAAA,GAAG,GAAG,OAAO;AAEtB,QAAI,KAAK,SAAO,KAAA,KAAK,UAAL,OAAA,SAAA,GAAA,KAAA,IAAA,IAAgB;AACxB,YAAA,aAAa,KAAK,OAAO,KAAK,IAAA,IAAQ,WAAY,GAAG,IAAI;AACzD,YAAA,gBAAgB,KAAK,OAAO,KAAK,IAAA,IAAQ,cAAe,GAAG,IAAI;AACrE,YAAM,sBAAsB,gBAAgB;AAEtC,YAAA,MAAM,CAAC,KAAsB,QAAgB;AACjD,cAAM,OAAO,GAAG;AACT,eAAA,IAAI,SAAS,KAAK;AACvB,gBAAM,MAAM;QAAA;AAEP,eAAA;MACT;AAEQ,cAAA;QACN,OAAO,IAAI,eAAe,CAAC,CAAC,KAAK,IAAI,YAAY,CAAC,CAAC;QACnD;;;yBAGiB,KAAK;UAChB;UACA,KAAK,IAAI,MAAM,MAAM,qBAAqB,GAAG;QAC9C,CAAA;QACL,QAAA,OAAA,SAAA,KAAM;MACR;IAAA;AAGF,KAAA,KAAA,QAAA,OAAA,SAAA,KAAM,aAAN,OAAA,SAAA,GAAA,KAAA,MAAiB,MAAA;AAEV,WAAA;EAAA;AAIQ,mBAAA,aAAa,CAAC,YAAwB;AAC9C,WAAA;EACT;AAEO,SAAA;AACT;AAEgB,SAAA,aAAgB,OAAsB,KAAiB;AACrE,MAAI,UAAU,QAAW;AACjB,UAAA,IAAI,MAAM,uBAAuB,MAAM,KAAK,GAAG,KAAK,EAAE,EAAE;EAAA,OACzD;AACE,WAAA;EAAA;AAEX;AAEa,IAAA,cAAc,CAACA,KAAWC,OAAc,KAAK,IAAID,MAAIC,EAAC,KAAK;AAEjE,IAAM,WAAW,CACtB,cACA,IACA,OACG;AACC,MAAA;AACJ,SAAO,YAAwB,MAAkB;AAC/C,iBAAa,aAAa,SAAS;AACvB,gBAAA,aAAa,WAAW,MAAM,GAAG,MAAM,MAAM,IAAI,GAAG,EAAE;EACpE;AACF;;;ACnDA,IAAM,UAAU,CAAC,YAA+B;AACxC,QAAA,EAAE,aAAa,aAAA,IAAiB;AACtC,SAAO,EAAE,OAAO,aAAa,QAAQ,aAAa;AACpD;AAEa,IAAA,sBAAsB,CAAC,UAAkB;AAEzC,IAAA,wBAAwB,CAAC,UAAiB;AACrD,QAAM,QAAQ,KAAK,IAAI,MAAM,aAAa,MAAM,UAAU,CAAC;AACrD,QAAA,MAAM,KAAK,IAAI,MAAM,WAAW,MAAM,UAAU,MAAM,QAAQ,CAAC;AAErE,QAAM,MAAM,CAAC;AAEb,WAASC,KAAI,OAAOA,MAAK,KAAKA,MAAK;AACjC,QAAI,KAAKA,EAAC;EAAA;AAGL,SAAA;AACT;AAEa,IAAA,qBAAqB,CAChC,UACA,OACG;AACH,QAAM,UAAU,SAAS;AACzB,MAAI,CAAC,SAAS;AACZ;EAAA;AAEF,QAAM,eAAe,SAAS;AAC9B,MAAI,CAAC,cAAc;AACjB;EAAA;AAGI,QAAA,UAAU,CAAC,SAAe;AACxB,UAAA,EAAE,OAAO,OAAA,IAAW;AACvB,OAAA,EAAE,OAAO,KAAK,MAAM,KAAK,GAAG,QAAQ,KAAK,MAAM,MAAM,EAAA,CAAG;EAC7D;AAEQ,UAAA,QAAQ,OAAiC,CAAC;AAE9C,MAAA,CAAC,aAAa,gBAAgB;AAChC,WAAO,MAAM;IAAC;EAAA;AAGhB,QAAM,WAAW,IAAI,aAAa,eAAe,CAAC,YAAY;AAC5D,UAAM,MAAM,MAAM;AACV,YAAA,QAAQ,QAAQ,CAAC;AACvB,UAAI,SAAA,OAAA,SAAA,MAAO,eAAe;AAClB,cAAA,MAAM,MAAM,cAAc,CAAC;AACjC,YAAI,KAAK;AACP,kBAAQ,EAAE,OAAO,IAAI,YAAY,QAAQ,IAAI,UAAA,CAAW;AACxD;QAAA;MACF;AAEM,cAAA,QAAQ,OAAiC,CAAC;IACpD;AAEA,aAAS,QAAQ,sCACb,sBAAsB,GAAG,IACzB,IAAI;EAAA,CACT;AAED,WAAS,QAAQ,SAAS,EAAE,KAAK,aAAA,CAAc;AAE/C,SAAO,MAAM;AACX,aAAS,UAAU,OAAO;EAC5B;AACF;AAEA,IAAM,0BAA0B;EAC9B,SAAS;AACX;AAuBA,IAAM,oBACJ,OAAO,UAAU,cAAc,OAAO,iBAAiB;AAI5C,IAAA,uBAAuB,CAClC,UACA,OACG;AACH,QAAM,UAAU,SAAS;AACzB,MAAI,CAAC,SAAS;AACZ;EAAA;AAEF,QAAM,eAAe,SAAS;AAC9B,MAAI,CAAC,cAAc;AACjB;EAAA;AAGF,MAAI,SAAS;AACb,QAAM,WACJ,SAAS,QAAQ,qBAAqB,oBAClC,MAAM,SACN;IACE;IACA,MAAM;AACJ,SAAG,QAAQ,KAAK;IAClB;IACA,SAAS,QAAQ;EACnB;AAEA,QAAA,gBAAgB,CAAC,gBAAyB,MAAM;AACpD,UAAM,EAAE,YAAY,MAAM,IAAI,SAAS;AAC9B,aAAA,aACL,QAAQ,YAAY,KAAM,SAAS,MAAO,KAC1C,QAAQ,WAAW;AACd,aAAA;AACT,OAAG,QAAQ,WAAW;EACxB;AACM,QAAA,UAAU,cAAc,IAAI;AAC5B,QAAA,aAAa,cAAc,KAAK;AAC3B,aAAA;AAEH,UAAA,iBAAiB,UAAU,SAAS,uBAAuB;AAC7D,QAAA,yBACJ,SAAS,QAAQ,qBAAqB;AACxC,MAAI,wBAAwB;AAClB,YAAA,iBAAiB,aAAa,YAAY,uBAAuB;EAAA;AAE3E,SAAO,MAAM;AACH,YAAA,oBAAoB,UAAU,OAAO;AAC7C,QAAI,wBAAwB;AAClB,cAAA,oBAAoB,aAAa,UAAU;IAAA;EAEvD;AACF;AAkDO,IAAM,iBAAiB,CAC5B,SACA,OACA,aACG;AACH,MAAI,SAAA,OAAA,SAAA,MAAO,eAAe;AAClB,UAAA,MAAM,MAAM,cAAc,CAAC;AACjC,QAAI,KAAK;AACP,YAAM,OAAO,KAAK;QAChB,IAAI,SAAS,QAAQ,aAAa,eAAe,WAAW;MAC9D;AACO,aAAA;IAAA;EACT;AAGF,SAAQ,QACN,SAAS,QAAQ,aAAa,gBAAgB,cAChD;AACF;AAkBa,IAAA,gBAAgB,CAC3B,QACA;EACE,cAAc;EACd;AACF,GACA,aACG;;AACH,QAAM,WAAW,SAAS;AAE1B,GAAA,MAAA,KAAA,SAAS,kBAAT,OAAA,SAAA,GAAwB,aAAxB,OAAA,SAAA,GAAA,KAAA,IAAmC;IACjC,CAAC,SAAS,QAAQ,aAAa,SAAS,KAAK,GAAG;IAChD;EAAA,CAAA;AAEJ;AA0DO,IAAM,cAAN,MAGL;EA0DA,YAAY,MAAwD;AAzDpE,SAAQ,SAAqC,CAAC;AAEP,SAAA,gBAAA;AACa,SAAA,eAAA;AACtC,SAAA,cAAA;AACd,SAAQ,yBAAwC;AAChD,SAAA,oBAAwC,CAAC;AACjC,SAAA,gBAAA,oBAAoB,IAAiB;AAC7C,SAAQ,8BAA6C,CAAC;AAC5B,SAAA,aAAA;AACI,SAAA,eAAA;AACY,SAAA,kBAAA;AAC1C,SAAQ,oBAAoB;AAQ5B,SAAA,gBAAA,oBAAoB,IAAuB;AAC3C,SAAQ,WAAkB,uBAAA;AACxB,UAAI,MAA6B;AAEjC,YAAM,MAAM,MAAM;AAChB,YAAI,KAAK;AACA,iBAAA;QAAA;AAGT,YAAI,CAAC,KAAK,gBAAgB,CAAC,KAAK,aAAa,gBAAgB;AACpD,iBAAA;QAAA;AAGT,eAAQ,MAAM,IAAI,KAAK,aAAa,eAAe,CAAC,YAAY;AACtD,kBAAA,QAAQ,CAAC,UAAU;AACzB,kBAAM,MAAM,MAAM;AACX,mBAAA,gBAAgB,MAAM,QAAwB,KAAK;YAC1D;AACA,iBAAK,QAAQ,sCACT,sBAAsB,GAAG,IACzB,IAAI;UAAA,CACT;QAAA,CACF;MACH;AAEO,aAAA;QACL,YAAY,MAAM;;AAChB,WAAA,KAAA,IAAA,MAAA,OAAA,SAAA,GAAO,WAAA;AACD,gBAAA;QACR;QACA,SAAS,CAAC,WAAA;;AACR,kBAAA,KAAA,IAAI,MAAJ,OAAA,SAAA,GAAO,QAAQ,QAAQ,EAAE,KAAK,aAAA,CAAA;;QAChC,WAAW,CAAC,WAAA;;AAAoB,kBAAA,KAAA,IAAI,MAAJ,OAAA,SAAA,GAAO,UAAU,MAAA;QAAA;MACnD;IAAA,GACC;AACsD,SAAA,QAAA;AAMzD,SAAA,aAAa,CAACC,UAA2D;AAChE,aAAA,QAAQA,KAAI,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC7C,YAAI,OAAO,UAAU,YAAa,QAAQA,MAAa,GAAG;MAAA,CAC3D;AAED,WAAK,UAAU;QACb,OAAO;QACP,eAAe;QACf,UAAU;QACV,cAAc;QACd,YAAY;QACZ,oBAAoB;QACpB,kBAAkB;QAClB,YAAY;QACZ,YAAY;QACZ,gBAAgB;QAChB,UAAU,MAAM;QAAC;QACjB;QACA,aAAa,EAAE,OAAO,GAAG,QAAQ,EAAE;QACnC,cAAc;QACd,KAAK;QACL,gBAAgB;QAChB,0BAA0B,CAAC;QAC3B,OAAO;QACP,uBAAuB;QACvB,SAAS;QACT,OAAO;QACP,mBAAmB;QACnB,qCAAqC;QACrC,GAAGA;MACL;IACF;AAEQ,SAAA,SAAS,CAAC,SAAkB;;AAC7B,OAAA,MAAA,KAAA,KAAA,SAAQ,aAAR,OAAA,SAAA,GAAA,KAAA,IAAmB,MAAM,IAAA;IAChC;AAEA,SAAQ,cAAc;MACpB,MAAM;AACJ,aAAK,eAAe;AAEb,eAAA;UACL,KAAK;UACL,KAAK,QAAQ,KAAK,MAAM,aAAa;UACrC,KAAK,QAAQ,KAAK,MAAM,WAAW;QACrC;MACF;MACA,CAAC,gBAAgB;AACf,aAAK,OAAO,WAAW;MACzB;MACA;QACE,KAA8C;QAC9C,OAAO,MAAM,KAAK,QAAQ;QAC1B,aAAa;UACX,KAAK;UACL,KAAK,QAAQ,KAAK,MAAM,aAAa;UACrC,KAAK,QAAQ,KAAK,MAAM,WAAW;QAAA;MACrC;IAEJ;AAEA,SAAQ,UAAU,MAAM;AACjB,WAAA,OAAO,OAAO,OAAO,EAAE,QAAQ,CAACC,QAAMA,IAAA,CAAI;AAC/C,WAAK,SAAS,CAAC;AACf,WAAK,SAAS,WAAW;AACzB,WAAK,gBAAgB;AACrB,WAAK,eAAe;IACtB;AAEA,SAAA,YAAY,MAAM;AAChB,aAAO,MAAM;AACX,aAAK,QAAQ;MACf;IACF;AAEA,SAAA,cAAc,MAAM;;AAClB,YAAM,gBAAgB,KAAK,QAAQ,UAC/B,KAAK,QAAQ,iBAAA,IACb;AAEA,UAAA,KAAK,kBAAkB,eAAe;AACxC,aAAK,QAAQ;AAEb,YAAI,CAAC,eAAe;AAClB,eAAK,YAAY;AACjB;QAAA;AAGF,aAAK,gBAAgB;AAErB,YAAI,KAAK,iBAAiB,mBAAmB,KAAK,eAAe;AAC1D,eAAA,eAAe,KAAK,cAAc,cAAc;QAAA,OAChD;AACA,eAAA,iBAAe,KAAA,KAAK,kBAAL,OAAA,SAAA,GAAoB,WAAU;QAAA;AAG/C,aAAA,cAAc,QAAQ,CAAC,WAAW;AAChC,eAAA,SAAS,QAAQ,MAAM;QAAA,CAC7B;AAEI,aAAA,gBAAgB,KAAK,gBAAA,GAAmB;UAC3C,aAAa;UACb,UAAU;QAAA,CACX;AAED,aAAK,OAAO;UACV,KAAK,QAAQ,mBAAmB,MAAM,CAAC,SAAS;AAC9C,iBAAK,aAAa;AAClB,iBAAK,YAAY;UAClB,CAAA;QACH;AAEA,aAAK,OAAO;UACV,KAAK,QAAQ,qBAAqB,MAAM,CAAC,QAAQ,gBAAgB;AAC/D,iBAAK,oBAAoB;AACzB,iBAAK,kBAAkB,cACnB,KAAK,gBAAA,IAAoB,SACvB,YACA,aACF;AACJ,iBAAK,eAAe;AACpB,iBAAK,cAAc;AAEnB,iBAAK,YAAY;UAClB,CAAA;QACH;MAAA;IAEJ;AAEA,SAAQ,UAAU,MAAM;AAClB,UAAA,CAAC,KAAK,QAAQ,SAAS;AACzB,aAAK,aAAa;AACX,eAAA;MAAA;AAGT,WAAK,aAAa,KAAK,cAAc,KAAK,QAAQ;AAElD,aAAO,KAAK,WAAW,KAAK,QAAQ,aAAa,UAAU,QAAQ;IACrE;AAEA,SAAQ,kBAAkB,MAAM;AAC1B,UAAA,CAAC,KAAK,QAAQ,SAAS;AACzB,aAAK,eAAe;AACb,eAAA;MAAA;AAGT,WAAK,eACH,KAAK,iBACJ,OAAO,KAAK,QAAQ,kBAAkB,aACnC,KAAK,QAAQ,cAAc,IAC3B,KAAK,QAAQ;AAEnB,aAAO,KAAK;IACd;AAEQ,SAAA,yBAAyB,CAC/B,cACA,UACG;AACG,YAAA,4BAAA,oBAAgC,IAAkB;AAClD,YAAA,uBAAA,oBAA2B,IAAyB;AAC1D,eAASC,MAAI,QAAQ,GAAGA,OAAK,GAAGA,OAAK;AAC7B,cAAA,cAAc,aAAaA,GAAC;AAElC,YAAI,0BAA0B,IAAI,YAAY,IAAI,GAAG;AACnD;QAAA;AAGF,cAAM,8BAA8B,qBAAqB;UACvD,YAAY;QACd;AACA,YACE,+BAA+B,QAC/B,YAAY,MAAM,4BAA4B,KAC9C;AACqB,+BAAA,IAAI,YAAY,MAAM,WAAW;QAC7C,WAAA,YAAY,MAAM,4BAA4B,KAAK;AAClC,oCAAA,IAAI,YAAY,MAAM,IAAI;QAAA;AAGtD,YAAI,0BAA0B,SAAS,KAAK,QAAQ,OAAO;AACzD;QAAA;MACF;AAGF,aAAO,qBAAqB,SAAS,KAAK,QAAQ,QAC9C,MAAM,KAAK,qBAAqB,OAAA,CAAQ,EAAE,KAAK,CAACC,KAAGC,OAAM;AACnD,YAAAD,IAAE,QAAQC,GAAE,KAAK;AACZ,iBAAAD,IAAE,QAAQC,GAAE;QAAA;AAGd,eAAAD,IAAE,MAAMC,GAAE;MAAA,CAClB,EAAE,CAAC,IACJ;IACN;AAEA,SAAQ,wBAAwB;MAC9B,MAAM;QACJ,KAAK,QAAQ;QACb,KAAK,QAAQ;QACb,KAAK,QAAQ;QACb,KAAK,QAAQ;QACb,KAAK,QAAQ;MACf;MACA,CAAC,OAAO,cAAc,cAAc,YAAY,YAAY;AAC1D,aAAK,8BAA8B,CAAC;AAC7B,eAAA;UACL;UACA;UACA;UACA;UACA;QACF;MACF;MACA;QACE,KAAK;MAAA;IAET;AAEA,SAAQ,kBAAkB;MACxB,MAAM,CAAC,KAAK,sBAAA,GAAyB,KAAK,aAAa;MACvD,CACE,EAAE,OAAO,cAAc,cAAc,YAAY,QAAA,GACjD,kBACG;AACH,YAAI,CAAC,SAAS;AACZ,eAAK,oBAAoB,CAAC;AAC1B,eAAK,cAAc,MAAM;AACzB,iBAAO,CAAC;QAAA;AAGN,YAAA,KAAK,kBAAkB,WAAW,GAAG;AAClC,eAAA,oBAAoB,KAAK,QAAQ;AACjC,eAAA,kBAAkB,QAAQ,CAAC,SAAS;AACvC,iBAAK,cAAc,IAAI,KAAK,KAAK,KAAK,IAAI;UAAA,CAC3C;QAAA;AAGG,cAAA,MACJ,KAAK,4BAA4B,SAAS,IACtC,KAAK,IAAI,GAAG,KAAK,2BAA2B,IAC5C;AACN,aAAK,8BAA8B,CAAC;AAEpC,cAAM,eAAe,KAAK,kBAAkB,MAAM,GAAG,GAAG;AAExD,iBAASC,KAAI,KAAKA,KAAI,OAAOA,MAAK;AAC1B,gBAAA,MAAM,WAAWA,EAAC;AAExB,gBAAM,sBACJ,KAAK,QAAQ,UAAU,IACnB,aAAaA,KAAI,CAAC,IAClB,KAAK,uBAAuB,cAAcA,EAAC;AAEjD,gBAAM,QAAQ,sBACV,oBAAoB,MAAM,KAAK,QAAQ,MACvC,eAAe;AAEb,gBAAA,eAAe,cAAc,IAAI,GAAG;AACpC,gBAAA,OACJ,OAAO,iBAAiB,WACpB,eACA,KAAK,QAAQ,aAAaA,EAAC;AAEjC,gBAAM,MAAM,QAAQ;AAEpB,gBAAM,OAAO,sBACT,oBAAoB,OACpBA,KAAI,KAAK,QAAQ;AAErB,uBAAaA,EAAC,IAAI;YAChB,OAAOA;YACP;YACA;YACA;YACA;YACA;UACF;QAAA;AAGF,aAAK,oBAAoB;AAElB,eAAA;MACT;MACA;QACE,KAA8C;QAC9C,OAAO,MAAM,KAAK,QAAQ;MAAA;IAE9B;AAEiB,SAAA,iBAAA;MACf,MAAM;QACJ,KAAK,gBAAgB;QACrB,KAAK,QAAQ;QACb,KAAK,gBAAgB;QACrB,KAAK,QAAQ;MACf;MACA,CAAC,cAAc,WAAW,cAAc,UAAU;AAChD,eAAQ,KAAK,QACX,aAAa,SAAS,KAAK,YAAY,IACnC,eAAe;UACb;UACA;UACA;UACA;QACD,CAAA,IACD;MACR;MACA;QACE,KAA8C;QAC9C,OAAO,MAAM,KAAK,QAAQ;MAAA;IAE9B;AAEoB,SAAA,oBAAA;MAClB,MAAM;AACJ,YAAI,aAA4B;AAChC,YAAI,WAA0B;AACxB,cAAA,QAAQ,KAAK,eAAe;AAClC,YAAI,OAAO;AACT,uBAAa,MAAM;AACnB,qBAAW,MAAM;QAAA;AAEnB,aAAK,YAAY,WAAW,CAAC,KAAK,aAAa,YAAY,QAAQ,CAAC;AAC7D,eAAA;UACL,KAAK,QAAQ;UACb,KAAK,QAAQ;UACb,KAAK,QAAQ;UACb;UACA;QACF;MACF;MACA,CAAC,gBAAgB,UAAU,OAAO,YAAY,aAAa;AACzD,eAAO,eAAe,QAAQ,aAAa,OACvC,CAAA,IACA,eAAe;UACb;UACA;UACA;UACA;QAAA,CACD;MACP;MACA;QACE,KAA8C;QAC9C,OAAO,MAAM,KAAK,QAAQ;MAAA;IAE9B;AAEA,SAAA,mBAAmB,CAAC,SAAuB;AACnC,YAAA,gBAAgB,KAAK,QAAQ;AAC7B,YAAA,WAAW,KAAK,aAAa,aAAa;AAEhD,UAAI,CAAC,UAAU;AACL,gBAAA;UACN,2BAA2B,aAAa;QAC1C;AACO,eAAA;MAAA;AAGF,aAAA,SAAS,UAAU,EAAE;IAC9B;AAEQ,SAAA,kBAAkB,CACxB,MACA,UACG;AACG,YAAA,QAAQ,KAAK,iBAAiB,IAAI;AAClC,YAAA,OAAO,KAAK,kBAAkB,KAAK;AACzC,UAAI,CAAC,MAAM;AACT;MAAA;AAEF,YAAM,MAAM,KAAK;AACjB,YAAM,WAAW,KAAK,cAAc,IAAI,GAAG;AAE3C,UAAI,aAAa,MAAM;AACrB,YAAI,UAAU;AACP,eAAA,SAAS,UAAU,QAAQ;QAAA;AAE7B,aAAA,SAAS,QAAQ,IAAI;AACrB,aAAA,cAAc,IAAI,KAAK,IAAI;MAAA;AAGlC,UAAI,KAAK,aAAa;AACf,aAAA,WAAW,OAAO,KAAK,QAAQ,eAAe,MAAM,OAAO,IAAI,CAAC;MAAA;IAEzE;AAEa,SAAA,aAAA,CAAC,OAAe,SAAiB;AACtC,YAAA,OAAO,KAAK,kBAAkB,KAAK;AACzC,UAAI,CAAC,MAAM;AACT;MAAA;AAEF,YAAM,WAAW,KAAK,cAAc,IAAI,KAAK,GAAG,KAAK,KAAK;AAC1D,YAAM,QAAQ,OAAO;AAErB,UAAI,UAAU,GAAG;AACf,YACE,KAAK,+CAA+C,SAChD,KAAK,2CAA2C,MAAM,OAAO,IAAI,IACjE,KAAK,oBAAoB,cACzB,KAAK,QAAQ,KAAK,gBAAgB,IAAI,KAAK,mBAC/C;AACA,cAA6C,KAAK,QAAQ,OAAO;AACvD,oBAAA,KAAK,cAAc,KAAK;UAAA;AAG7B,eAAA,gBAAgB,KAAK,gBAAA,GAAmB;YAC3C,aAAc,KAAK,qBAAqB;YACxC,UAAU;UAAA,CACX;QAAA;AAGE,aAAA,4BAA4B,KAAK,KAAK,KAAK;AAC3C,aAAA,gBAAgB,IAAI,IAAI,KAAK,cAAc,IAAI,KAAK,KAAK,IAAI,CAAC;AAEnE,aAAK,OAAO,KAAK;MAAA;IAErB;AAEA,SAAA,iBAAiB,CAAC,SAA0C;AAC1D,UAAI,CAAC,MAAM;AACT,aAAK,cAAc,QAAQ,CAAC,QAAQ,QAAQ;AACtC,cAAA,CAAC,OAAO,aAAa;AAClB,iBAAA,SAAS,UAAU,MAAM;AACzB,iBAAA,cAAc,OAAO,GAAG;UAAA;QAC/B,CACD;AACD;MAAA;AAGG,WAAA,gBAAgB,MAAM,MAAS;IACtC;AAEkB,SAAA,kBAAA;MAChB,MAAM,CAAC,KAAK,kBAAqB,GAAA,KAAK,gBAAA,CAAiB;MACvD,CAAC,SAAS,iBAAiB;AACzB,cAAM,eAAmC,CAAC;AAE1C,iBAASC,KAAI,GAAG,MAAM,QAAQ,QAAQA,KAAI,KAAKA,MAAK;AAC5C,gBAAAD,KAAI,QAAQC,EAAC;AACb,gBAAA,cAAc,aAAaD,EAAC;AAElC,uBAAa,KAAK,WAAW;QAAA;AAGxB,eAAA;MACT;MACA;QACE,KAA8C;QAC9C,OAAO,MAAM,KAAK,QAAQ;MAAA;IAE9B;AAEA,SAAA,0BAA0B,CAAC,WAAmB;AACtC,YAAA,eAAe,KAAK,gBAAgB;AACtC,UAAA,aAAa,WAAW,GAAG;AACtB,eAAA;MAAA;AAEF,aAAA;QACL,aACE;UACE;UACA,aAAa,SAAS;UACtB,CAAC,UAAkB,aAAa,aAAa,KAAK,CAAC,EAAE;UACrD;QAEJ,CAAA;MACF;IACF;AAEA,SAAA,wBAAwB,CACtB,UACA,OACA,WAAW,MACR;AACG,YAAA,OAAO,KAAK,QAAQ;AACpB,YAAA,eAAe,KAAK,gBAAgB;AAE1C,UAAI,UAAU,QAAQ;AACZ,gBAAA,YAAY,eAAe,OAAO,QAAQ;MAAA;AAGpD,UAAI,UAAU,UAAU;AAGtB,qBAAa,WAAW,QAAQ;MAAA,WACvB,UAAU,OAAO;AACd,oBAAA;MAAA;AAGR,YAAA,YAAY,KAAK,aAAA,IAAiB;AAExC,aAAO,KAAK,IAAI,KAAK,IAAI,WAAW,QAAQ,GAAG,CAAC;IAClD;AAEoB,SAAA,oBAAA,CAAC,OAAe,QAAyB,WAAW;AAC9D,cAAA,KAAK,IAAI,GAAG,KAAK,IAAI,OAAO,KAAK,QAAQ,QAAQ,CAAC,CAAC;AAErD,YAAA,OAAO,KAAK,kBAAkB,KAAK;AACzC,UAAI,CAAC,MAAM;AACF,eAAA;MAAA;AAGH,YAAA,OAAO,KAAK,QAAQ;AACpB,YAAA,eAAe,KAAK,gBAAgB;AAE1C,UAAI,UAAU,QAAQ;AACpB,YAAI,KAAK,OAAO,eAAe,OAAO,KAAK,QAAQ,kBAAkB;AAC3D,kBAAA;QAAA,WACC,KAAK,SAAS,eAAe,KAAK,QAAQ,oBAAoB;AAC/D,kBAAA;QAAA,OACH;AACE,iBAAA,CAAC,cAAc,KAAK;QAAA;MAC7B;AAGI,YAAA,WACJ,UAAU,QACN,KAAK,MAAM,KAAK,QAAQ,mBACxB,KAAK,QAAQ,KAAK,QAAQ;AAEzB,aAAA;QACL,KAAK,sBAAsB,UAAU,OAAO,KAAK,IAAI;QACrD;MACF;IACF;AAEA,SAAQ,gBAAgB,MAAM,KAAK,cAAc,OAAO;AAExD,SAAQ,sBAAsB,MAAM;AAClC,UAAI,KAAK,2BAA2B,QAAQ,KAAK,cAAc;AACxD,aAAA,aAAa,aAAa,KAAK,sBAAsB;AAC1D,aAAK,yBAAyB;MAAA;IAElC;AAEiB,SAAA,iBAAA,CACf,UACA,EAAE,QAAQ,SAAS,SAAS,IAA2B,CAAA,MACpD;AACH,WAAK,oBAAoB;AAEzB,UAAI,aAAa,YAAY,KAAK,cAAA,GAAiB;AACzC,gBAAA;UACN;QACF;MAAA;AAGF,WAAK,gBAAgB,KAAK,sBAAsB,UAAU,KAAK,GAAG;QAChE,aAAa;QACb;MAAA,CACD;IACH;AAEgB,SAAA,gBAAA,CACd,OACA,EAAE,OAAO,eAAe,QAAQ,SAAmC,IAAA,CAAA,MAChE;AACK,cAAA,KAAK,IAAI,GAAG,KAAK,IAAI,OAAO,KAAK,QAAQ,QAAQ,CAAC,CAAC;AAE3D,WAAK,oBAAoB;AAEzB,UAAI,aAAa,YAAY,KAAK,cAAA,GAAiB;AACzC,gBAAA;UACN;QACF;MAAA;AAGF,YAAM,iBAAiB,KAAK,kBAAkB,OAAO,YAAY;AACjE,UAAI,CAAC,eAAgB;AAEf,YAAA,CAAC,QAAQ,KAAK,IAAI;AAExB,WAAK,gBAAgB,QAAQ,EAAE,aAAa,QAAW,SAAA,CAAU;AAEjE,UAAI,aAAa,YAAY,KAAK,cAAc,KAAK,KAAK,cAAc;AACtE,aAAK,yBAAyB,KAAK,aAAa,WAAW,MAAM;AAC/D,eAAK,yBAAyB;AAExB,gBAAA,eAAe,KAAK,cAAc;YACtC,KAAK,QAAQ,WAAW,KAAK;UAC/B;AAEA,cAAI,cAAc;AAChB,kBAAM,SAAS,KAAK,kBAAkB,OAAO,KAAK;AAClD,gBAAI,CAAC,OAAQ;AACP,kBAAA,CAAC,YAAY,IAAI;AAEjB,kBAAA,sBAAsB,KAAK,gBAAgB;AACjD,gBAAI,CAAC,YAAY,cAAc,mBAAmB,GAAG;AACnD,mBAAK,cAAc,OAAO,EAAE,OAAO,SAAA,CAAU;YAAA;UAC/C,OACK;AACL,iBAAK,cAAc,OAAO,EAAE,OAAO,SAAA,CAAU;UAAA;QAC/C,CACD;MAAA;IAEL;AAEA,SAAA,WAAW,CAAC,OAAe,EAAE,SAAS,IAA2B,CAAA,MAAO;AACtE,WAAK,oBAAoB;AAEzB,UAAI,aAAa,YAAY,KAAK,cAAA,GAAiB;AACzC,gBAAA;UACN;QACF;MAAA;AAGF,WAAK,gBAAgB,KAAK,gBAAgB,IAAI,OAAO;QACnD,aAAa;QACb;MAAA,CACD;IACH;AAEA,SAAA,eAAe,MAAM;;AACb,YAAA,eAAe,KAAK,gBAAgB;AAEtC,UAAA;AAIA,UAAA,aAAa,WAAW,GAAG;AAC7B,cAAM,KAAK,QAAQ;MACV,WAAA,KAAK,QAAQ,UAAU,GAAG;AACnC,gBAAM,KAAA,aAAa,aAAa,SAAS,CAAC,MAApC,OAAA,SAAA,GAAuC,QAAO;MAAA,OAC/C;AACL,cAAM,YAAY,MAAqB,KAAK,QAAQ,KAAK,EAAE,KAAK,IAAI;AAChE,YAAA,WAAW,aAAa,SAAS;AAC9B,eAAA,YAAY,KAAK,UAAU,KAAK,CAAC,QAAQ,QAAQ,IAAI,GAAG;AACvD,gBAAA,OAAO,aAAa,QAAQ;AAClC,cAAI,UAAU,KAAK,IAAI,MAAM,MAAM;AACvB,sBAAA,KAAK,IAAI,IAAI,KAAK;UAAA;AAG9B;QAAA;AAGI,cAAA,KAAK,IAAI,GAAG,UAAU,OAAO,CAAC,QAAuB,QAAQ,IAAI,CAAC;MAAA;AAG1E,aAAO,KAAK;QACV,MAAM,KAAK,QAAQ,eAAe,KAAK,QAAQ;QAC/C;MACF;IACF;AAEQ,SAAA,kBAAkB,CACxB,QACA;MACE;MACA;IAAA,MAKC;AACH,WAAK,QAAQ,WAAW,QAAQ,EAAE,UAAU,YAAA,GAAe,IAAI;IACjE;AAEA,SAAA,UAAU,MAAM;AACT,WAAA,gBAAA,oBAAoB,IAAI;AAC7B,WAAK,OAAO,KAAK;IACnB;AAzpBE,SAAK,WAAW,IAAI;EAAA;AA0pBxB;AAEA,IAAM,0BAA0B,CAC9B,KACA,MACA,iBACA,UACG;AACH,SAAO,OAAO,MAAM;AACZ,UAAA,UAAW,MAAM,QAAQ,IAAK;AAC9B,UAAA,eAAe,gBAAgB,MAAM;AAE3C,QAAI,eAAe,OAAO;AACxB,YAAM,SAAS;IAAA,WACN,eAAe,OAAO;AAC/B,aAAO,SAAS;IAAA,OACX;AACE,aAAA;IAAA;EACT;AAGF,MAAI,MAAM,GAAG;AACX,WAAO,MAAM;EAAA,OACR;AACE,WAAA;EAAA;AAEX;AAEA,SAAS,eAAe;EACtB;EACA;EACA;EACA;AACF,GAKG;AACK,QAAA,YAAY,aAAa,SAAS;AACxC,QAAM,YAAY,CAAC,UAAkB,aAAa,KAAK,EAAG;AAGtD,MAAA,aAAa,UAAU,OAAO;AACzB,WAAA;MACL,YAAY;MACZ,UAAU;IACZ;EAAA;AAGF,MAAI,aAAa;IACf;IACA;IACA;IACA;EACF;AACA,MAAI,WAAW;AAEf,MAAI,UAAU,GAAG;AACf,WACE,WAAW,aACX,aAAa,QAAQ,EAAG,MAAM,eAAe,WAC7C;AACA;IAAA;EACF,WACS,QAAQ,GAAG;AAGpB,UAAM,aAAa,MAAM,KAAK,EAAE,KAAK,CAAC;AAEpC,WAAA,WAAW,aACX,WAAW,KAAK,CAAC,QAAQ,MAAM,eAAe,SAAS,GACvD;AACM,YAAA,OAAO,aAAa,QAAQ;AACvB,iBAAA,KAAK,IAAI,IAAI,KAAK;AAC7B;IAAA;AAKF,UAAM,eAAe,MAAM,KAAK,EAAE,KAAK,eAAe,SAAS;AACxD,WAAA,cAAc,KAAK,aAAa,KAAK,CAAC,QAAQ,OAAO,YAAY,GAAG;AACnE,YAAA,OAAO,aAAa,UAAU;AACvB,mBAAA,KAAK,IAAI,IAAI,KAAK;AAC/B;IAAA;AAIF,iBAAa,KAAK,IAAI,GAAG,aAAc,aAAa,KAAM;AAE1D,eAAW,KAAK,IAAI,WAAW,YAAY,QAAQ,IAAK,WAAW,MAAO;EAAA;AAGrE,SAAA,EAAE,YAAY,SAAS;AAChC;;;ACvoCA,IAAM,4BACJ,OAAO,aAAa,cAAoB,wBAAwB;AAElE,SAAS,mBAIP,SAC2C;AACrC,QAAA,WAAiB,iBAAW,OAAO,CAAA,IAAK,CAAA,CAAE,EAAE,CAAC;AAEnD,QAAM,kBAAoE;IACxE,GAAG;IACH,UAAU,CAACE,WAAU,SAAS;;AAC5B,UAAI,MAAM;AACR,wCAAU,QAAQ;MAAA,OACb;AACI,iBAAA;MAAA;AAEH,OAAA,KAAA,QAAA,aAAA,OAAA,SAAA,GAAA,KAAA,SAAWA,WAAU,IAAA;IAAI;EAErC;AAEM,QAAA,CAAC,QAAQ,IAAU;IACvB,MAAM,IAAI,YAA0C,eAAe;EACrE;AAEA,WAAS,WAAW,eAAe;AAEnC,4BAA0B,MAAM;AAC9B,WAAO,SAAS,UAAU;EAC5B,GAAG,CAAA,CAAE;AAEL,4BAA0B,MAAM;AAC9B,WAAO,SAAS,YAAY;EAAA,CAC7B;AAEM,SAAA;AACT;AAEO,SAAS,eAId,SAI2C;AAC3C,SAAO,mBAAiD;IACtD;IACA;IACA,YAAY;IACZ,GAAG;EAAA,CACJ;AACH;;;ACtE0D,IAAAC,iBAA6K;;;ACAvO,IAAAC,gBAAyB;;;ACAzB,mBAA+C;;;ACA/C,IAAI,IAAE,OAAO;AAAe,IAAI,IAAE,CAACC,KAAEC,IAAEC,OAAID,MAAKD,MAAE,EAAEA,KAAEC,IAAE,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAMC,GAAC,CAAC,IAAEF,IAAEC,EAAC,IAAEC;AAAE,IAAI,IAAE,CAACF,KAAEC,IAAEC,QAAK,EAAEF,KAAE,OAAOC,MAAG,WAASA,KAAE,KAAGA,IAAEC,EAAC,GAAEA;AAAG,IAAM,IAAN,MAAO;AAAA,EAAC,cAAa;AAAC,MAAE,MAAK,WAAU,KAAK,OAAO,CAAC;AAAE,MAAE,MAAK,gBAAe,SAAS;AAAE,MAAE,MAAK,aAAY,CAAC;AAAA,EAAC;AAAA,EAAC,IAAID,IAAE;AAAC,SAAK,YAAUA,OAAI,KAAK,eAAa,WAAU,KAAK,YAAU,GAAE,KAAK,UAAQA;AAAA,EAAE;AAAA,EAAC,QAAO;AAAC,SAAK,IAAI,KAAK,OAAO,CAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,WAAM,EAAE,KAAK;AAAA,EAAS;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,YAAU;AAAA,EAAQ;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,YAAU;AAAA,EAAQ;AAAA,EAAC,SAAQ;AAAC,WAAO,OAAO,UAAQ,eAAa,OAAO,YAAU,cAAY,WAAS;AAAA,EAAQ;AAAA,EAAC,UAAS;AAAC,SAAK,iBAAe,cAAY,KAAK,eAAa;AAAA,EAAW;AAAA,EAAC,IAAI,oBAAmB;AAAC,WAAO,KAAK,iBAAe;AAAA,EAAU;AAAC;AAAC,IAAI,IAAE,IAAI;;;ADAvpB,IAAI,IAAE,CAACE,IAAEC,QAAI;AAAC,IAAE,eAAS,aAAAC,WAAEF,IAAEC,GAAC,QAAE,aAAAE,iBAAEH,IAAEC,GAAC;AAAC;;;AEAnI,IAAAG,gBAAuB;AAA0E,SAASC,GAAEC,IAAE;AAAC,MAAIC,SAAE,cAAAC,QAAEF,EAAC;AAAE,SAAO,EAAE,MAAI;AAAC,IAAAC,GAAE,UAAQD;AAAA,EAAC,GAAE,CAACA,EAAC,CAAC,GAAEC;AAAC;;;AHAD,SAASE,GAAEC,IAAEC,KAAE;AAAC,MAAG,CAACC,KAAEC,GAAC,QAAE,cAAAC,UAAEJ,EAAC,GAAEK,KAAED,GAAEJ,EAAC;AAAE,SAAO,EAAE,MAAIG,IAAEE,GAAE,OAAO,GAAE,CAACA,IAAEF,KAAE,GAAGF,GAAC,CAAC,GAAEC;AAAC;;;AIAtO,IAAAI,gBAAqC;;;ACArC,IAAAC,gBAAa;AAA+D,IAAIC,KAAE,SAASC,KAAE;AAAC,MAAIC,KAAEC,GAAEF,GAAC;AAAE,SAAO,cAAAG,QAAE,YAAY,IAAIC,OAAIH,GAAE,QAAQ,GAAGG,EAAC,GAAE,CAACH,EAAC,CAAC;AAAC;;;ADAnE,SAAS,EAAEI,KAAEC,IAAEC,KAAE;AAAC,MAAG,CAACC,IAAEC,GAAC,QAAE,cAAAC,UAAEH,GAAC,GAAEI,KAAEN,QAAI,QAAOO,UAAE,cAAAC,QAAEF,EAAC,GAAEG,UAAE,cAAAD,QAAE,KAAE,GAAEE,UAAE,cAAAF,QAAE,KAAE;AAAE,SAAOF,MAAG,CAACC,IAAE,WAAS,CAACE,IAAE,WAASA,IAAE,UAAQ,MAAGF,IAAE,UAAQD,IAAE,QAAQ,MAAM,+JAA+J,KAAG,CAACA,MAAGC,IAAE,WAAS,CAACG,IAAE,YAAUA,IAAE,UAAQ,MAAGH,IAAE,UAAQD,IAAE,QAAQ,MAAM,+JAA+J,IAAG,CAACA,KAAEN,MAAEG,IAAEK,GAAE,CAAAG,QAAIL,MAAGF,IAAEO,EAAC,GAAEV,MAAG,OAAK,SAAOA,GAAEU,EAAC,EAAE,CAAC;AAAC;;;AEAtpB,IAAAC,gBAAwC;;;ACAxC,SAASC,GAAEC,IAAE;AAAC,SAAO,kBAAgB,aAAW,eAAeA,EAAC,IAAE,QAAQ,QAAQ,EAAE,KAAKA,EAAC,EAAE,MAAM,CAAAC,QAAG,WAAW,MAAI;AAAC,UAAMA;AAAA,EAAC,CAAC,CAAC;AAAC;;;ACAnF,SAASC,KAAG;AAAC,MAAIC,KAAE,CAAC,GAAEC,KAAE,EAAC,iBAAiBC,IAAEC,KAAEC,KAAEC,KAAE;AAAC,WAAOH,GAAE,iBAAiBC,KAAEC,KAAEC,GAAC,GAAEJ,GAAE,IAAI,MAAIC,GAAE,oBAAoBC,KAAEC,KAAEC,GAAC,CAAC;AAAA,EAAC,GAAE,yBAAyBH,IAAE;AAAC,QAAIC,MAAE,sBAAsB,GAAGD,EAAC;AAAE,WAAOD,GAAE,IAAI,MAAI,qBAAqBE,GAAC,CAAC;AAAA,EAAC,GAAE,aAAaD,IAAE;AAAC,WAAOD,GAAE,sBAAsB,MAAIA,GAAE,sBAAsB,GAAGC,EAAC,CAAC;AAAA,EAAC,GAAE,cAAcA,IAAE;AAAC,QAAIC,MAAE,WAAW,GAAGD,EAAC;AAAE,WAAOD,GAAE,IAAI,MAAI,aAAaE,GAAC,CAAC;AAAA,EAAC,GAAE,aAAaD,IAAE;AAAC,QAAIC,MAAE,EAAC,SAAQ,KAAE;AAAE,WAAOA,GAAE,MAAI;AAAC,MAAAA,IAAE,WAASD,GAAE,CAAC,EAAE;AAAA,IAAC,CAAC,GAAED,GAAE,IAAI,MAAI;AAAC,MAAAE,IAAE,UAAQ;AAAA,IAAE,CAAC;AAAA,EAAC,GAAE,MAAMD,IAAEC,KAAEC,KAAE;AAAC,QAAIC,MAAEH,GAAE,MAAM,iBAAiBC,GAAC;AAAE,WAAO,OAAO,OAAOD,GAAE,OAAM,EAAC,CAACC,GAAC,GAAEC,IAAC,CAAC,GAAE,KAAK,IAAI,MAAI;AAAC,aAAO,OAAOF,GAAE,OAAM,EAAC,CAACC,GAAC,GAAEE,IAAC,CAAC;AAAA,IAAC,CAAC;AAAA,EAAC,GAAE,MAAMH,IAAE;AAAC,QAAIC,MAAEJ,GAAE;AAAE,WAAOG,GAAEC,GAAC,GAAE,KAAK,IAAI,MAAIA,IAAE,QAAQ,CAAC;AAAA,EAAC,GAAE,IAAID,IAAE;AAAC,WAAOF,GAAE,KAAKE,EAAC,GAAE,MAAI;AAAC,UAAIC,MAAEH,GAAE,QAAQE,EAAC;AAAE,UAAGC,OAAG,EAAE,UAAQC,OAAKJ,GAAE,OAAOG,KAAE,CAAC,EAAE,CAAAC,IAAE;AAAA,IAAC;AAAA,EAAC,GAAE,UAAS;AAAC,aAAQF,MAAKF,GAAE,OAAO,CAAC,EAAE,CAAAE,GAAE;AAAA,EAAC,EAAC;AAAE,SAAOD;AAAC;;;AFAnvB,SAAS,IAAG;AAAC,MAAG,CAACK,EAAC,QAAE,cAAAC,UAAEA,EAAC;AAAE,aAAO,cAAAC,WAAE,MAAI,MAAIF,GAAE,QAAQ,GAAE,CAACA,EAAC,CAAC,GAAEA;AAAC;;;AGA5J,IAAAG,gBAAa;;;ACAnB,IAAAC,KAAgB;AAA8C,SAASC,KAAG;AAAC,MAAIC,KAAE,OAAO,YAAU;AAAY,SAAM,0BAAyBC,MAAG,CAAAC,QAAGA,IAAE,sBAAsBD,EAAC,EAAE,MAAI,MAAI;AAAA,EAAC,GAAE,MAAI,OAAG,MAAI,CAACD,EAAC,IAAE;AAAE;AAAC,SAASG,KAAG;AAAC,MAAIH,KAAED,GAAE,GAAE,CAACK,IAAEC,EAAC,IAAI,YAAS,EAAE,iBAAiB;AAAE,SAAOD,MAAG,EAAE,sBAAoB,SAAIC,GAAE,KAAE,GAAI,aAAU,MAAI;AAAC,IAAAD,OAAI,QAAIC,GAAE,IAAE;AAAA,EAAC,GAAE,CAACD,EAAC,CAAC,GAAI,aAAU,MAAI,EAAE,QAAQ,GAAE,CAAC,CAAC,GAAEJ,KAAE,QAAGI;AAAC;;;ADA7X,IAAIE;AAA2M,IAAI,KAAGA,KAAE,cAAAC,QAAE,UAAQ,OAAKD,KAAE,WAAU;AAAC,MAAIE,KAAEC,GAAE,GAAE,CAACC,IAAEC,GAAC,IAAE,cAAAJ,QAAE,SAASC,KAAE,MAAI,EAAE,OAAO,IAAE,IAAI;AAAE,SAAO,EAAE,MAAI;AAAC,IAAAE,OAAI,QAAMC,IAAE,EAAE,OAAO,CAAC;AAAA,EAAC,GAAE,CAACD,EAAC,CAAC,GAAEA,MAAG,OAAK,KAAGA,KAAE;AAAM;;;AEArW,IAAAE,iBAAsC;;;ACAtC,SAAS,EAAEC,IAAEC,OAAKC,KAAE;AAAC,MAAGF,MAAKC,IAAE;AAAC,QAAIE,KAAEF,GAAED,EAAC;AAAE,WAAO,OAAOG,MAAG,aAAWA,GAAE,GAAGD,GAAC,IAAEC;AAAA,EAAC;AAAC,MAAIC,MAAE,IAAI,MAAM,oBAAoBJ,EAAC,iEAAiE,OAAO,KAAKC,EAAC,EAAE,IAAI,CAAAE,OAAG,IAAIA,EAAC,GAAG,EAAE,KAAK,IAAI,CAAC,GAAG;AAAE,QAAM,MAAM,qBAAmB,MAAM,kBAAkBC,KAAE,CAAC,GAAEA;AAAC;;;ACApQ,SAASC,GAAEC,IAAE;AAAC,SAAO,EAAE,WAAS,OAAKA,cAAa,OAAKA,GAAE,gBAAcA,MAAG,QAAMA,GAAE,eAAe,SAAS,KAAGA,GAAE,mBAAmB,OAAKA,GAAE,QAAQ,gBAAc;AAAQ;;;ACAtE,IAAIC,KAAE,CAAC,0BAAyB,cAAa,WAAU,cAAa,0BAAyB,UAAS,yBAAwB,0BAAyB,0BAA0B,EAAE,IAAI,CAAAC,OAAG,GAAGA,EAAC,uBAAuB,EAAE,KAAK,GAAG;AAAE,IAAI,KAAG,CAAAC,QAAIA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,aAAW,EAAE,IAAE,cAAaA,GAAEA,GAAE,WAAS,EAAE,IAAE,YAAWA,KAAI,KAAG,CAAC,CAAC;AAAtK,IAAwK,KAAG,CAAAC,SAAIA,IAAEA,IAAE,QAAM,CAAC,IAAE,SAAQA,IAAEA,IAAE,WAAS,CAAC,IAAE,YAAWA,IAAEA,IAAE,UAAQ,CAAC,IAAE,WAAUA,IAAEA,IAAE,YAAU,CAAC,IAAE,aAAYA,MAAI,KAAG,CAAC,CAAC;AAA9R,IAAgS,KAAG,CAAAC,SAAIA,IAAEA,IAAE,WAAS,EAAE,IAAE,YAAWA,IAAEA,IAAE,OAAK,CAAC,IAAE,QAAOA,MAAI,KAAG,CAAC,CAAC;AAAE,SAASC,GAAEJ,KAAE,SAAS,MAAK;AAAC,SAAOA,MAAG,OAAK,CAAC,IAAE,MAAM,KAAKA,GAAE,iBAAiBD,EAAC,CAAC,EAAE,KAAK,CAACM,IAAEF,QAAI,KAAK,MAAME,GAAE,YAAU,OAAO,qBAAmBF,IAAE,YAAU,OAAO,iBAAiB,CAAC;AAAC;AAAC,IAAIG,MAAG,CAAAH,SAAIA,IAAEA,IAAE,SAAO,CAAC,IAAE,UAASA,IAAEA,IAAE,QAAM,CAAC,IAAE,SAAQA,MAAIG,MAAG,CAAC,CAAC;AAAE,SAAS,EAAEN,IAAEK,KAAE,GAAE;AAAC,MAAIF;AAAE,SAAOH,SAAMG,MAAED,GAAEF,EAAC,MAAI,OAAK,SAAOG,IAAE,QAAM,QAAG,EAAEE,IAAE,EAAC,CAAC,CAAC,IAAG;AAAC,WAAOL,GAAE,QAAQD,EAAC;AAAA,EAAC,GAAE,CAAC,CAAC,IAAG;AAAC,QAAIQ,MAAEP;AAAE,WAAKO,QAAI,QAAM;AAAC,UAAGA,IAAE,QAAQR,EAAC,EAAE,QAAM;AAAG,MAAAQ,MAAEA,IAAE;AAAA,IAAa;AAAC,WAAM;AAAA,EAAE,EAAC,CAAC;AAAC;AAAC,SAAS,EAAEP,IAAE;AAAC,MAAIK,KAAEH,GAAEF,EAAC;AAAE,EAAAE,GAAE,EAAE,UAAU,MAAI;AAAC,IAAAG,MAAG,CAAC,EAAEA,GAAE,eAAc,CAAC,KAAG,EAAEL,EAAC;AAAA,EAAC,CAAC;AAAC;AAAC,IAAI,KAAG,CAAAG,SAAIA,IAAEA,IAAE,WAAS,CAAC,IAAE,YAAWA,IAAEA,IAAE,QAAM,CAAC,IAAE,SAAQA,MAAI,KAAG,CAAC,CAAC;AAAE,OAAO,UAAQ,eAAa,OAAO,YAAU,gBAAc,SAAS,iBAAiB,WAAU,CAAAH,OAAG;AAAC,EAAAA,GAAE,WAASA,GAAE,UAAQA,GAAE,YAAU,SAAS,gBAAgB,QAAQ,yBAAuB;AAAG,GAAE,IAAE,GAAE,SAAS,iBAAiB,SAAQ,CAAAA,OAAG;AAAC,EAAAA,GAAE,WAAS,IAAE,OAAO,SAAS,gBAAgB,QAAQ,yBAAuBA,GAAE,WAAS,MAAI,SAAS,gBAAgB,QAAQ,yBAAuB;AAAG,GAAE,IAAE;AAAG,SAAS,EAAEA,IAAE;AAAC,EAAAA,MAAG,QAAMA,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAC;AAAC,IAAI,IAAE,CAAC,YAAW,OAAO,EAAE,KAAK,GAAG;AAAE,SAAS,EAAEA,IAAE;AAAC,MAAIK,IAAEF;AAAE,UAAOA,OAAGE,KAAEL,MAAG,OAAK,SAAOA,GAAE,YAAU,OAAK,SAAOK,GAAE,KAAKL,IAAE,CAAC,MAAI,OAAKG,MAAE;AAAE;AAAC,SAASK,GAAER,IAAEK,KAAE,CAAAF,QAAGA,KAAE;AAAC,SAAOH,GAAE,MAAM,EAAE,KAAK,CAACG,KAAEI,QAAI;AAAC,QAAIL,MAAEG,GAAEF,GAAC,GAAEM,KAAEJ,GAAEE,GAAC;AAAE,QAAGL,QAAI,QAAMO,OAAI,KAAK,QAAO;AAAE,QAAIR,KAAEC,IAAE,wBAAwBO,EAAC;AAAE,WAAOR,KAAE,KAAK,8BAA4B,KAAGA,KAAE,KAAK,8BAA4B,IAAE;AAAA,EAAC,CAAC;AAAC;AAAC,SAAS,EAAED,IAAEK,IAAE;AAAC,SAAO,EAAED,GAAE,GAAEC,IAAE,EAAC,YAAWL,GAAC,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAEK,IAAE,EAAC,QAAOF,MAAE,MAAG,YAAWI,MAAE,MAAK,cAAaL,MAAE,CAAC,EAAC,IAAE,CAAC,GAAE;AAAC,MAAIO,KAAE,MAAM,QAAQT,EAAC,IAAEA,GAAE,SAAO,IAAEA,GAAE,CAAC,EAAE,gBAAc,WAASA,GAAE,eAAcC,KAAE,MAAM,QAAQD,EAAC,IAAEG,MAAEK,GAAER,EAAC,IAAEA,KAAEI,GAAEJ,EAAC;AAAE,EAAAE,IAAE,SAAO,KAAGD,GAAE,SAAO,MAAIA,KAAEA,GAAE,OAAO,CAAAS,QAAG,CAACR,IAAE,SAASQ,GAAC,CAAC,IAAGH,MAAEA,OAAG,OAAKA,MAAEE,GAAE;AAAc,MAAIE,MAAG,MAAI;AAAC,QAAGN,KAAE,EAAE,QAAO;AAAE,QAAGA,KAAE,GAAG,QAAM;AAAG,UAAM,IAAI,MAAM,+DAA+D;AAAA,EAAC,GAAG,GAAEO,MAAG,MAAI;AAAC,QAAGP,KAAE,EAAE,QAAO;AAAE,QAAGA,KAAE,EAAE,QAAO,KAAK,IAAI,GAAEJ,GAAE,QAAQM,GAAC,CAAC,IAAE;AAAE,QAAGF,KAAE,EAAE,QAAO,KAAK,IAAI,GAAEJ,GAAE,QAAQM,GAAC,CAAC,IAAE;AAAE,QAAGF,KAAE,EAAE,QAAOJ,GAAE,SAAO;AAAE,UAAM,IAAI,MAAM,+DAA+D;AAAA,EAAC,GAAG,GAAEY,KAAER,KAAE,KAAG,EAAC,eAAc,KAAE,IAAE,CAAC,GAAES,MAAE,GAAEC,MAAEd,GAAE,QAAOe;AAAE,KAAE;AAAC,QAAGF,OAAGC,OAAGD,MAAEC,OAAG,EAAE,QAAO;AAAE,QAAIL,MAAEE,KAAEE;AAAE,QAAGT,KAAE,GAAG,CAAAK,OAAGA,MAAEK,OAAGA;AAAA,SAAM;AAAC,UAAGL,MAAE,EAAE,QAAO;AAAE,UAAGA,OAAGK,IAAE,QAAO;AAAA,IAAC;AAAC,IAAAC,MAAEf,GAAES,GAAC,GAAEM,OAAG,QAAMA,IAAE,MAAMH,EAAC,GAAEC,OAAGH;AAAA,EAAC,SAAOK,QAAIP,GAAE;AAAe,SAAOJ,KAAE,KAAG,EAAEW,GAAC,KAAGA,IAAE,OAAO,GAAE;AAAC;;;ACAz5F,SAASC,KAAG;AAAC,SAAM,WAAW,KAAK,OAAO,UAAU,QAAQ,KAAG,QAAQ,KAAK,OAAO,UAAU,QAAQ,KAAG,OAAO,UAAU,iBAAe;AAAC;AAAC,SAASC,KAAG;AAAC,SAAM,YAAY,KAAK,OAAO,UAAU,SAAS;AAAC;AAAC,SAAS,IAAG;AAAC,SAAOD,GAAE,KAAGC,GAAE;AAAC;;;ACAtO,IAAAC,gBAA0B;AAA+D,SAASC,GAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,MAAEC,GAAEH,EAAC;AAAE,oBAAAI,WAAE,MAAI;AAAC,aAASC,IAAEC,KAAE;AAAC,MAAAJ,IAAE,QAAQI,GAAC;AAAA,IAAC;AAAC,WAAO,SAAS,iBAAiBP,IAAEM,KAAEJ,EAAC,GAAE,MAAI,SAAS,oBAAoBF,IAAEM,KAAEJ,EAAC;AAAA,EAAC,GAAE,CAACF,IAAEE,EAAC,CAAC;AAAC;;;ACA/O,IAAAM,gBAA0B;AAA+D,SAASC,GAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,MAAEJ,GAAEE,EAAC;AAAE,oBAAAG,WAAE,MAAI;AAAC,aAASC,IAAEC,IAAE;AAAC,MAAAH,IAAE,QAAQG,EAAC;AAAA,IAAC;AAAC,WAAO,OAAO,iBAAiBN,IAAEK,KAAEH,EAAC,GAAE,MAAI,OAAO,oBAAoBF,IAAEK,KAAEH,EAAC;AAAA,EAAC,GAAE,CAACF,IAAEE,EAAC,CAAC;AAAC;;;ANA0D,SAASK,GAAEC,KAAEC,KAAEC,MAAE,MAAG;AAAC,MAAIC,SAAE,eAAAC,QAAE,KAAE;AAAE,qBAAAC,WAAE,MAAI;AAAC,0BAAsB,MAAI;AAAC,MAAAF,GAAE,UAAQD;AAAA,IAAC,CAAC;AAAA,EAAC,GAAE,CAACA,GAAC,CAAC;AAAE,WAASI,IAAEC,IAAEC,IAAE;AAAC,QAAG,CAACL,GAAE,WAASI,GAAE,iBAAiB;AAAO,QAAIE,MAAED,GAAED,EAAC;AAAE,QAAGE,QAAI,QAAM,CAACA,IAAE,YAAY,EAAE,SAASA,GAAC,KAAG,CAACA,IAAE,YAAY;AAAO,QAAIC,KAAE,SAASC,IAAEC,IAAE;AAAC,aAAO,OAAOA,MAAG,aAAWD,IAAEC,GAAE,CAAC,IAAE,MAAM,QAAQA,EAAC,KAAGA,cAAa,MAAIA,KAAE,CAACA,EAAC;AAAA,IAAC,EAAEZ,GAAC;AAAE,aAAQW,OAAKD,IAAE;AAAC,UAAGC,QAAI,KAAK;AAAS,UAAIC,KAAED,eAAa,cAAYA,MAAEA,IAAE;AAAQ,UAAGC,MAAG,QAAMA,GAAE,SAASH,GAAC,KAAGF,GAAE,YAAUA,GAAE,aAAa,EAAE,SAASK,EAAC,EAAE;AAAA,IAAM;AAAC,WAAM,CAAC,EAAEH,KAAEI,GAAE,KAAK,KAAGJ,IAAE,aAAW,MAAIF,GAAE,eAAe,GAAEN,IAAEM,IAAEE,GAAC;AAAA,EAAC;AAAC,MAAIK,UAAE,eAAAV,QAAE,IAAI;AAAE,EAAAC,GAAE,eAAc,CAAAE,OAAG;AAAC,QAAIC,IAAEC;AAAE,IAAAN,GAAE,YAAUW,IAAE,YAAUL,OAAGD,KAAED,GAAE,iBAAe,OAAK,SAAOC,GAAE,KAAKD,EAAC,MAAI,OAAK,SAAOE,IAAE,CAAC,MAAIF,GAAE;AAAA,EAAO,GAAE,IAAE,GAAEF,GAAE,aAAY,CAAAE,OAAG;AAAC,QAAIC,IAAEC;AAAE,IAAAN,GAAE,YAAUW,IAAE,YAAUL,OAAGD,KAAED,GAAE,iBAAe,OAAK,SAAOC,GAAE,KAAKD,EAAC,MAAI,OAAK,SAAOE,IAAE,CAAC,MAAIF,GAAE;AAAA,EAAO,GAAE,IAAE,GAAEF,GAAE,SAAQ,CAAAE,OAAG;AAAC,MAAE,KAAGO,IAAE,YAAUR,IAAEC,IAAE,MAAIO,IAAE,OAAO,GAAEA,IAAE,UAAQ;AAAA,EAAK,GAAE,IAAE,GAAET,GAAE,YAAW,CAAAE,OAAGD,IAAEC,IAAE,MAAIA,GAAE,kBAAkB,cAAYA,GAAE,SAAO,IAAI,GAAE,IAAE,GAAEP,GAAE,QAAO,CAAAO,OAAGD,IAAEC,IAAE,MAAI,OAAO,SAAS,yBAAyB,oBAAkB,OAAO,SAAS,gBAAc,IAAI,GAAE,IAAE;AAAC;;;AOA51C,IAAAQ,iBAAwB;AAA6D,SAASC,MAAKC,IAAE;AAAC,aAAO,eAAAC,SAAE,MAAIC,GAAE,GAAGF,EAAC,GAAE,CAAC,GAAGA,EAAC,CAAC;AAAC;;;ACAlI,IAAAG,iBAAyB;AAA0E,SAASC,GAAEC,KAAE;AAAC,MAAIC;AAAE,MAAGD,IAAE,KAAK,QAAOA,IAAE;AAAK,MAAIE,MAAGD,KAAED,IAAE,OAAK,OAAKC,KAAE;AAAS,MAAG,OAAOC,MAAG,YAAUA,GAAE,YAAY,MAAI,SAAS,QAAM;AAAQ;AAAC,SAASC,GAAEH,KAAEE,IAAE;AAAC,MAAG,CAACD,IAAEG,GAAC,QAAE,eAAAC,UAAE,MAAIN,GAAEC,GAAC,CAAC;AAAE,SAAO,EAAE,MAAI;AAAC,IAAAI,IAAEL,GAAEC,GAAC,CAAC;AAAA,EAAC,GAAE,CAACA,IAAE,MAAKA,IAAE,EAAE,CAAC,GAAE,EAAE,MAAI;AAAC,IAAAC,MAAGC,GAAE,WAASA,GAAE,mBAAmB,qBAAmB,CAACA,GAAE,QAAQ,aAAa,MAAM,KAAGE,IAAE,QAAQ;AAAA,EAAC,GAAE,CAACH,IAAEC,EAAC,CAAC,GAAED;AAAC;;;ACA9a,IAAAK,iBAAsC;AAAkD,IAAIC,KAAE,OAAO;AAAE,SAASC,GAAEC,KAAEC,KAAE,MAAG;AAAC,SAAO,OAAO,OAAOD,KAAE,EAAC,CAACF,EAAC,GAAEG,GAAC,CAAC;AAAC;AAAC,SAASC,MAAKF,KAAE;AAAC,MAAIC,SAAE,eAAAE,QAAEH,GAAC;AAAE,qBAAAI,WAAE,MAAI;AAAC,IAAAH,GAAE,UAAQD;AAAA,EAAC,GAAE,CAACA,GAAC,CAAC;AAAE,MAAIK,MAAEC,GAAE,CAAAC,OAAG;AAAC,aAAQD,OAAKL,GAAE,QAAQ,CAAAK,OAAG,SAAO,OAAOA,OAAG,aAAWA,IAAEC,EAAC,IAAED,IAAE,UAAQC;AAAA,EAAE,CAAC;AAAE,SAAOP,IAAE,MAAM,CAAAO,OAAGA,MAAG,SAAOA,MAAG,OAAK,SAAOA,GAAET,EAAC,EAAE,IAAE,SAAOO;AAAC;;;ACA/V,IAAAG,iBAAuB;AAAQ,SAASC,GAAEC,IAAE;AAAC,SAAM,CAACA,GAAE,SAAQA,GAAE,OAAO;AAAC;AAAC,SAASC,KAAG;AAAC,MAAID,SAAE,eAAAE,QAAE,CAAC,IAAG,EAAE,CAAC;AAAE,SAAM,EAAC,SAASC,IAAE;AAAC,QAAIC,KAAEL,GAAEI,EAAC;AAAE,WAAOH,GAAE,QAAQ,CAAC,MAAII,GAAE,CAAC,KAAGJ,GAAE,QAAQ,CAAC,MAAII,GAAE,CAAC,IAAE,SAAIJ,GAAE,UAAQI,IAAE;AAAA,EAAG,GAAE,OAAOD,IAAE;AAAC,IAAAH,GAAE,UAAQD,GAAEI,EAAC;AAAA,EAAC,EAAC;AAAC;;;ACApO,IAAAE,iBAAsC;AAA+H,SAASC,GAAE,EAAC,WAAUC,IAAE,QAAOC,KAAE,MAAKC,IAAE,SAAQC,MAAE,KAAE,GAAE;AAAC,MAAIC,UAAE,eAAAC,QAAEJ,GAAC,GAAEK,UAAE,eAAAD,QAAEH,EAAC;AAAE,qBAAAK,WAAE,MAAI;AAAC,IAAAH,IAAE,UAAQH,KAAEK,IAAE,UAAQJ;AAAA,EAAC,GAAE,CAACD,KAAEC,EAAC,CAAC,GAAE,EAAE,MAAI;AAAC,QAAG,CAACF,MAAG,CAACG,IAAE;AAAO,QAAIK,KAAEJ,GAAEJ,EAAC;AAAE,QAAG,CAACQ,GAAE;AAAO,QAAIC,MAAEL,IAAE,SAAQM,KAAEJ,IAAE,SAAQK,MAAE,OAAO,OAAO,CAAAC,OAAGH,IAAEG,EAAC,GAAE,EAAC,YAAWH,IAAC,CAAC,GAAEI,MAAEL,GAAE,iBAAiBR,IAAE,WAAW,cAAaW,KAAE,KAAE;AAAE,WAAKE,IAAE,SAAS,IAAG,CAAAH,GAAEG,IAAE,WAAW;AAAA,EAAC,GAAE,CAACb,IAAEG,KAAEC,KAAEE,GAAC,CAAC;AAAC;;;ACA7e,IAAAQ,iBAAsC;AAAkD,SAASC,GAAEC,KAAEC,KAAE;AAAC,MAAIC,SAAE,eAAAC,QAAE,CAAC,CAAC,GAAEC,KAAEC,GAAEL,GAAC;AAAE,qBAAAM,WAAE,MAAI;AAAC,QAAID,MAAE,CAAC,GAAGH,GAAE,OAAO;AAAE,aAAO,CAACK,IAAEC,GAAC,KAAIP,IAAE,QAAQ,EAAE,KAAGC,GAAE,QAAQK,EAAC,MAAIC,KAAE;AAAC,UAAIC,MAAEL,GAAEH,KAAEI,GAAC;AAAE,aAAOH,GAAE,UAAQD,KAAEQ;AAAA,IAAC;AAAA,EAAC,GAAE,CAACL,IAAE,GAAGH,GAAC,CAAC;AAAC;;;ACApP,IAAAS,iBAA+H;;;ACA/H,SAASC,MAAKC,IAAE;AAAC,SAAO,MAAM,KAAK,IAAI,IAAIA,GAAE,QAAQ,CAAAC,OAAG,OAAOA,MAAG,WAASA,GAAE,MAAM,GAAG,IAAE,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AAAC;;;ADAiG,IAAIC,MAAG,CAAAC,QAAIA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,iBAAe,CAAC,IAAE,kBAAiBA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,KAAID,MAAG,CAAC,CAAC;AAArG,IAAuG,KAAG,CAAAE,QAAIA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,KAAI,KAAG,CAAC,CAAC;AAAE,SAAS,EAAE,EAAC,UAASC,IAAE,YAAWC,KAAE,MAAKF,IAAE,YAAWD,IAAE,UAASI,KAAE,SAAQC,MAAE,MAAG,MAAKC,KAAE,WAAUC,IAAC,GAAE;AAAC,EAAAA,MAAEA,OAAG,OAAKA,MAAE;AAAE,MAAIC,MAAE,EAAEL,KAAED,EAAC;AAAE,MAAGG,IAAE,QAAOI,GAAED,KAAEP,IAAED,IAAEM,KAAEC,GAAC;AAAE,MAAIG,KAAEN,OAAG,OAAKA,MAAE;AAAE,MAAGM,KAAE,GAAE;AAAC,QAAG,EAAC,QAAOC,MAAE,OAAG,GAAGC,IAAC,IAAEJ;AAAE,QAAGG,IAAE,QAAOF,GAAEG,KAAEX,IAAED,IAAEM,KAAEC,GAAC;AAAA,EAAC;AAAC,MAAGG,KAAE,GAAE;AAAC,QAAG,EAAC,SAAQC,MAAE,MAAG,GAAGC,IAAC,IAAEJ;AAAE,WAAO,EAAEG,MAAE,IAAE,GAAE,EAAC,CAAC,CAAC,IAAG;AAAC,aAAO;AAAA,IAAI,GAAE,CAAC,CAAC,IAAG;AAAC,aAAOF,GAAE,EAAC,GAAGG,KAAE,QAAO,MAAG,OAAM,EAAC,SAAQ,OAAM,EAAC,GAAEX,IAAED,IAAEM,KAAEC,GAAC;AAAA,IAAC,EAAC,CAAC;AAAA,EAAC;AAAC,SAAOE,GAAED,KAAEP,IAAED,IAAEM,KAAEC,GAAC;AAAC;AAAC,SAASE,GAAEP,IAAEC,MAAE,CAAC,GAAEF,IAAED,IAAEI,KAAE;AAAC,MAAG,EAAC,IAAGC,MAAEJ,IAAE,UAASK,KAAE,SAAQC,MAAE,OAAM,GAAGC,IAAC,IAAEK,GAAEX,IAAE,CAAC,WAAU,QAAQ,CAAC,GAAEQ,KAAER,GAAE,QAAM,SAAO,EAAC,CAACK,GAAC,GAAEL,GAAE,IAAG,IAAE,CAAC,GAAES,MAAE,OAAOL,OAAG,aAAWA,IAAEH,GAAC,IAAEG;AAAE,iBAAcE,OAAGA,IAAE,aAAW,OAAOA,IAAE,aAAW,eAAaA,IAAE,YAAUA,IAAE,UAAUL,GAAC;AAAG,MAAIS,MAAE,CAAC;AAAE,MAAGT,KAAE;AAAC,QAAIW,KAAE,OAAGC,MAAE,CAAC;AAAE,aAAO,CAACC,IAAEC,EAAC,KAAI,OAAO,QAAQd,GAAC,EAAE,QAAOc,MAAG,cAAYH,KAAE,OAAIG,OAAI,QAAIF,IAAE,KAAKC,EAAC;AAAE,IAAAF,OAAIF,IAAE,uBAAuB,IAAEG,IAAE,KAAK,GAAG;AAAA,EAAE;AAAC,MAAGV,QAAI,eAAAa,YAAG,OAAO,KAAK,EAAEV,GAAC,CAAC,EAAE,SAAO,GAAE;AAAC,QAAG,KAAC,eAAAW,gBAAER,GAAC,KAAG,MAAM,QAAQA,GAAC,KAAGA,IAAE,SAAO,EAAE,OAAM,IAAI,MAAM,CAAC,gCAA+B,IAAG,0BAA0BX,EAAC,kCAAiC,uDAAsD,OAAO,KAAKQ,GAAC,EAAE,IAAI,CAAAS,OAAG,OAAOA,EAAC,EAAE,EAAE,KAAK;AAAA,CACx6C,GAAE,IAAG,kCAAiC,CAAC,+FAA8F,0FAA0F,EAAE,IAAI,CAAAA,OAAG,OAAOA,EAAC,EAAE,EAAE,KAAK;AAAA,CACzP,CAAC,EAAE,KAAK;AAAA,CACR,CAAC;AAAE,QAAIH,KAAEH,IAAE,OAAMI,MAAE,QAAOD,MAAG,OAAK,SAAOA,GAAE,cAAY,aAAW,IAAIG,OAAId,GAAEW,MAAG,OAAK,SAAOA,GAAE,UAAU,GAAGG,EAAC,GAAET,IAAE,SAAS,IAAEL,GAAEW,MAAG,OAAK,SAAOA,GAAE,WAAUN,IAAE,SAAS,GAAEQ,KAAED,MAAE,EAAC,WAAUA,IAAC,IAAE,CAAC;AAAE,eAAO,eAAAK,cAAET,KAAE,OAAO,OAAO,CAAC,GAAE,EAAEA,IAAE,OAAM,EAAEE,GAAEL,KAAE,CAAC,KAAK,CAAC,CAAC,CAAC,GAAEI,KAAEF,IAAE,EAAC,KAAIN,IAAEO,IAAE,KAAID,GAAE,GAAG,EAAC,GAAEM,EAAC,CAAC;AAAA,EAAC;AAAC,aAAO,eAAAK,eAAEhB,KAAE,OAAO,OAAO,CAAC,GAAEQ,GAAEL,KAAE,CAAC,KAAK,CAAC,GAAEH,QAAI,eAAAa,YAAGR,IAAEL,QAAI,eAAAa,YAAGN,GAAC,GAAED,GAAC;AAAC;AAAC,SAASW,KAAG;AAAC,MAAIpB,SAAE,eAAAqB,QAAE,CAAC,CAAC,GAAEpB,UAAE,eAAAqB,aAAE,CAAAvB,OAAG;AAAC,aAAQD,MAAKE,GAAE,QAAQ,CAAAF,MAAG,SAAO,OAAOA,MAAG,aAAWA,GAAEC,EAAC,IAAED,GAAE,UAAQC;AAAA,EAAE,GAAE,CAAC,CAAC;AAAE,SAAM,IAAIA,OAAI;AAAC,QAAG,CAACA,GAAE,MAAM,CAAAD,OAAGA,MAAG,IAAI,EAAE,QAAOE,GAAE,UAAQD,IAAEE;AAAA,EAAC;AAAC;AAAC,SAAS,KAAKD,IAAE;AAAC,SAAOA,GAAE,MAAM,CAAAC,QAAGA,OAAG,IAAI,IAAE,SAAO,CAAAA,QAAG;AAAC,aAAQF,MAAKC,GAAE,CAAAD,MAAG,SAAO,OAAOA,MAAG,aAAWA,GAAEE,GAAC,IAAEF,GAAE,UAAQE;AAAA,EAAE;AAAC;AAAC,SAAS,KAAKD,IAAE;AAAC,MAAIF;AAAE,MAAGE,GAAE,WAAS,EAAE,QAAM,CAAC;AAAE,MAAGA,GAAE,WAAS,EAAE,QAAOA,GAAE,CAAC;AAAE,MAAIC,MAAE,CAAC,GAAEF,KAAE,CAAC;AAAE,WAAQG,OAAKF,GAAE,UAAQG,OAAKD,IAAE,CAAAC,IAAE,WAAW,IAAI,KAAG,OAAOD,IAAEC,GAAC,KAAG,eAAaL,KAAEC,GAAEI,GAAC,MAAI,SAAOJ,GAAEI,GAAC,IAAE,CAAC,IAAGJ,GAAEI,GAAC,EAAE,KAAKD,IAAEC,GAAC,CAAC,KAAGF,IAAEE,GAAC,IAAED,IAAEC,GAAC;AAAE,MAAGF,IAAE,YAAUA,IAAE,eAAe,EAAE,QAAO,OAAO,OAAOA,KAAE,OAAO,YAAY,OAAO,KAAKF,EAAC,EAAE,IAAI,CAAAG,QAAG,CAACA,KAAE,MAAM,CAAC,CAAC,CAAC;AAAE,WAAQA,OAAKH,GAAE,QAAO,OAAOE,KAAE,EAAC,CAACC,GAAC,EAAEC,QAAKC,KAAE;AAAC,QAAIC,MAAEN,GAAEG,GAAC;AAAE,aAAQI,OAAKD,KAAE;AAAC,WAAIF,eAAa,UAAQA,OAAG,OAAK,SAAOA,IAAE,wBAAuB,UAAQA,IAAE,iBAAiB;AAAO,MAAAG,IAAEH,KAAE,GAAGC,GAAC;AAAA,IAAC;AAAA,EAAC,EAAC,CAAC;AAAE,SAAOH;AAAC;AAAC,SAAS,EAAED,IAAE;AAAC,MAAIC;AAAE,SAAO,OAAO,WAAO,eAAAsB,YAAEvB,EAAC,GAAE,EAAC,cAAaC,MAAED,GAAE,gBAAc,OAAKC,MAAED,GAAE,KAAI,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,MAAIC,MAAE,OAAO,OAAO,CAAC,GAAED,EAAC;AAAE,WAAQD,MAAKE,IAAE,CAAAA,IAAEF,EAAC,MAAI,UAAQ,OAAOE,IAAEF,EAAC;AAAE,SAAOE;AAAC;AAAC,SAASU,GAAEX,IAAEC,MAAE,CAAC,GAAE;AAAC,MAAIF,KAAE,OAAO,OAAO,CAAC,GAAEC,EAAC;AAAE,WAAQF,MAAKG,IAAE,CAAAH,MAAKC,MAAG,OAAOA,GAAED,EAAC;AAAE,SAAOC;AAAC;;;AEH10C,IAAIyB,KAAE;AAAM,IAAIC,MAAG,CAAAC,QAAIA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,YAAU,CAAC,IAAE,aAAYA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,KAAID,MAAG,CAAC,CAAC;AAAE,SAASE,GAAEC,KAAEC,KAAE;AAAC,MAAIC;AAAE,MAAG,EAAC,UAASC,MAAE,GAAE,GAAGL,GAAC,IAAEE,KAAEI,KAAE,EAAC,KAAIH,KAAE,gBAAeE,MAAE,OAAK,IAAE,QAAID,KAAEJ,GAAE,aAAa,MAAI,OAAKI,KAAE,QAAO,SAAQC,MAAE,OAAK,IAAE,OAAG,QAAO,OAAM,EAAC,UAAS,SAAQ,KAAI,GAAE,MAAK,GAAE,OAAM,GAAE,QAAO,GAAE,SAAQ,GAAE,QAAO,IAAG,UAAS,UAAS,MAAK,oBAAmB,YAAW,UAAS,aAAY,KAAI,IAAIA,MAAE,OAAK,MAAIA,MAAE,OAAK,KAAG,EAAC,SAAQ,OAAM,EAAC,EAAC;AAAE,SAAO,EAAE,EAAC,UAASC,IAAE,YAAWN,IAAE,MAAK,CAAC,GAAE,YAAWF,IAAE,MAAK,SAAQ,CAAC;AAAC;AAAC,IAAIS,KAAE,EAAEN,EAAC;;;ACAjlB,IAAAO,iBAAiD;AAAQ,IAAIC,SAAE,eAAAC,eAAE,IAAI;AAAED,GAAE,cAAY;AAAoB,IAAIE,MAAG,CAAAC,QAAIA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,KAAID,MAAG,CAAC,CAAC;AAAE,SAASE,KAAG;AAAC,aAAO,eAAAC,YAAEL,EAAC;AAAC;AAAC,SAASM,GAAE,EAAC,OAAMC,KAAE,UAASC,GAAC,GAAE;AAAC,SAAO,eAAAC,QAAE,cAAcT,GAAE,UAAS,EAAC,OAAMO,IAAC,GAAEC,EAAC;AAAC;;;ACAnU,SAASE,IAAEC,IAAE;AAAC,WAASC,KAAG;AAAC,aAAS,eAAa,cAAYD,GAAE,GAAE,SAAS,oBAAoB,oBAAmBC,EAAC;AAAA,EAAE;AAAC,SAAO,UAAQ,eAAa,OAAO,YAAU,gBAAc,SAAS,iBAAiB,oBAAmBA,EAAC,GAAEA,GAAE;AAAE;;;ACA9K,IAAIC,MAAE,CAAC;AAAEA,IAAE,MAAI;AAAC,WAASC,GAAEC,IAAE;AAAC,IAAAA,GAAE,kBAAkB,eAAaA,GAAE,WAAS,SAAS,QAAMF,IAAE,CAAC,MAAIE,GAAE,WAASF,IAAE,QAAQE,GAAE,MAAM,GAAEF,MAAEA,IAAE,OAAO,CAAAG,OAAGA,MAAG,QAAMA,GAAE,WAAW,GAAEH,IAAE,OAAO,EAAE;AAAA,EAAE;AAAC,SAAO,iBAAiB,SAAQC,IAAE,EAAC,SAAQ,KAAE,CAAC,GAAE,OAAO,iBAAiB,aAAYA,IAAE,EAAC,SAAQ,KAAE,CAAC,GAAE,OAAO,iBAAiB,SAAQA,IAAE,EAAC,SAAQ,KAAE,CAAC,GAAE,SAAS,KAAK,iBAAiB,SAAQA,IAAE,EAAC,SAAQ,KAAE,CAAC,GAAE,SAAS,KAAK,iBAAiB,aAAYA,IAAE,EAAC,SAAQ,KAAE,CAAC,GAAE,SAAS,KAAK,iBAAiB,SAAQA,IAAE,EAAC,SAAQ,KAAE,CAAC;AAAC,CAAC;;;ACAtiB,SAASG,GAAEC,IAAE;AAAC,MAAIC,KAAED,GAAE,eAAcE,MAAE;AAAK,SAAKD,MAAG,EAAEA,cAAa,uBAAsB,CAAAA,cAAa,sBAAoBC,MAAED,KAAGA,KAAEA,GAAE;AAAc,MAAIE,OAAGF,MAAG,OAAK,SAAOA,GAAE,aAAa,UAAU,OAAK;AAAG,SAAOE,OAAGC,GAAEF,GAAC,IAAE,QAAGC;AAAC;AAAC,SAASC,GAAEJ,IAAE;AAAC,MAAG,CAACA,GAAE,QAAM;AAAG,MAAIC,KAAED,GAAE;AAAuB,SAAKC,OAAI,QAAM;AAAC,QAAGA,cAAa,kBAAkB,QAAM;AAAG,IAAAA,KAAEA,GAAE;AAAA,EAAsB;AAAC,SAAM;AAAE;;;ACArX,SAASI,GAAEC,KAAE;AAAC,QAAM,IAAI,MAAM,wBAAsBA,GAAC;AAAC;AAAC,IAAIC,MAAG,CAAAC,QAAIA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,KAAID,MAAG,CAAC,CAAC;AAAE,SAASE,GAAEH,KAAEI,IAAE;AAAC,MAAIC,MAAED,GAAE,aAAa;AAAE,MAAGC,IAAE,UAAQ,EAAE,QAAO;AAAK,MAAIC,KAAEF,GAAE,mBAAmB,GAAEG,MAAED,MAAG,OAAKA,KAAE;AAAG,UAAON,IAAE,OAAM;AAAA,IAAC,KAAK,GAAE;AAAC,eAAQQ,KAAE,GAAEA,KAAEH,IAAE,QAAO,EAAEG,GAAE,KAAG,CAACJ,GAAE,gBAAgBC,IAAEG,EAAC,GAAEA,IAAEH,GAAC,EAAE,QAAOG;AAAE,aAAOF;AAAA,IAAC;AAAA,IAAC,KAAK,GAAE;AAAC,eAAQE,KAAED,MAAE,GAAEC,MAAG,GAAE,EAAEA,GAAE,KAAG,CAACJ,GAAE,gBAAgBC,IAAEG,EAAC,GAAEA,IAAEH,GAAC,EAAE,QAAOG;AAAE,aAAOF;AAAA,IAAC;AAAA,IAAC,KAAK,GAAE;AAAC,eAAQE,KAAED,MAAE,GAAEC,KAAEH,IAAE,QAAO,EAAEG,GAAE,KAAG,CAACJ,GAAE,gBAAgBC,IAAEG,EAAC,GAAEA,IAAEH,GAAC,EAAE,QAAOG;AAAE,aAAOF;AAAA,IAAC;AAAA,IAAC,KAAK,GAAE;AAAC,eAAQE,KAAEH,IAAE,SAAO,GAAEG,MAAG,GAAE,EAAEA,GAAE,KAAG,CAACJ,GAAE,gBAAgBC,IAAEG,EAAC,GAAEA,IAAEH,GAAC,EAAE,QAAOG;AAAE,aAAOF;AAAA,IAAC;AAAA,IAAC,KAAK,GAAE;AAAC,eAAQE,KAAE,GAAEA,KAAEH,IAAE,QAAO,EAAEG,GAAE,KAAGJ,GAAE,UAAUC,IAAEG,EAAC,GAAEA,IAAEH,GAAC,MAAIL,IAAE,GAAG,QAAOQ;AAAE,aAAOF;AAAA,IAAC;AAAA,IAAC,KAAK;AAAE,aAAO;AAAA,IAAK;AAAQ,MAAAP,GAAEC,GAAC;AAAA,EAAC;AAAC;;;ACA/xB,SAAS,EAAES,KAAE,CAAC,GAAEC,MAAE,MAAKC,MAAE,CAAC,GAAE;AAAC,WAAO,CAACC,IAAEC,EAAC,KAAI,OAAO,QAAQJ,EAAC,EAAE,CAAAK,IAAEH,KAAEI,GAAEL,KAAEE,EAAC,GAAEC,EAAC;AAAE,SAAOF;AAAC;AAAC,SAASI,GAAEN,IAAEC,KAAE;AAAC,SAAOD,KAAEA,KAAE,MAAIC,MAAE,MAAIA;AAAC;AAAC,SAASI,IAAEL,IAAEC,KAAEC,KAAE;AAAC,MAAG,MAAM,QAAQA,GAAC,EAAE,UAAO,CAACC,IAAEC,EAAC,KAAIF,IAAE,QAAQ,EAAE,CAAAG,IAAEL,IAAEM,GAAEL,KAAEE,GAAE,SAAS,CAAC,GAAEC,EAAC;AAAA,MAAO,CAAAF,eAAa,OAAKF,GAAE,KAAK,CAACC,KAAEC,IAAE,YAAY,CAAC,CAAC,IAAE,OAAOA,OAAG,YAAUF,GAAE,KAAK,CAACC,KAAEC,MAAE,MAAI,GAAG,CAAC,IAAE,OAAOA,OAAG,WAASF,GAAE,KAAK,CAACC,KAAEC,GAAC,CAAC,IAAE,OAAOA,OAAG,WAASF,GAAE,KAAK,CAACC,KAAE,GAAGC,GAAC,EAAE,CAAC,IAAEA,OAAG,OAAKF,GAAE,KAAK,CAACC,KAAE,EAAE,CAAC,IAAE,EAAEC,KAAED,KAAED,EAAC;AAAC;AAAC,SAASO,GAAEP,IAAE;AAAC,MAAIE,KAAEC;AAAE,MAAIF,OAAGC,MAAEF,MAAG,OAAK,SAAOA,GAAE,SAAO,OAAKE,MAAEF,GAAE,QAAQ,MAAM;AAAE,MAAGC,KAAE;AAAC,aAAQG,MAAKH,IAAE,SAAS,KAAGG,OAAIJ,OAAII,GAAE,YAAU,WAASA,GAAE,SAAO,YAAUA,GAAE,YAAU,YAAUA,GAAE,SAAO,YAAUA,GAAE,aAAW,WAASA,GAAE,SAAO,UAAS;AAAC,MAAAA,GAAE,MAAM;AAAE;AAAA,IAAM;AAAC,KAACD,KAAEF,IAAE,kBAAgB,QAAME,GAAE,KAAKF,GAAC;AAAA,EAAC;AAAC;;;ACAnsB,IAAIO,OAAG,CAAAC,QAAIA,GAAE,QAAM,KAAIA,GAAE,QAAM,SAAQA,GAAE,SAAO,UAASA,GAAE,YAAU,aAAYA,GAAE,SAAO,UAASA,GAAE,YAAU,aAAYA,GAAE,UAAQ,WAAUA,GAAE,aAAW,cAAaA,GAAE,YAAU,aAAYA,GAAE,OAAK,QAAOA,GAAE,MAAI,OAAMA,GAAE,SAAO,UAASA,GAAE,WAAS,YAAWA,GAAE,MAAI,OAAMA,KAAID,OAAG,CAAC,CAAC;;;AlCA6mD,IAAI,MAAI,CAAAE,SAAIA,IAAEA,IAAE,OAAK,CAAC,IAAE,QAAOA,IAAEA,IAAE,SAAO,CAAC,IAAE,UAASA,MAAI,MAAI,CAAC,CAAC;AAAhE,IAAkE,MAAI,CAAAA,SAAIA,IAAEA,IAAE,SAAO,CAAC,IAAE,UAASA,IAAEA,IAAE,QAAM,CAAC,IAAE,SAAQA,MAAI,MAAI,CAAC,CAAC;AAAhI,IAAkI,MAAI,CAAAC,SAAIA,IAAEA,IAAE,UAAQ,CAAC,IAAE,WAAUA,IAAEA,IAAE,QAAM,CAAC,IAAE,SAAQA,IAAEA,IAAE,QAAM,CAAC,IAAE,SAAQA,MAAI,MAAI,CAAC,CAAC;AAAvN,IAAyN,MAAI,CAAAC,QAAIA,GAAEA,GAAE,eAAa,CAAC,IAAE,gBAAeA,GAAEA,GAAE,gBAAc,CAAC,IAAE,iBAAgBA,GAAEA,GAAE,aAAW,CAAC,IAAE,cAAaA,GAAEA,GAAE,iBAAe,CAAC,IAAE,kBAAiBA,GAAEA,GAAE,mBAAiB,CAAC,IAAE,oBAAmBA,GAAEA,GAAE,gBAAc,CAAC,IAAE,iBAAgBA,GAAEA,GAAE,uBAAqB,CAAC,IAAE,wBAAuBA,GAAEA,GAAE,uBAAqB,CAAC,IAAE,wBAAuBA,KAAI,MAAI,CAAC,CAAC;AAAE,SAAS,GAAGC,KAAEC,KAAE,CAAAJ,QAAGA,KAAE;AAAC,MAAIA,MAAEG,IAAE,sBAAoB,OAAKA,IAAE,QAAQA,IAAE,iBAAiB,IAAE,MAAKF,MAAEG,GAAED,IAAE,QAAQ,MAAM,CAAC,GAAEE,KAAEJ,IAAE,SAAO,KAAGA,IAAE,CAAC,EAAE,QAAQ,QAAQ,UAAQ,OAAKA,IAAE,KAAK,CAACK,IAAEC,QAAID,GAAE,QAAQ,QAAQ,QAAMC,IAAE,QAAQ,QAAQ,KAAK,IAAEC,GAAGP,KAAE,CAAAK,OAAGA,GAAE,QAAQ,QAAQ,OAAO,OAAO,GAAEG,MAAET,MAAEK,GAAE,QAAQL,GAAC,IAAE;AAAK,SAAOS,QAAI,OAAKA,MAAE,OAAM,EAAC,SAAQJ,IAAE,mBAAkBI,IAAC;AAAC;AAAC,IAAI,KAAG,EAAC,CAAC,CAAC,EAAEN,KAAE;AAAC,MAAIC;AAAE,UAAOA,KAAED,IAAE,QAAQ,YAAU,QAAMC,GAAE,YAAUD,IAAE,kBAAgB,IAAEA,MAAE,EAAC,GAAGA,KAAE,mBAAkB,MAAK,eAAc,EAAC;AAAC,GAAE,CAAC,CAAC,EAAEA,KAAE;AAAC,MAAIC,IAAEJ;AAAE,OAAII,KAAED,IAAE,QAAQ,YAAU,QAAMC,GAAE,YAAUD,IAAE,kBAAgB,EAAE,QAAOA;AAAE,OAAIH,MAAEG,IAAE,QAAQ,YAAU,QAAMH,IAAE,OAAM;AAAC,QAAIC,MAAEE,IAAE,QAAQ,QAAQ,eAAeA,IAAE,QAAQ,QAAQ,KAAK;AAAE,QAAGF,QAAI,GAAG,QAAM,EAAC,GAAGE,KAAE,mBAAkBF,KAAE,eAAc,EAAC;AAAA,EAAC;AAAC,SAAM,EAAC,GAAGE,KAAE,eAAc,EAAC;AAAC,GAAE,CAAC,CAAC,EAAEA,KAAEC,IAAE;AAAC,MAAIK,KAAEH,IAAEC,KAAEL,IAAEQ;AAAE,OAAID,MAAEN,IAAE,QAAQ,YAAU,QAAMM,IAAE,aAAWH,KAAEH,IAAE,QAAQ,YAAU,QAAMG,GAAE,WAAW,WAAS,GAAGC,MAAEJ,IAAE,QAAQ,YAAU,QAAMI,IAAE,gBAAgB,QAAQ,WAASJ,IAAE,kBAAgB,EAAE,QAAOA;AAAE,MAAGA,IAAE,SAAQ;AAAC,QAAIQ,KAAEP,GAAE,UAAQG,GAAE,WAASH,GAAE,MAAIQ,GAAGR,IAAE,EAAC,cAAa,MAAID,IAAE,QAAQ,SAAQ,oBAAmB,MAAI;AAAC,UAAIS,KAAEC;AAAE,cAAOA,MAAGD,MAAET,IAAE,sBAAoB,OAAKS,MAAET,IAAE,QAAQ,QAAQ,UAAU,CAAAW,QAAG,CAACX,IAAE,QAAQ,SAASW,GAAC,CAAC,MAAI,OAAKD,KAAE;AAAA,IAAI,GAAE,iBAAgBV,IAAE,QAAQ,UAAS,YAAW;AAAC,YAAM,IAAI,MAAM,2BAA2B;AAAA,IAAC,EAAC,CAAC,GAAEY,MAAGb,KAAEE,GAAE,YAAU,OAAKF,KAAE;AAAE,WAAOC,IAAE,sBAAoBQ,MAAGR,IAAE,sBAAoBY,KAAEZ,MAAE,EAAC,GAAGA,KAAE,mBAAkBQ,IAAE,mBAAkBI,GAAC;AAAA,EAAC;AAAC,MAAIf,MAAE,GAAGG,GAAC;AAAE,MAAGH,IAAE,sBAAoB,MAAK;AAAC,QAAIW,KAAEX,IAAE,QAAQ,UAAU,CAAAe,OAAG,CAACA,GAAE,QAAQ,QAAQ,QAAQ;AAAE,IAAAJ,OAAI,OAAKX,IAAE,oBAAkBW;AAAA,EAAE;AAAC,MAAIV,MAAEG,GAAE,UAAQG,GAAE,WAASH,GAAE,MAAIQ,GAAGR,IAAE,EAAC,cAAa,MAAIJ,IAAE,SAAQ,oBAAmB,MAAIA,IAAE,mBAAkB,WAAU,CAAAW,OAAGA,GAAE,IAAG,iBAAgB,CAAAA,OAAGA,GAAE,QAAQ,QAAQ,SAAQ,CAAC,GAAEN,MAAGK,MAAEN,GAAE,YAAU,OAAKM,MAAE;AAAE,SAAOP,IAAE,sBAAoBF,OAAGE,IAAE,sBAAoBE,KAAEF,MAAE,EAAC,GAAGA,KAAE,GAAGH,KAAE,mBAAkBC,KAAE,mBAAkBI,GAAC;AAAC,GAAE,CAAC,CAAC,GAAE,CAACF,KAAEC,OAAI;AAAC,MAAIK,KAAEH,IAAEC;AAAE,OAAIE,MAAEN,IAAE,QAAQ,YAAU,QAAMM,IAAE,QAAQ,QAAM,EAAC,GAAGN,KAAE,SAAQ,CAAC,GAAGA,IAAE,SAAQC,GAAE,OAAO,EAAC;AAAE,MAAIJ,MAAEI,GAAE,SAAQH,MAAE,GAAGE,KAAE,CAAAD,QAAIA,GAAE,KAAKF,GAAC,GAAEE,GAAE;AAAE,EAAAC,IAAE,sBAAoB,SAAOG,KAAEH,IAAE,QAAQ,YAAU,QAAMG,GAAE,WAAWF,GAAE,QAAQ,QAAQ,QAAQ,KAAK,MAAIH,IAAE,oBAAkBA,IAAE,QAAQ,QAAQD,GAAC;AAAG,MAAIK,KAAE,EAAC,GAAGF,KAAE,GAAGF,KAAE,mBAAkB,EAAC;AAAE,UAAOM,MAAEJ,IAAE,QAAQ,YAAU,QAAMI,IAAE,cAAYJ,IAAE,QAAQ,QAAQ,UAAQ,WAASE,GAAE,oBAAkB,IAAGA;AAAC,GAAE,CAAC,CAAC,GAAE,CAACF,KAAEC,OAAI;AAAC,MAAIH;AAAE,OAAIA,MAAEE,IAAE,QAAQ,YAAU,QAAMF,IAAE,QAAQ,QAAM,EAAC,GAAGE,KAAE,SAAQA,IAAE,QAAQ,OAAO,CAAAE,OAAGA,GAAE,OAAKD,GAAE,EAAE,EAAC;AAAE,MAAIJ,MAAE,GAAGG,KAAE,CAAAE,OAAG;AAAC,QAAII,MAAEJ,GAAE,UAAU,CAAAC,OAAGA,GAAE,OAAKF,GAAE,EAAE;AAAE,WAAOK,QAAI,MAAIJ,GAAE,OAAOI,KAAE,CAAC,GAAEJ;AAAA,EAAC,CAAC;AAAE,SAAM,EAAC,GAAGF,KAAE,GAAGH,KAAE,mBAAkB,EAAC;AAAC,GAAE,CAAC,CAAC,GAAE,CAACG,KAAEC,OAAID,IAAE,YAAUC,GAAE,KAAGD,MAAE,EAAC,GAAGA,KAAE,SAAQC,GAAE,GAAE,GAAE,CAAC,CAAC,GAAE,CAACD,KAAEC,OAAID,IAAE,sBAAoBC,GAAE,UAAQD,MAAE,EAAC,GAAGA,KAAE,mBAAkBC,GAAE,QAAO,GAAE,CAAC,CAAC,GAAE,CAACD,KAAEC,OAAI;AAAC,MAAIH;AAAE,QAAKA,MAAEE,IAAE,YAAU,OAAK,SAAOF,IAAE,aAAWG,GAAE,QAAQ,QAAOD;AAAE,MAAIH,MAAEG,IAAE;AAAkB,MAAGA,IAAE,sBAAoB,MAAK;AAAC,QAAIE,KAAED,GAAE,QAAQ,QAAQD,IAAE,QAAQ,QAAQA,IAAE,iBAAiB,CAAC;AAAE,IAAAE,OAAI,KAAGL,MAAEK,KAAEL,MAAE;AAAA,EAAI;AAAC,SAAM,EAAC,GAAGG,KAAE,mBAAkBH,KAAE,SAAQ,OAAO,OAAO,CAAC,GAAEG,IAAE,SAAQ,EAAC,SAAQC,GAAE,QAAO,CAAC,EAAC;AAAC,EAAC;AAA9lF,IAAgmF,SAAG,eAAAY,eAAG,IAAI;AAAE,GAAG,cAAY;AAAyB,SAAS,GAAGb,KAAE;AAAC,MAAIC,SAAE,eAAAa,YAAG,EAAE;AAAE,MAAGb,OAAI,MAAK;AAAC,QAAIJ,MAAE,IAAI,MAAM,IAAIG,GAAC,iDAAiD;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBH,KAAE,EAAE,GAAEA;AAAA,EAAC;AAAC,SAAOI;AAAC;AAAC,IAAI,SAAG,eAAAY,eAAG,IAAI;AAAE,SAAS,GAAGb,KAAE;AAAC,MAAII;AAAE,MAAIH,KAAEc,GAAE,iBAAiB,GAAE,CAAClB,KAAEC,GAAC,QAAE,eAAAkB,SAAE,MAAI;AAAC,QAAIjB,KAAEE,GAAE,WAAW;AAAQ,QAAG,CAACF,GAAE,QAAM,CAAC,GAAE,CAAC;AAAE,QAAIQ,MAAE,OAAO,iBAAiBR,EAAC;AAAE,WAAM,CAAC,WAAWQ,IAAE,qBAAmBA,IAAE,UAAU,GAAE,WAAWA,IAAE,mBAAiBA,IAAE,aAAa,CAAC;AAAA,EAAC,GAAE,CAACN,GAAE,WAAW,OAAO,CAAC,GAAEC,KAAE,eAAG,EAAC,oBAAmBL,KAAE,kBAAiBC,KAAE,OAAMG,GAAE,QAAQ,QAAQ,QAAO,eAAc;AAAC,WAAO;AAAA,EAAE,GAAE,mBAAkB;AAAC,QAAIF;AAAE,YAAOA,KAAEE,GAAE,WAAW,YAAU,OAAKF,KAAE;AAAA,EAAI,GAAE,UAAS,GAAE,CAAC,GAAE,CAACO,KAAEH,EAAC,QAAE,eAAAc,UAAG,CAAC;AAAE,SAAO,EAAE,MAAI;AAAC,IAAAd,GAAE,CAAAJ,OAAGA,KAAE,CAAC;AAAA,EAAC,GAAE,EAAEK,MAAEH,GAAE,YAAU,OAAK,SAAOG,IAAE,OAAO,CAAC,GAAE,eAAAc,QAAE,cAAc,GAAG,UAAS,EAAC,OAAMhB,GAAC,GAAE,eAAAgB,QAAE,cAAc,OAAM,EAAC,OAAM,EAAC,UAAS,YAAW,OAAM,QAAO,QAAO,GAAGhB,GAAE,aAAa,CAAC,KAAI,GAAE,KAAI,CAAAH,OAAG;AAAC,QAAGA,IAAE;AAAC,UAAG,OAAO,WAAS,eAAa,QAAQ,IAAI,mBAAiB,UAAQE,GAAE,sBAAoB,EAAE;AAAO,MAAAA,GAAE,sBAAoB,QAAMA,GAAE,QAAQ,QAAQ,SAAOA,GAAE,qBAAmBC,GAAE,cAAcD,GAAE,iBAAiB;AAAA,IAAC;AAAA,EAAC,EAAC,GAAEC,GAAE,gBAAgB,EAAE,IAAI,CAAAH,OAAG;AAAC,QAAIQ;AAAE,WAAO,eAAAW,QAAE,cAAc,eAAAC,UAAG,EAAC,KAAIpB,GAAE,IAAG,GAAE,eAAAmB,QAAE,cAAcX,MAAEP,IAAE,aAAW,OAAK,SAAOO,IAAE,KAAKP,KAAE,EAAC,QAAOC,GAAE,QAAQ,QAAQF,GAAE,KAAK,GAAE,MAAKE,GAAE,kBAAgB,EAAC,CAAC,GAAE,EAAC,KAAI,GAAGK,GAAC,IAAIP,GAAE,GAAG,IAAG,cAAaA,GAAE,OAAM,gBAAeE,GAAE,QAAQ,QAAQ,QAAO,iBAAgBF,GAAE,QAAM,GAAE,OAAM,EAAC,UAAS,YAAW,KAAI,GAAE,MAAK,GAAE,WAAU,cAAcA,GAAE,KAAK,OAAM,gBAAe,OAAM,EAAC,CAAC,CAAC;AAAA,EAAC,CAAC,CAAC,CAAC;AAAC;AAAC,IAAI,SAAG,eAAAc,eAAG,IAAI;AAAE,GAAG,cAAY;AAAsB,SAASE,GAAEf,KAAE;AAAC,MAAIC,SAAE,eAAAa,YAAG,EAAE;AAAE,MAAGb,OAAI,MAAK;AAAC,QAAIJ,MAAE,IAAI,MAAM,IAAIG,GAAC,iDAAiD;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBH,KAAEkB,EAAC,GAAElB;AAAA,EAAC;AAAC,SAAOI;AAAC;AAAC,SAAS,GAAGD,KAAEC,IAAE;AAAC,SAAO,EAAEA,GAAE,MAAK,IAAGD,KAAEC,EAAC;AAAC;AAAC,IAAI,KAAG,eAAAkB;AAAG,SAAS,GAAGnB,KAAEC,IAAE;AAAC,MAAImB;AAAG,MAAG,EAAC,OAAMvB,KAAE,cAAaC,KAAE,UAASI,IAAE,MAAKI,KAAE,MAAKH,IAAE,IAAGC,MAAE,MAAK,UAASL,KAAE,OAAG,YAAWQ,MAAE,OAAG,UAASC,KAAE,OAAG,UAASI,KAAE,OAAG,WAAUH,MAAE,OAAG,SAAQC,KAAE,MAAK,GAAGC,IAAC,IAAEX,KAAEqB,KAAE,OAAGC,MAAE,MAAK,CAACjB,MAAEO,KAAE,CAAC,IAAE,QAAOW,EAAC,IAAE,EAAG1B,KAAEK,IAAEJ,GAAC,GAAE,CAAC0B,IAAEC,EAAC,QAAE,eAAAC,YAAG,IAAG,EAAC,aAAQ,eAAAC,WAAG,GAAE,eAAcpB,MAAE,IAAE,GAAE,SAAQ,CAAC,GAAE,SAAQe,MAAE,EAAC,SAAQA,IAAE,SAAQ,WAAUF,MAAGE,IAAE,aAAW,OAAKF,MAAG,MAAI,MAAE,IAAE,MAAK,mBAAkB,MAAK,mBAAkB,GAAE,SAAQ,KAAI,CAAC,GAAEQ,SAAE,eAAAC,QAAE,KAAE,GAAEC,SAAE,eAAAD,QAAE,EAAC,QAAO,OAAG,MAAK,MAAE,CAAC,GAAEE,SAAE,eAAAF,QAAE,IAAI,GAAEG,SAAE,eAAAH,QAAE,IAAI,GAAEI,UAAG,eAAAJ,QAAE,IAAI,GAAEK,SAAE,eAAAL,QAAE,IAAI,GAAEM,KAAEtC,GAAE,OAAOO,OAAG,WAAS,CAACgC,KAAEC,OAAI;AAAC,QAAIC,KAAElC;AAAE,YAAOgC,OAAG,OAAK,SAAOA,IAAEE,EAAC,QAAMD,MAAG,OAAK,SAAOA,GAAEC,EAAC;AAAA,EAAE,IAAElC,OAAG,OAAKA,MAAE,CAACgC,KAAEC,OAAID,QAAIC,EAAC,GAAEE,KAAE1C,GAAE,CAAAuC,QAAGd,MAAElB,QAAI,OAAKkB,IAAE,QAAQ,QAAQc,GAAC,IAAEd,IAAE,QAAQ,UAAU,CAAAe,OAAGF,GAAEE,IAAED,GAAC,CAAC,IAAEZ,GAAE,QAAQ,UAAU,CAAAa,OAAGF,GAAEE,GAAE,QAAQ,QAAQ,OAAMD,GAAC,CAAC,CAAC,GAAEI,SAAE,eAAAC,aAAG,CAAAL,QAAG,EAAEM,GAAE,MAAK,EAAC,CAAC,CAAC,GAAE,MAAIrC,IAAE,KAAK,CAAAgC,OAAGF,GAAEE,IAAED,GAAC,CAAC,GAAE,CAAC,CAAC,GAAE,MAAID,GAAE9B,KAAE+B,GAAC,EAAC,CAAC,GAAE,CAAC/B,GAAC,CAAC,GAAEsC,MAAG9C,GAAE,CAAAuC,QAAGZ,GAAE,sBAAoBe,GAAEH,GAAC,CAAC,GAAEM,SAAE,eAAA1B,SAAE,OAAK,EAAC,GAAGQ,IAAE,WAAUH,IAAE,iBAAgBS,IAAE,UAASC,IAAE,UAASC,IAAE,WAAUC,KAAG,YAAWC,IAAE,OAAM7B,KAAE,cAAaP,KAAE,UAASC,IAAE,MAAKa,KAAE,IAAE,GAAE,SAAQY,GAAE,SAAQ,IAAI,oBAAmB;AAAC,QAAGI,GAAE,WAASJ,GAAE,sBAAoB,SAAOF,MAAEA,IAAE,QAAQ,SAAO,IAAEE,GAAE,QAAQ,SAAO,IAAG;AAAC,UAAGF,KAAE;AAAC,YAAIe,KAAEf,IAAE,QAAQ,UAAU,CAAAgB,OAAG;AAAC,cAAIM,IAAEC;AAAE,iBAAM,GAAGA,MAAGD,KAAEtB,OAAG,OAAK,SAAOA,IAAE,aAAW,OAAK,SAAOsB,GAAE,KAAKtB,KAAEgB,EAAC,MAAI,QAAMO;AAAA,QAAE,CAAC;AAAE,YAAGR,OAAI,GAAG,QAAOA;AAAA,MAAC;AAAC,UAAID,MAAEZ,GAAE,QAAQ,UAAU,CAAAa,OAAG,CAACA,GAAE,QAAQ,QAAQ,QAAQ;AAAE,UAAGD,QAAI,GAAG,QAAOA;AAAA,IAAC;AAAC,WAAOZ,GAAE;AAAA,EAAiB,GAAE,gBAAee,IAAE,SAAQJ,IAAE,YAAWK,IAAE,UAASG,KAAG,UAASnC,IAAE,YAAWD,IAAC,IAAG,CAACF,KAAEP,KAAEC,IAAEa,IAAEJ,IAAED,KAAEiB,IAAEF,GAAC,CAAC;AAAE,IAAE,MAAI;AAAC,IAAAA,OAAGG,GAAE,EAAC,MAAK,GAAE,SAAQH,IAAE,QAAO,CAAC;AAAA,EAAC,GAAE,CAACA,KAAEA,OAAG,OAAK,SAAOA,IAAE,OAAO,CAAC,GAAE,EAAE,MAAI;AAAC,IAAAE,GAAE,QAAQ,UAAQkB;AAAA,EAAC,GAAE,CAACA,EAAC,CAAC,GAAEI,GAAG,CAACJ,GAAE,WAAUA,GAAE,UAASA,GAAE,UAAU,GAAE,MAAIK,IAAG,cAAc,GAAEL,GAAE,kBAAgB,CAAC;AAAE,MAAIM,UAAE,eAAAhC,SAAE,MAAI;AAAC,QAAIoB,KAAEC,IAAEC;AAAE,WAAM,EAAC,MAAKI,GAAE,kBAAgB,GAAE,UAAS3C,IAAE,aAAY2C,GAAE,mBAAkB,cAAaA,GAAE,sBAAoB,OAAK,OAAKA,GAAE,UAAQA,GAAE,QAAQ,SAASN,MAAEM,GAAE,sBAAoB,OAAKN,MAAE,CAAC,KAAGE,MAAGD,KAAEK,GAAE,QAAQA,GAAE,iBAAiB,MAAI,OAAK,SAAOL,GAAE,QAAQ,QAAQ,UAAQ,OAAKC,KAAE,MAAK,OAAMjC,IAAC;AAAA,EAAC,GAAE,CAACqC,IAAE3C,IAAEM,GAAC,CAAC,GAAE4C,KAAEpD,GAAE,MAAI;AAAC,QAAG6C,GAAE,sBAAoB,MAAK;AAAC,UAAGA,GAAE,QAAQ,CAAAQ,IAAGR,GAAE,QAAQ,QAAQA,GAAE,iBAAiB,CAAC;AAAA,WAAM;AAAC,YAAG,EAAC,SAAQN,IAAC,IAAEM,GAAE,QAAQA,GAAE,iBAAiB;AAAE,QAAAQ,IAAGd,IAAE,QAAQ,KAAK;AAAA,MAAC;AAAC,MAAAW,IAAG,WAAW3C,GAAE,UAASsC,GAAE,iBAAiB;AAAA,IAAC;AAAA,EAAC,CAAC,GAAES,KAAEtD,GAAE,MAAI;AAAC,IAAA4B,GAAE,EAAC,MAAK,EAAC,CAAC,GAAEG,GAAE,UAAQ;AAAA,EAAE,CAAC,GAAEwB,KAAEvD,GAAE,MAAI;AAAC,IAAA4B,GAAE,EAAC,MAAK,EAAC,CAAC,GAAEG,GAAE,UAAQ;AAAA,EAAE,CAAC,GAAEyB,KAAExD,GAAE,CAACuC,KAAEC,IAAEC,QAAKV,GAAE,UAAQ,OAAGQ,QAAIhC,GAAE,WAASqB,GAAE,EAAC,MAAK,GAAE,OAAMrB,GAAE,UAAS,KAAIiC,IAAE,SAAQC,GAAC,CAAC,IAAEb,GAAE,EAAC,MAAK,GAAE,OAAMW,KAAE,SAAQE,GAAC,CAAC,EAAE,GAAEgB,KAAEzD,GAAE,CAACuC,KAAEC,QAAKZ,GAAE,EAAC,MAAK,GAAE,SAAQ,EAAC,IAAGW,KAAE,SAAQC,GAAC,EAAC,CAAC,GAAE,MAAI;AAAC,IAAAK,GAAE,SAASL,GAAE,QAAQ,KAAK,MAAIT,GAAE,UAAQ,OAAIH,GAAE,EAAC,MAAK,GAAE,IAAGW,IAAC,CAAC;AAAA,EAAC,EAAE,GAAEmB,MAAG1D,GAAE,CAAAuC,SAAIX,GAAE,EAAC,MAAK,GAAE,IAAGW,IAAC,CAAC,GAAE,MAAIX,GAAE,EAAC,MAAK,GAAE,IAAG,KAAI,CAAC,EAAE,GAAEyB,MAAGrD,GAAE,CAAAuC,QAAG,EAAEM,GAAE,MAAK,EAAC,CAAC,CAAC,IAAG;AAAC,WAAOnB,MAAG,OAAK,SAAOA,GAAEa,GAAC;AAAA,EAAC,GAAE,CAAC,CAAC,IAAG;AAAC,QAAIC,KAAEK,GAAE,MAAM,MAAM,GAAEJ,KAAED,GAAE,UAAU,CAAAO,OAAGT,GAAES,IAAER,GAAC,CAAC;AAAE,WAAOE,OAAI,KAAGD,GAAE,KAAKD,GAAC,IAAEC,GAAE,OAAOC,IAAE,CAAC,GAAEf,MAAG,OAAK,SAAOA,GAAEc,EAAC;AAAA,EAAC,EAAC,CAAC,CAAC,GAAEmB,MAAG3D,GAAE,CAAAuC,QAAG;AAAC,IAAAX,GAAE,EAAC,MAAK,GAAE,SAAQW,IAAC,CAAC;AAAA,EAAC,CAAC,GAAEW,UAAG,eAAA/B,SAAE,OAAK,EAAC,UAASkC,KAAG,gBAAeI,IAAE,eAAcC,KAAG,YAAWF,IAAE,eAAcD,IAAE,cAAaD,IAAE,sBAAqBK,KAAG,oBAAmBP,GAAC,IAAG,CAAC,CAAC,GAAEQ,MAAGxD,OAAI,OAAK,CAAC,IAAE,EAAC,KAAIA,GAAC,GAAEyD,UAAG,eAAA7B,QAAE,IAAI,GAAE8B,MAAG,EAAG;AAAE,aAAO,eAAAC,WAAG,MAAI;AAAC,IAAAF,IAAG,WAAS5D,QAAI,UAAQ6D,IAAG,iBAAiBD,IAAG,SAAQ,SAAQ,MAAI;AAAC,MAAAnC,MAAG,QAAMA,GAAEzB,GAAC;AAAA,IAAC,CAAC;AAAA,EAAC,GAAE,CAAC4D,KAAGnC,EAAC,CAAC,GAAE,eAAAL,QAAE,cAAc,GAAG,UAAS,EAAC,OAAM6B,IAAE,GAAE,eAAA7B,QAAE,cAAc,GAAG,UAAS,EAAC,OAAMwB,GAAC,GAAE,eAAAxB,QAAE,cAAcI,IAAG,EAAC,OAAM,EAAEoB,GAAE,eAAc,EAAC,CAAC,CAAC,GAAEN,GAAG,MAAK,CAAC,CAAC,GAAEA,GAAG,OAAM,CAAC,EAAC,GAAEjC,MAAG,QAAME,OAAG,QAAM,EAAG,EAAC,CAACF,EAAC,GAAEE,IAAC,CAAC,EAAE,IAAI,CAAC,CAAC+B,KAAEC,EAAC,GAAEC,OAAI,eAAApB,QAAE,cAAcZ,IAAG,EAAC,UAASgB,GAAG,QAAO,KAAIgB,OAAI,IAAE,CAAAM,OAAG;AAAC,QAAIC;AAAE,IAAAa,IAAG,WAASb,KAAED,MAAG,OAAK,SAAOA,GAAE,QAAQ,MAAM,MAAI,OAAKC,KAAE;AAAA,EAAI,IAAE,QAAO,GAAG,EAAG,EAAC,KAAIT,KAAE,IAAG,SAAQ,MAAK,UAAS,QAAO,MAAG,UAAS,MAAG,MAAK9B,KAAE,UAASP,IAAE,MAAKqC,KAAE,OAAMC,GAAC,CAAC,EAAC,CAAC,CAAC,GAAE,EAAE,EAAC,UAASoB,KAAG,YAAW9C,KAAE,MAAKqC,KAAE,YAAW,IAAG,MAAK,WAAU,CAAC,CAAC,CAAC,CAAC;AAAC;AAAC,IAAI,KAAG;AAAQ,SAAS,GAAGhD,KAAEC,IAAE;AAAC,MAAIiC,IAAEC,IAAEI,IAAEC,IAAEG;AAAG,MAAI9C,MAAE,EAAE,GAAE,EAAC,IAAGC,MAAE,6BAA6BD,GAAC,IAAG,UAASK,IAAE,cAAaI,KAAE,MAAKH,KAAE,QAAO,GAAGC,IAAC,IAAEJ,KAAED,KAAEgB,GAAE,gBAAgB,GAAER,MAAE,GAAG,gBAAgB,GAAEC,KAAEsC,GAAE/C,GAAE,UAASE,EAAC,GAAEW,KAAE8B,GAAG3C,GAAE,QAAQ,GAAEU,UAAE,eAAAoB,QAAE,KAAE,GAAEnB,KAAE,EAAG,GAAEC,MAAEd,GAAE,MAAI;AAAC,IAAAU,IAAE,SAAS,IAAI,GAAER,GAAE,WAAW,YAAUA,GAAE,WAAW,QAAQ,YAAU,IAAGQ,IAAE,WAAWH,GAAE,OAAO;AAAA,EAAC,CAAC,GAAEiB,KAAE,WAAU;AAAC,QAAIqB;AAAE,WAAO,OAAOpC,OAAG,cAAYP,GAAE,UAAQ,UAAQ2C,KAAEpC,IAAEP,GAAE,KAAK,MAAI,OAAK2C,KAAE,KAAG,OAAO3C,GAAE,SAAO,WAASA,GAAE,QAAM;AAAA,EAAE,EAAE;AAAE,EAAA8D,GAAG,CAAC,CAACnB,IAAEM,GAAC,GAAE,CAACC,IAAEE,EAAC,MAAI;AAAC,QAAG1C,IAAE,QAAQ;AAAO,QAAI2C,KAAErD,GAAE,SAAS;AAAQ,IAAAqD,QAAKD,OAAI,KAAGH,QAAI,KAAGN,OAAIO,QAAKG,GAAE,QAAMV,KAAG,sBAAsB,MAAI;AAAC,UAAGjC,IAAE,WAAS,CAAC2C,OAAIxC,MAAG,OAAK,SAAOA,GAAE,mBAAiBwC,GAAE;AAAO,UAAG,EAAC,gBAAeC,IAAE,cAAaC,GAAC,IAAEF;AAAE,WAAK,KAAKE,MAAG,OAAKA,KAAE,MAAID,MAAG,OAAKA,KAAE,EAAE,MAAI,KAAGA,OAAI,KAAGD,GAAE,kBAAkBA,GAAE,MAAM,QAAOA,GAAE,MAAM,MAAM;AAAA,IAAC,CAAC;AAAA,EAAE,GAAE,CAAC/B,IAAEtB,GAAE,eAAca,EAAC,CAAC,GAAEiD,GAAG,CAAC,CAACnB,EAAC,GAAE,CAACM,GAAC,MAAI;AAAC,QAAGN,OAAI,KAAGM,QAAI,GAAE;AAAC,UAAGvC,IAAE,QAAQ;AAAO,UAAIwC,KAAElD,GAAE,SAAS;AAAQ,UAAG,CAACkD,GAAE;AAAO,UAAIE,KAAEF,GAAE,OAAM,EAAC,gBAAeG,IAAE,cAAaC,IAAE,oBAAmBC,GAAC,IAAEL;AAAE,MAAAA,GAAE,QAAM,IAAGA,GAAE,QAAME,IAAEG,OAAI,OAAKL,GAAE,kBAAkBG,IAAEC,IAAEC,EAAC,IAAEL,GAAE,kBAAkBG,IAAEC,EAAC;AAAA,IAAC;AAAA,EAAC,GAAE,CAACtD,GAAE,aAAa,CAAC;AAAE,MAAIuB,UAAE,eAAAO,QAAE,KAAE,GAAExB,MAAER,GAAE,MAAI;AAAC,IAAAyB,IAAE,UAAQ;AAAA,EAAE,CAAC,GAAEC,KAAE1B,GAAE,MAAI;AAAC,IAAAa,GAAE,UAAU,MAAI;AAAC,MAAAY,IAAE,UAAQ;AAAA,IAAE,CAAC;AAAA,EAAC,CAAC,GAAEE,KAAE3B,GAAE,CAAA6C,OAAG;AAAC,YAAOjC,IAAE,UAAQ,MAAGiC,GAAE,KAAI;AAAA,MAAC,KAAK7C,IAAE;AAAM,YAAGY,IAAE,UAAQ,OAAGV,GAAE,kBAAgB,KAAGuB,IAAE,QAAQ;AAAO,YAAGoB,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAE3C,GAAE,sBAAoB,MAAK;AAAC,UAAAQ,IAAE,cAAc;AAAE;AAAA,QAAM;AAAC,QAAAA,IAAE,mBAAmB,GAAER,GAAE,SAAO,KAAGQ,IAAE,cAAc;AAAE;AAAA,MAAM,KAAKV,IAAE;AAAU,eAAOY,IAAE,UAAQ,OAAGiC,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAE,EAAE3C,GAAE,eAAc,EAAC,CAAC,CAAC,GAAE,MAAIQ,IAAE,WAAWH,GAAE,IAAI,GAAE,CAAC,CAAC,GAAE,MAAIG,IAAE,aAAa,EAAC,CAAC;AAAA,MAAE,KAAKV,IAAE;AAAQ,eAAOY,IAAE,UAAQ,OAAGiC,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAE,EAAE3C,GAAE,eAAc,EAAC,CAAC,CAAC,GAAE,MAAIQ,IAAE,WAAWH,GAAE,QAAQ,GAAE,CAAC,CAAC,GAAE,MAAI;AAAC,UAAAG,IAAE,aAAa,GAAEG,GAAE,UAAU,MAAI;AAAC,YAAAX,GAAE,SAAOQ,IAAE,WAAWH,GAAE,IAAI;AAAA,UAAC,CAAC;AAAA,QAAC,EAAC,CAAC;AAAA,MAAE,KAAKP,IAAE;AAAK,YAAG6C,GAAE,SAAS;AAAM,eAAOjC,IAAE,UAAQ,OAAGiC,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEnC,IAAE,WAAWH,GAAE,KAAK;AAAA,MAAE,KAAKP,IAAE;AAAO,eAAOY,IAAE,UAAQ,OAAGiC,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEnC,IAAE,WAAWH,GAAE,KAAK;AAAA,MAAE,KAAKP,IAAE;AAAI,YAAG6C,GAAE,SAAS;AAAM,eAAOjC,IAAE,UAAQ,OAAGiC,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEnC,IAAE,WAAWH,GAAE,IAAI;AAAA,MAAE,KAAKP,IAAE;AAAS,eAAOY,IAAE,UAAQ,OAAGiC,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEnC,IAAE,WAAWH,GAAE,IAAI;AAAA,MAAE,KAAKP,IAAE;AAAO,eAAOY,IAAE,UAAQ,OAAGV,GAAE,kBAAgB,IAAE,UAAQ2C,GAAE,eAAe,GAAE3C,GAAE,WAAW,WAAS,CAACA,GAAE,gBAAgB,QAAQ,UAAQ2C,GAAE,gBAAgB,GAAE3C,GAAE,YAAUA,GAAE,SAAO,KAAGA,GAAE,UAAQ,QAAMY,IAAE,GAAEJ,IAAE,cAAc;AAAA,MAAG,KAAKV,IAAE;AAAI,YAAGY,IAAE,UAAQ,OAAGV,GAAE,kBAAgB,EAAE;AAAO,QAAAA,GAAE,SAAO,KAAGA,GAAE,sBAAoB,KAAGQ,IAAE,mBAAmB,GAAEA,IAAE,cAAc;AAAE;AAAA,IAAK;AAAA,EAAC,CAAC,GAAEkB,KAAE5B,GAAE,CAAA6C,OAAG;AAAC,IAAAxC,MAAG,QAAMA,GAAEwC,EAAC,GAAE3C,GAAE,YAAUA,GAAE,SAAO,KAAG2C,GAAE,OAAO,UAAQ,MAAI/B,IAAE,GAAEJ,IAAE,aAAa;AAAA,EAAC,CAAC,GAAEqB,KAAE/B,GAAE,CAAA6C,OAAG;AAAC,QAAIO,IAAEE,IAAEC;AAAE,QAAIJ,OAAGC,KAAEP,GAAE,kBAAgB,OAAKO,KAAEjD,IAAG,KAAK,CAAAqD,OAAGA,OAAIX,GAAE,aAAa;AAAE,QAAGjC,IAAE,UAAQ,OAAG,GAAG0C,KAAEpD,GAAE,WAAW,YAAU,QAAMoD,GAAE,SAASH,GAAC,MAAI,GAAGI,KAAErD,GAAE,UAAU,YAAU,QAAMqD,GAAE,SAASJ,GAAC,MAAIjD,GAAE,kBAAgB,EAAE,QAAO2C,GAAE,eAAe,GAAE3C,GAAE,SAAO,MAAIA,GAAE,YAAUA,GAAE,UAAQ,OAAKY,IAAE,IAAEZ,GAAE,sBAAoB,KAAGQ,IAAE,mBAAmB,IAAGA,IAAE,cAAc;AAAA,EAAC,CAAC,GAAEuB,KAAEjC,GAAE,CAAA6C,OAAG;AAAC,QAAIO,IAAEE,IAAEC;AAAE,QAAIJ,OAAGC,KAAEP,GAAE,kBAAgB,OAAKO,KAAEjD,IAAG,KAAK,CAAAqD,OAAGA,OAAIX,GAAE,aAAa;AAAE,KAACS,KAAEpD,GAAE,UAAU,YAAU,QAAMoD,GAAE,SAASH,GAAC,MAAII,KAAErD,GAAE,WAAW,YAAU,QAAMqD,GAAE,SAASJ,GAAC,KAAGjD,GAAE,YAAUA,GAAE,aAAWA,GAAE,kBAAgB,MAAIQ,IAAE,aAAa,GAAEG,GAAE,UAAU,MAAI;AAAC,MAAAH,IAAE,qBAAqB,CAAC;AAAA,IAAC,CAAC;AAAA,EAAE,CAAC,GAAEwB,KAAE7B,GAAG,MAAI;AAAC,QAAGH,GAAE,QAAQ,QAAM,CAACA,GAAE,OAAO,EAAE,KAAK,GAAG;AAAA,EAAC,GAAE,CAACA,GAAE,OAAO,CAAC,GAAEiC,SAAE,eAAAhB,SAAE,OAAK,EAAC,MAAKjB,GAAE,kBAAgB,GAAE,UAASA,GAAE,SAAQ,IAAG,CAACA,EAAC,CAAC,GAAEkC,MAAG,EAAC,KAAIzB,IAAE,IAAGV,KAAE,MAAK,YAAW,MAAKK,IAAE,kBAAiB+B,KAAEnC,GAAE,WAAW,YAAU,OAAK,SAAOmC,GAAE,IAAG,iBAAgBnC,GAAE,kBAAgB,GAAE,yBAAwBA,GAAE,sBAAoB,OAAK,SAAOA,GAAE,WAASoC,KAAEpC,GAAE,QAAQ,KAAK,CAAA2C,OAAG;AAAC,QAAIM;AAAE,WAAM,GAAGA,MAAEjD,GAAE,YAAU,QAAMiD,IAAE,SAASN,GAAE,QAAQ,QAAQ,KAAK,MAAI3C,GAAE,QAAQ2C,GAAE,QAAQ,QAAQ,OAAM3C,GAAE,QAAQ,QAAQA,GAAE,iBAAiB,CAAC;AAAA,EAAC,CAAC,MAAI,OAAK,SAAOoC,GAAE,MAAII,KAAExC,GAAE,QAAQA,GAAE,iBAAiB,MAAI,OAAK,SAAOwC,GAAE,IAAG,mBAAkBR,IAAE,qBAAoB,QAAO,eAAcY,OAAIH,KAAExC,IAAE,iBAAe,OAAKwC,KAAEzC,GAAE,iBAAe,SAAOO,OAAG,OAAK,SAAOA,IAAEP,GAAE,YAAY,IAAE,SAAO,OAAK4C,MAAG5C,GAAE,cAAa,UAASA,GAAE,UAAS,oBAAmBM,KAAE,kBAAiBkB,IAAE,WAAUC,IAAE,UAASC,IAAE,SAAQK,IAAE,QAAOF,GAAC;AAAE,SAAO,EAAE,EAAC,UAASK,KAAG,YAAW7B,KAAE,MAAK4B,IAAE,YAAW,IAAG,MAAK,iBAAgB,CAAC;AAAC;AAAC,IAAI,KAAG;AAAS,SAAS,GAAGhC,KAAEC,IAAE;AAAC,MAAIU;AAAE,MAAId,MAAEkB,GAAE,iBAAiB,GAAEjB,MAAE,GAAG,iBAAiB,GAAEI,KAAE4C,GAAEjD,IAAE,WAAUI,EAAC,GAAEK,MAAE,EAAE,GAAE,EAAC,IAAGH,KAAE,8BAA8BG,GAAC,IAAG,GAAGF,IAAC,IAAEJ,KAAED,KAAE,EAAG,GAAEQ,MAAEV,GAAE,CAAAwB,OAAG;AAAC,YAAOA,GAAE,KAAI;AAAA,MAAC,KAAKxB,IAAE;AAAU,eAAOwB,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAExB,IAAE,kBAAgB,KAAGC,IAAE,aAAa,GAAEC,GAAE,UAAU,MAAI;AAAC,cAAIuB;AAAE,kBAAOA,MAAEzB,IAAE,SAAS,YAAU,OAAK,SAAOyB,IAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,QAAC,CAAC;AAAA,MAAE,KAAKzB,IAAE;AAAQ,eAAOwB,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAExB,IAAE,kBAAgB,MAAIC,IAAE,aAAa,GAAEC,GAAE,UAAU,MAAI;AAAC,UAAAF,IAAE,SAAOC,IAAE,WAAWM,GAAE,IAAI;AAAA,QAAC,CAAC,IAAGL,GAAE,UAAU,MAAI;AAAC,cAAIuB;AAAE,kBAAOA,MAAEzB,IAAE,SAAS,YAAU,OAAK,SAAOyB,IAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,QAAC,CAAC;AAAA,MAAE,KAAKzB,IAAE;AAAO,eAAOA,IAAE,kBAAgB,IAAE,UAAQwB,GAAE,eAAe,GAAExB,IAAE,WAAW,WAAS,CAACA,IAAE,gBAAgB,QAAQ,UAAQwB,GAAE,gBAAgB,GAAEvB,IAAE,cAAc,GAAEC,GAAE,UAAU,MAAI;AAAC,cAAIuB;AAAE,kBAAOA,MAAEzB,IAAE,SAAS,YAAU,OAAK,SAAOyB,IAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,QAAC,CAAC;AAAA,MAAG;AAAQ;AAAA,IAAM;AAAA,EAAC,CAAC,GAAEd,KAAEX,GAAE,CAAAwB,OAAG;AAAC,QAAGpB,GAAGoB,GAAE,aAAa,EAAE,QAAOA,GAAE,eAAe;AAAE,IAAAxB,IAAE,kBAAgB,IAAEC,IAAE,cAAc,KAAGuB,GAAE,eAAe,GAAEvB,IAAE,aAAa,IAAGC,GAAE,UAAU,MAAI;AAAC,UAAIuB;AAAE,cAAOA,MAAEzB,IAAE,SAAS,YAAU,OAAK,SAAOyB,IAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,IAAC,CAAC;AAAA,EAAC,CAAC,GAAEV,KAAEV,GAAG,MAAI;AAAC,QAAGL,IAAE,QAAQ,QAAM,CAACA,IAAE,SAAQM,EAAC,EAAE,KAAK,GAAG;AAAA,EAAC,GAAE,CAACN,IAAE,SAAQM,EAAC,CAAC,GAAEM,UAAE,eAAAO,SAAE,OAAK,EAAC,MAAKnB,IAAE,kBAAgB,GAAE,UAASA,IAAE,UAAS,OAAMA,IAAE,MAAK,IAAG,CAACA,GAAC,CAAC,GAAEa,KAAE,EAAC,KAAIR,IAAE,IAAGC,IAAE,MAAKK,GAAGR,KAAEH,IAAE,SAAS,GAAE,UAAS,IAAG,iBAAgB,WAAU,kBAAiBc,MAAEd,IAAE,WAAW,YAAU,OAAK,SAAOc,IAAE,IAAG,iBAAgBd,IAAE,kBAAgB,GAAE,mBAAkBe,IAAE,UAASf,IAAE,UAAS,SAAQW,IAAE,WAAUD,IAAC;AAAE,SAAO,EAAE,EAAC,UAASG,IAAE,YAAWN,KAAE,MAAKK,KAAE,YAAW,IAAG,MAAK,kBAAiB,CAAC;AAAC;AAAC,IAAI,KAAG;AAAQ,SAAS,GAAGT,KAAEC,IAAE;AAAC,MAAIJ,MAAE,EAAE,GAAE,EAAC,IAAGC,MAAE,6BAA6BD,GAAC,IAAG,GAAGK,GAAC,IAAEF,KAAEM,MAAES,GAAE,gBAAgB,GAAEZ,KAAE,GAAG,gBAAgB,GAAEC,MAAE0C,GAAExC,IAAE,UAASL,EAAC;AAAE,IAAE,MAAIE,GAAE,cAAcL,GAAC,GAAE,CAACA,GAAC,CAAC;AAAE,MAAIC,KAAEF,GAAE,MAAI;AAAC,QAAIe;AAAE,YAAOA,KAAEN,IAAE,SAAS,YAAU,OAAK,SAAOM,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,EAAC,CAAC,GAAEL,UAAE,eAAAS,SAAE,OAAK,EAAC,MAAKV,IAAE,kBAAgB,GAAE,UAASA,IAAE,SAAQ,IAAG,CAACA,GAAC,CAAC;AAAE,SAAO,EAAE,EAAC,UAAS,EAAC,KAAIF,KAAE,IAAGN,KAAE,SAAQC,GAAC,GAAE,YAAWG,IAAE,MAAKK,KAAE,YAAW,IAAG,MAAK,iBAAgB,CAAC;AAAC;AAAC,IAAI,KAAG;AAAP,IAAY,KAAGgC,GAAG,iBAAeA,GAAG;AAAO,SAAS,GAAGvC,KAAEC,IAAE;AAAC,MAAIJ,MAAE,EAAE,GAAE,EAAC,IAAGC,MAAE,+BAA+BD,GAAC,IAAG,MAAKK,KAAE,OAAG,GAAGI,IAAC,IAAEN,KAAEG,KAAEY,GAAE,kBAAkB,GAAEX,MAAE0C,GAAE3C,GAAE,YAAWF,EAAC,GAAEF,KAAEO,GAAG,GAAEC,OAAG,MAAIR,OAAI,QAAMA,KAAEqC,GAAG,UAAQA,GAAG,OAAKjC,GAAE,kBAAgB,GAAG;AAAE,IAAE,MAAI;AAAC,QAAIO;AAAE,IAAAP,GAAE,gBAAgB,QAAQ,UAAQO,KAAEV,IAAE,WAAS,OAAKU,KAAE;AAAA,EAAE,GAAE,CAACP,GAAE,iBAAgBH,IAAE,MAAM,CAAC,GAAE,EAAE,MAAI;AAAC,IAAAG,GAAE,gBAAgB,QAAQ,OAAKD;AAAA,EAAC,GAAE,CAACC,GAAE,iBAAgBD,EAAC,CAAC,GAAE8C,GAAG,EAAC,WAAU7C,GAAE,WAAW,SAAQ,SAAQA,GAAE,kBAAgB,GAAE,OAAOO,IAAE;AAAC,WAAOA,GAAE,aAAa,MAAM,MAAI,WAAS,WAAW,gBAAcA,GAAE,aAAa,MAAM,IAAE,WAAW,cAAY,WAAW;AAAA,EAAa,GAAE,KAAKA,IAAE;AAAC,IAAAA,GAAE,aAAa,QAAO,MAAM;AAAA,EAAC,EAAC,CAAC;AAAE,MAAIF,KAAEN,GAAG,MAAI;AAAC,QAAIQ,IAAEC;AAAE,YAAOA,MAAER,GAAE,YAAU,OAAKQ,OAAGD,KAAEP,GAAE,UAAU,YAAU,OAAK,SAAOO,GAAE;AAAA,EAAE,GAAE,CAACP,GAAE,SAAQA,GAAE,UAAU,OAAO,CAAC,GAAES,SAAE,eAAAI,SAAE,OAAK,EAAC,MAAKb,GAAE,kBAAgB,GAAE,QAAO,OAAM,IAAG,CAACA,EAAC,CAAC,GAAEM,MAAE,EAAC,mBAAkBD,IAAE,MAAK,WAAU,wBAAuBL,GAAE,SAAO,IAAE,OAAG,QAAO,IAAGL,KAAE,KAAIM,IAAC;AAAE,SAAOD,GAAE,WAASA,GAAE,kBAAgB,KAAG,OAAO,OAAOG,KAAE,EAAC,UAAS,eAAAY,QAAE,cAAc,IAAG,MAAKZ,IAAE,QAAQ,EAAC,CAAC,GAAE,EAAE,EAAC,UAASG,KAAE,YAAWH,KAAE,MAAKM,IAAE,YAAW,IAAG,UAAS,IAAG,SAAQL,KAAE,MAAK,mBAAkB,CAAC;AAAC;AAAC,IAAI,KAAG;AAAK,SAAS,GAAGP,KAAEC,IAAE;AAAC,MAAIiC;AAAE,MAAIrC,MAAE,EAAE,GAAE,EAAC,IAAGC,MAAE,8BAA8BD,GAAC,IAAG,UAASK,KAAE,OAAG,OAAMI,KAAE,OAAMH,KAAE,MAAK,GAAGC,IAAC,IAAEJ,KAAED,KAAEgB,GAAE,iBAAiB,GAAER,MAAE,GAAG,iBAAiB,GAAEC,KAAET,GAAE,UAAQA,GAAE,sBAAoBA,GAAE,eAAeO,GAAC,IAAEP,GAAE,sBAAoB,OAAK,UAAKmC,KAAEnC,GAAE,QAAQA,GAAE,iBAAiB,MAAI,OAAK,SAAOmC,GAAE,QAAMpC,KAAEc,KAAEb,GAAE,WAAWO,GAAC,GAAEG,UAAE,eAAAoB,QAAE,IAAI,GAAEnB,KAAEY,GAAG,EAAC,UAASpB,IAAE,OAAMI,KAAE,QAAOG,KAAE,OAAMN,GAAC,CAAC,GAAEQ,UAAE,eAAAG,YAAG,EAAE,GAAEO,KAAEyB,GAAE7C,IAAEQ,KAAEE,MAAEA,IAAE,iBAAe,IAAI,GAAEW,MAAEzB,GAAE,MAAIU,IAAE,SAASD,GAAC,CAAC;AAAE,IAAE,MAAIC,IAAE,eAAeT,KAAEY,EAAC,GAAE,CAACA,IAAEZ,GAAC,CAAC;AAAE,MAAIO,UAAE,eAAAwB,QAAE,EAAE9B,GAAE,WAASA,GAAE,WAAW;AAAE,IAAE,MAAI;AAAC,QAAG,CAACA,GAAE,WAAS,CAACA,GAAE,WAAW;AAAO,QAAIoC,KAAEtC,GAAG;AAAE,WAAOsC,GAAE,sBAAsB,MAAI;AAAC,MAAA9B,IAAE,UAAQ;AAAA,IAAE,CAAC,GAAE8B,GAAE;AAAA,EAAO,GAAE,CAACpC,GAAE,SAAQA,GAAE,UAAU,CAAC,GAAE,EAAE,MAAI;AAAC,QAAG,CAACM,IAAE,WAASN,GAAE,kBAAgB,KAAG,CAACS,MAAGT,GAAE,sBAAoB,EAAE;AAAO,QAAIoC,KAAEtC,GAAG;AAAE,WAAOsC,GAAE,sBAAsB,MAAI;AAAC,UAAII,IAAEC;AAAE,OAACA,MAAGD,KAAE9B,IAAE,YAAU,OAAK,SAAO8B,GAAE,mBAAiB,QAAMC,GAAE,KAAKD,IAAE,EAAC,OAAM,UAAS,CAAC;AAAA,IAAC,CAAC,GAAEJ,GAAE;AAAA,EAAO,GAAE,CAAC1B,KAAED,IAAET,GAAE,eAAcA,GAAE,mBAAkBA,GAAE,iBAAiB,CAAC;AAAE,MAAIwB,KAAE1B,GAAE,CAAAsC,OAAG;AAAC,QAAII;AAAE,QAAGrC,OAAIqC,KAAExC,GAAE,YAAU,QAAMwC,GAAE,SAASjC,GAAC,EAAE,QAAO6B,GAAE,eAAe;AAAE,IAAAb,IAAE,GAAE,EAAG,KAAG,sBAAsB,MAAI;AAAC,UAAIkB;AAAE,cAAOA,KAAEzC,GAAE,SAAS,YAAU,OAAK,SAAOyC,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,IAAC,CAAC,GAAEzC,GAAE,SAAO,KAAG,sBAAsB,MAAIQ,IAAE,cAAc,CAAC;AAAA,EAAC,CAAC,GAAEiB,KAAE3B,GAAE,MAAI;AAAC,QAAI0C;AAAE,QAAGrC,OAAIqC,KAAExC,GAAE,YAAU,QAAMwC,GAAE,SAASjC,GAAC,EAAE,QAAOC,IAAE,WAAWH,GAAE,OAAO;AAAE,QAAI+B,KAAEpC,GAAE,eAAeO,GAAC;AAAE,IAAAC,IAAE,WAAWH,GAAE,UAAS+B,EAAC;AAAA,EAAC,CAAC,GAAEV,KAAEnB,GAAG,GAAEsB,KAAE/B,GAAE,CAAAsC,OAAGV,GAAE,OAAOU,EAAC,CAAC,GAAEL,KAAEjC,GAAE,CAAAsC,OAAG;AAAC,QAAIK;AAAE,QAAG,CAACf,GAAE,SAASU,EAAC,KAAGjC,OAAIsC,KAAEzC,GAAE,YAAU,QAAMyC,GAAE,SAASlC,GAAC,KAAGE,GAAE;AAAO,QAAI+B,KAAExC,GAAE,eAAeO,GAAC;AAAE,IAAAC,IAAE,WAAWH,GAAE,UAASmC,IAAE,CAAC;AAAA,EAAC,CAAC,GAAER,KAAElC,GAAE,CAAAsC,OAAG;AAAC,QAAII;AAAE,IAAAd,GAAE,SAASU,EAAC,MAAIjC,OAAIqC,KAAExC,GAAE,YAAU,QAAMwC,GAAE,SAASjC,GAAC,KAAGE,OAAIT,GAAE,gBAAgB,QAAQ,QAAMQ,IAAE,WAAWH,GAAE,OAAO;AAAA,EAAG,CAAC,GAAE4B,SAAE,eAAAhB,SAAE,OAAK,EAAC,QAAOR,IAAE,UAASI,IAAE,UAASV,GAAC,IAAG,CAACM,IAAEI,IAAEV,EAAC,CAAC;AAAE,SAAO,EAAE,EAAC,UAAS,EAAC,IAAGJ,KAAE,KAAIuB,IAAE,MAAK,UAAS,UAASnB,OAAI,OAAG,SAAO,IAAG,iBAAgBA,OAAI,OAAG,OAAG,QAAO,iBAAgBU,IAAE,UAAS,QAAO,SAAQW,IAAE,SAAQC,IAAE,gBAAeI,IAAE,cAAaA,IAAE,eAAcE,IAAE,aAAYA,IAAE,gBAAeC,IAAE,cAAaA,GAAC,GAAE,YAAW3B,KAAE,MAAK4B,IAAE,YAAW,IAAG,MAAK,kBAAiB,CAAC;AAAC;AAAC,IAAI,KAAG,EAAE,EAAE;AAAX,IAAa,KAAG,EAAE,EAAE;AAApB,IAAsB,KAAG,EAAE,EAAE;AAA7B,IAA+B,KAAG,EAAE,EAAE;AAAtC,IAAwC,KAAG,EAAE,EAAE;AAA/C,IAAiD,KAAG,EAAE,EAAE;AAAxD,IAA0D,KAAG,OAAO,OAAO,IAAG,EAAC,OAAM,IAAG,QAAO,IAAG,OAAM,IAAG,SAAQ,IAAG,QAAO,GAAE,CAAC;;;AmCAh6nB,IAAA8B,iBAA2J;;;ACA3J,IAAAC,iBAA0B;;;ACA1B,IAAAC,iBAA0B;AAA+D,SAASC,GAAEC,IAAEC,IAAEC,KAAEC,KAAE;AAAC,MAAIC,KAAEC,GAAEH,GAAC;AAAE,qBAAAI,WAAE,MAAI;AAAC,IAAAN,KAAEA,MAAG,OAAKA,KAAE;AAAO,aAASO,GAAEC,KAAE;AAAC,MAAAJ,GAAE,QAAQI,GAAC;AAAA,IAAC;AAAC,WAAOR,GAAE,iBAAiBC,IAAEM,IAAEJ,GAAC,GAAE,MAAIH,GAAE,oBAAoBC,IAAEM,IAAEJ,GAAC;AAAA,EAAC,GAAE,CAACH,IAAEC,IAAEE,GAAC,CAAC;AAAC;;;ACAxP,IAAAM,iBAAuB;AAA0E,SAASC,KAAG;AAAC,MAAIC,SAAE,eAAAC,QAAE,KAAE;AAAE,SAAO,EAAE,OAAKD,GAAE,UAAQ,MAAG,MAAI;AAAC,IAAAA,GAAE,UAAQ;AAAA,EAAE,IAAG,CAAC,CAAC,GAAEA;AAAC;;;ACA9K,IAAAE,iBAAsC;AAAqG,SAASC,GAAEC,KAAE;AAAC,MAAIC,KAAEC,GAAEF,GAAC,GAAEG,SAAE,eAAAC,QAAE,KAAE;AAAE,qBAAAC,WAAE,OAAKF,GAAE,UAAQ,OAAG,MAAI;AAAC,IAAAA,GAAE,UAAQ,MAAGH,GAAE,MAAI;AAAC,MAAAG,GAAE,WAASF,GAAE;AAAA,IAAC,CAAC;AAAA,EAAC,IAAG,CAACA,EAAC,CAAC;AAAC;;;ACAhP,IAAAK,iBAAuB;AAA+D,IAAIC,OAAG,CAAAC,QAAIA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,YAAU,CAAC,IAAE,aAAYA,KAAID,OAAG,CAAC,CAAC;AAAE,SAASE,KAAG;AAAC,MAAIC,SAAE,eAAAC,QAAE,CAAC;AAAE,SAAOJ,GAAE,WAAU,CAAAK,QAAG;AAAC,IAAAA,IAAE,QAAM,UAAQF,GAAE,UAAQE,IAAE,WAAS,IAAE;AAAA,EAAE,GAAE,IAAE,GAAEF;AAAC;;;AJAqzB,SAASG,GAAEC,KAAE;AAAC,MAAG,CAACA,IAAE,QAAO,oBAAI;AAAI,MAAG,OAAOA,OAAG,WAAW,QAAO,IAAI,IAAIA,IAAE,CAAC;AAAE,MAAIC,KAAE,oBAAI;AAAI,WAAQC,MAAKF,IAAE,QAAQ,CAAAE,GAAE,mBAAmB,eAAaD,GAAE,IAAIC,GAAE,OAAO;AAAE,SAAOD;AAAC;AAAC,IAAI,IAAE;AAAM,IAAIE,MAAG,CAAAC,QAAIA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,eAAa,CAAC,IAAE,gBAAeA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,YAAU,CAAC,IAAE,aAAYA,GAAEA,GAAE,eAAa,EAAE,IAAE,gBAAeA,GAAEA,GAAE,MAAI,EAAE,IAAE,OAAMA,KAAID,MAAG,CAAC,CAAC;AAAE,SAAS,EAAEH,KAAEC,IAAE;AAAC,MAAIC,SAAE,eAAAG,QAAE,IAAI,GAAEC,MAAEC,GAAEL,IAAED,EAAC,GAAE,EAAC,cAAaO,KAAE,YAAWC,KAAE,UAASL,KAAE,IAAG,GAAGM,IAAC,IAAEV;AAAE,EAAAQ,GAAE,MAAIJ,KAAE;AAAG,MAAIO,KAAEV,GAAEC,EAAC;AAAE,IAAE,EAAC,eAAcS,GAAC,GAAE,QAAQP,KAAE,EAAE,CAAC;AAAE,MAAIQ,MAAE,EAAE,EAAC,eAAcD,IAAE,WAAUT,IAAE,cAAaM,IAAC,GAAE,QAAQJ,KAAE,CAAC,CAAC;AAAE,IAAE,EAAC,eAAcO,IAAE,WAAUT,IAAE,YAAWO,KAAE,uBAAsBG,IAAC,GAAE,QAAQR,KAAE,CAAC,CAAC;AAAE,MAAIG,KAAEN,GAAE,GAAEY,KAAEP,GAAE,CAAAQ,QAAG;AAAC,QAAIC,MAAEb,GAAE;AAAQ,QAAG,CAACa,IAAE;AAAO,KAAC,CAAAC,OAAGA,GAAE,GAAG,MAAI;AAAC,QAAET,GAAE,SAAQ,EAAC,CAACG,IAAE,QAAQ,GAAE,MAAI;AAAC,UAAEK,KAAE,EAAE,OAAM,EAAC,cAAa,CAACD,IAAE,aAAa,EAAC,CAAC;AAAA,MAAC,GAAE,CAACJ,IAAE,SAAS,GAAE,MAAI;AAAC,UAAEK,KAAE,EAAE,MAAK,EAAC,cAAa,CAACD,IAAE,aAAa,EAAC,CAAC;AAAA,MAAC,EAAC,CAAC;AAAA,IAAC,CAAC;AAAA,EAAC,CAAC,GAAEG,KAAE,EAAE,GAAEC,SAAE,eAAAb,QAAE,KAAE,GAAEc,KAAE,EAAC,KAAIb,KAAE,UAAUQ,KAAE;AAAC,IAAAA,IAAE,OAAK,UAAQI,GAAE,UAAQ,MAAGD,GAAE,sBAAsB,MAAI;AAAC,MAAAC,GAAE,UAAQ;AAAA,IAAE,CAAC;AAAA,EAAE,GAAE,OAAOJ,KAAE;AAAC,QAAIC,MAAEhB,GAAEU,GAAC;AAAE,IAAAP,GAAE,mBAAmB,eAAaa,IAAE,IAAIb,GAAE,OAAO;AAAE,QAAIkB,KAAEN,IAAE;AAAc,IAAAM,cAAa,eAAaA,GAAE,QAAQ,yBAAuB,WAASC,GAAEN,KAAEK,EAAC,MAAIF,GAAE,UAAQ,EAAEhB,GAAE,SAAQ,EAAEK,GAAE,SAAQ,EAAC,CAACG,IAAE,QAAQ,GAAE,MAAI,EAAE,MAAK,CAACA,IAAE,SAAS,GAAE,MAAI,EAAE,SAAQ,CAAC,IAAE,EAAE,YAAW,EAAC,YAAWI,IAAE,OAAM,CAAC,IAAEA,IAAE,kBAAkB,eAAa,EAAEA,IAAE,MAAM;AAAA,EAAG,EAAC;AAAE,SAAO,eAAAQ,QAAE,cAAc,eAAAA,QAAE,UAAS,MAAK,QAAQlB,KAAE,CAAC,KAAG,eAAAkB,QAAE,cAAcV,IAAE,EAAC,IAAG,UAAS,MAAK,UAAS,+BAA8B,MAAG,SAAQC,IAAE,UAASH,GAAE,UAAS,CAAC,GAAE,EAAE,EAAC,UAASS,IAAE,YAAWT,KAAE,YAAW,GAAE,MAAK,YAAW,CAAC,GAAE,QAAQN,KAAE,CAAC,KAAG,eAAAkB,QAAE,cAAcV,IAAE,EAAC,IAAG,UAAS,MAAK,UAAS,+BAA8B,MAAG,SAAQC,IAAE,UAASH,GAAE,UAAS,CAAC,CAAC;AAAC;AAAC,IAAIa,KAAE,EAAE,CAAC;AAAT,IAAWC,MAAG,OAAO,OAAOD,IAAE,EAAC,UAASpB,GAAC,CAAC;AAAE,SAAS,EAAEH,MAAE,MAAG;AAAC,MAAIC,SAAE,eAAAI,QAAEL,IAAE,MAAM,CAAC;AAAE,SAAOe,GAAE,CAAC,CAACb,EAAC,GAAE,CAACI,GAAC,MAAI;AAAC,IAAAA,QAAI,QAAIJ,OAAI,SAAIF,GAAE,MAAI;AAAC,MAAAC,GAAE,QAAQ,OAAO,CAAC;AAAA,IAAC,CAAC,GAAEK,QAAI,SAAIJ,OAAI,SAAKD,GAAE,UAAQD,IAAE,MAAM;AAAA,EAAE,GAAE,CAACA,KAAEA,KAAEC,EAAC,CAAC,GAAEK,GAAE,MAAI;AAAC,QAAIJ;AAAE,YAAOA,KAAED,GAAE,QAAQ,KAAK,CAAAK,QAAGA,OAAG,QAAMA,IAAE,WAAW,MAAI,OAAKJ,KAAE;AAAA,EAAI,CAAC;AAAC;AAAC,SAAS,EAAE,EAAC,eAAcF,IAAC,GAAEC,IAAE;AAAC,MAAIC,KAAE,EAAED,EAAC;AAAE,EAAAc,GAAE,MAAI;AAAC,IAAAd,OAAID,OAAG,OAAK,SAAOA,IAAE,oBAAkBA,OAAG,OAAK,SAAOA,IAAE,SAAO,EAAEE,GAAE,CAAC;AAAA,EAAC,GAAE,CAACD,EAAC,CAAC,GAAEQ,GAAE,MAAI;AAAC,IAAAR,MAAG,EAAEC,GAAE,CAAC;AAAA,EAAC,CAAC;AAAC;AAAC,SAAS,EAAE,EAAC,eAAcF,KAAE,WAAUC,IAAE,cAAaC,GAAC,GAAEI,KAAE;AAAC,MAAIE,UAAE,eAAAH,QAAE,IAAI,GAAEI,MAAEgB,GAAE;AAAE,SAAOV,GAAE,MAAI;AAAC,QAAG,CAACT,IAAE;AAAO,QAAIF,KAAEH,GAAE;AAAQ,IAAAG,MAAGJ,GAAE,MAAI;AAAC,UAAG,CAACS,IAAE,QAAQ;AAAO,UAAIC,MAAEV,OAAG,OAAK,SAAOA,IAAE;AAAc,UAAGE,MAAG,QAAMA,GAAE,SAAQ;AAAC,aAAIA,MAAG,OAAK,SAAOA,GAAE,aAAWQ,KAAE;AAAC,UAAAF,IAAE,UAAQE;AAAE;AAAA,QAAM;AAAA,MAAC,WAASN,GAAE,SAASM,GAAC,GAAE;AAAC,QAAAF,IAAE,UAAQE;AAAE;AAAA,MAAM;AAAC,MAAAR,MAAG,QAAMA,GAAE,UAAQ,EAAEA,GAAE,OAAO,IAAE,EAAEE,IAAE,EAAE,KAAK,MAAI,EAAE,SAAO,QAAQ,KAAK,0DAA0D,GAAEI,IAAE,UAAQR,OAAG,OAAK,SAAOA,IAAE;AAAA,IAAa,CAAC;AAAA,EAAC,GAAE,CAACM,GAAC,CAAC,GAAEE;AAAC;AAAC,SAAS,EAAE,EAAC,eAAcR,KAAE,WAAUC,IAAE,YAAWC,IAAE,uBAAsBI,IAAC,GAAEE,KAAE;AAAC,MAAIC,MAAEgB,GAAE;AAAE,EAAAH,GAAEtB,OAAG,OAAK,SAAOA,IAAE,aAAY,SAAQ,CAAAI,OAAG;AAAC,QAAG,CAACI,OAAG,CAACC,IAAE,QAAQ;AAAO,QAAIC,MAAEX,GAAEG,EAAC;AAAE,IAAAD,GAAE,mBAAmB,eAAaS,IAAE,IAAIT,GAAE,OAAO;AAAE,QAAIU,KAAEL,IAAE;AAAQ,QAAG,CAACK,GAAE;AAAO,QAAIC,MAAER,GAAE;AAAO,IAAAQ,OAAGA,eAAa,cAAYS,GAAEX,KAAEE,GAAC,KAAGN,IAAE,UAAQM,KAAE,EAAEA,GAAC,MAAIR,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAE,EAAEO,EAAC,KAAG,EAAEL,IAAE,OAAO;AAAA,EAAC,GAAE,IAAE;AAAC;AAAC,SAASe,GAAErB,KAAEC,IAAE;AAAC,WAAQC,MAAKF,IAAE,KAAGE,GAAE,SAASD,EAAC,EAAE,QAAM;AAAG,SAAM;AAAE;;;AKAzhI,IAAAyB,iBAAqH;AAAQ,IAAAC,oBAA6B;;;ACA1J,IAAAC,iBAAiD;AAAQ,IAAIC,SAAE,eAAAC,eAAE,KAAE;AAAE,SAASC,KAAG;AAAC,aAAO,eAAAC,YAAEH,EAAC;AAAC;AAAC,SAASI,GAAEC,KAAE;AAAC,SAAO,eAAAC,QAAE,cAAcN,GAAE,UAAS,EAAC,OAAMK,IAAE,MAAK,GAAEA,IAAE,QAAQ;AAAC;;;ADA0kB,SAASE,GAAEC,IAAE;AAAC,MAAIC,KAAEC,GAAE,GAAEC,UAAE,eAAAC,YAAEC,EAAC,GAAEC,KAAEL,GAAED,EAAC,GAAE,CAACE,KAAEK,GAAC,QAAE,eAAAC,UAAE,MAAI;AAAC,QAAG,CAACP,MAAGE,QAAI,QAAM,EAAE,SAAS,QAAO;AAAK,QAAIM,MAAEH,MAAG,OAAK,SAAOA,GAAE,eAAe,wBAAwB;AAAE,QAAGG,IAAE,QAAOA;AAAE,QAAGH,OAAI,KAAK,QAAO;AAAK,QAAII,KAAEJ,GAAE,cAAc,KAAK;AAAE,WAAOI,GAAE,aAAa,MAAK,wBAAwB,GAAEJ,GAAE,KAAK,YAAYI,EAAC;AAAA,EAAC,CAAC;AAAE,aAAO,eAAAC,WAAE,MAAI;AAAC,IAAAT,QAAI,SAAOI,MAAG,QAAMA,GAAE,KAAK,SAASJ,GAAC,KAAGI,MAAG,QAAMA,GAAE,KAAK,YAAYJ,GAAC;AAAA,EAAE,GAAE,CAACA,KAAEI,EAAC,CAAC,OAAE,eAAAK,WAAE,MAAI;AAAC,IAAAV,MAAGE,QAAI,QAAMI,IAAEJ,IAAE,OAAO;AAAA,EAAC,GAAE,CAACA,KAAEI,KAAEN,EAAC,CAAC,GAAEC;AAAC;AAAC,IAAIU,KAAE,eAAAC;AAAE,SAASC,GAAEd,IAAEC,IAAE;AAAC,MAAIE,MAAEH,IAAEM,SAAE,eAAAS,QAAE,IAAI,GAAEb,MAAEc,GAAEC,GAAE,CAAAC,QAAG;AAAC,IAAAZ,GAAE,UAAQY;AAAA,EAAC,CAAC,GAAEjB,EAAC,GAAEM,MAAEN,GAAEK,EAAC,GAAEG,MAAEV,GAAEO,EAAC,GAAE,CAACI,EAAC,QAAE,eAAAF,UAAE,MAAI;AAAC,QAAIU;AAAE,WAAO,EAAE,WAAS,QAAMA,MAAEX,OAAG,OAAK,SAAOA,IAAE,cAAc,KAAK,MAAI,OAAKW,MAAE;AAAA,EAAI,CAAC,GAAEC,SAAE,eAAAf,YAAEgB,EAAC,GAAEC,KAAElB,GAAE;AAAE,SAAO,EAAE,MAAI;AAAC,KAACM,OAAG,CAACC,MAAGD,IAAE,SAASC,EAAC,MAAIA,GAAE,aAAa,0BAAyB,EAAE,GAAED,IAAE,YAAYC,EAAC;AAAA,EAAE,GAAE,CAACD,KAAEC,EAAC,CAAC,GAAE,EAAE,MAAI;AAAC,QAAGA,MAAGS,GAAE,QAAOA,GAAE,SAAST,EAAC;AAAA,EAAC,GAAE,CAACS,IAAET,EAAC,CAAC,GAAEY,GAAE,MAAI;AAAC,QAAIJ;AAAE,KAACT,OAAG,CAACC,OAAIA,cAAa,QAAMD,IAAE,SAASC,EAAC,KAAGD,IAAE,YAAYC,EAAC,GAAED,IAAE,WAAW,UAAQ,OAAKS,MAAET,IAAE,kBAAgB,QAAMS,IAAE,YAAYT,GAAC;AAAA,EAAG,CAAC,GAAEY,KAAE,CAACZ,OAAG,CAACC,KAAE,WAAK,kBAAAa,cAAE,EAAE,EAAC,UAAS,EAAC,KAAIrB,IAAC,GAAE,YAAWC,KAAE,YAAWS,IAAE,MAAK,SAAQ,CAAC,GAAEF,EAAC,IAAE;AAAI;AAAC,IAAIc,KAAE,eAAAX;AAAN,IAAQR,SAAE,eAAAoB,eAAE,IAAI;AAAE,SAASC,GAAE1B,IAAEC,IAAE;AAAC,MAAG,EAAC,QAAOE,KAAE,GAAGG,GAAC,IAAEN,IAAEO,MAAE,EAAC,KAAIS,GAAEf,EAAC,EAAC;AAAE,SAAO,eAAAgB,QAAE,cAAcZ,GAAE,UAAS,EAAC,OAAMF,IAAC,GAAE,EAAE,EAAC,UAASI,KAAE,YAAWD,IAAE,YAAWkB,IAAE,MAAK,gBAAe,CAAC,CAAC;AAAC;AAAC,IAAIJ,SAAE,eAAAK,eAAE,IAAI;AAAE,SAASE,MAAI;AAAC,MAAI3B,SAAE,eAAAI,YAAEgB,EAAC,GAAEnB,SAAE,eAAAc,QAAE,CAAC,CAAC,GAAEZ,MAAEI,GAAE,CAAAA,SAAIN,GAAE,QAAQ,KAAKM,GAAC,GAAEP,MAAGA,GAAE,SAASO,GAAC,GAAE,MAAID,GAAEC,GAAC,EAAE,GAAED,KAAEC,GAAE,CAAAA,QAAG;AAAC,QAAIE,MAAER,GAAE,QAAQ,QAAQM,GAAC;AAAE,IAAAE,QAAI,MAAIR,GAAE,QAAQ,OAAOQ,KAAE,CAAC,GAAET,MAAGA,GAAE,WAAWO,GAAC;AAAA,EAAC,CAAC,GAAEL,UAAE,eAAA0B,SAAE,OAAK,EAAC,UAASzB,KAAE,YAAWG,IAAE,SAAQL,GAAC,IAAG,CAACE,KAAEG,IAAEL,EAAC,CAAC;AAAE,SAAM,CAACA,QAAE,eAAA2B,SAAE,MAAI,SAAS,EAAC,UAASnB,IAAC,GAAE;AAAC,WAAO,eAAAQ,QAAE,cAAcG,GAAE,UAAS,EAAC,OAAMlB,IAAC,GAAEO,GAAC;AAAA,EAAC,GAAE,CAACP,GAAC,CAAC,CAAC;AAAC;AAAC,IAAI2B,KAAE,EAAEf,EAAC;AAAT,IAAWgB,KAAE,EAAEJ,EAAC;AAAhB,IAAkB,KAAG,OAAO,OAAOG,IAAE,EAAC,OAAMC,GAAC,CAAC;;;AEAtxE,IAAAC,KAAgB;;;ACAhB,IAAAC,KAAgB;AAAQ,SAASC,GAAEC,IAAEC,KAAE;AAAC,SAAOD,OAAIC,QAAID,OAAI,KAAG,IAAEA,OAAI,IAAEC,QAAID,OAAIA,MAAGC,QAAIA;AAAC;AAAC,IAAMC,KAAE,OAAO,OAAO,MAAI,aAAW,OAAO,KAAGH;AAA/C,IAAiD,EAAC,UAASI,IAAE,WAAUC,IAAE,iBAAgBC,IAAE,eAAcC,GAAC,IAAER;AAAgB,SAASS,GAAEC,IAAEC,KAAEC,KAAE;AAAC,QAAMC,MAAEF,IAAE,GAAE,CAAC,EAAC,MAAKG,GAAC,GAAEC,GAAC,IAAEC,GAAE,EAAC,MAAK,EAAC,OAAMH,KAAE,aAAYF,IAAC,EAAC,CAAC;AAAE,SAAOM,GAAE,MAAI;AAAC,IAAAH,GAAE,QAAMD,KAAEC,GAAE,cAAYH,KAAEO,GAAEJ,EAAC,KAAGC,IAAE,EAAC,MAAKD,GAAC,CAAC;AAAA,EAAC,GAAE,CAACJ,IAAEG,KAAEF,GAAC,CAAC,GAAEQ,GAAE,OAAKD,GAAEJ,EAAC,KAAGC,IAAE,EAAC,MAAKD,GAAC,CAAC,GAAEJ,GAAE,MAAI;AAAC,IAAAQ,GAAEJ,EAAC,KAAGC,IAAE,EAAC,MAAKD,GAAC,CAAC;AAAA,EAAC,CAAC,IAAG,CAACJ,EAAC,CAAC,GAAEU,GAAEP,GAAC,GAAEA;AAAC;AAAC,SAASK,GAAER,IAAE;AAAC,QAAMC,MAAED,GAAE,aAAYE,MAAEF,GAAE;AAAM,MAAG;AAAC,UAAMG,MAAEF,IAAE;AAAE,WAAM,CAACU,GAAET,KAAEC,GAAC;AAAA,EAAC,QAAM;AAAC,WAAM;AAAA,EAAE;AAAC;;;ACAhgB,SAASS,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAOD,GAAE;AAAC;;;AFAkJ,IAAME,KAAE,OAAO,UAAQ,eAAa,OAAO,OAAO,YAAU,eAAa,OAAO,OAAO,SAAS,iBAAe;AAA/G,IAA2HC,MAAE,CAACD;AAA9H,IAAgIE,KAAED,MAAEE,MAAEC;AAAtI,IAAwIC,KAAE,0BAAyBC,MAAG,CAAAC,OAAGA,GAAE,sBAAsBD,EAAC,IAAEJ;;;AGAlS,SAASM,GAAEC,KAAE;AAAC,SAAOC,GAAED,IAAE,WAAUA,IAAE,aAAYA,IAAE,WAAW;AAAC;;;ACA/I,SAASE,GAAEC,KAAEC,IAAE;AAAC,MAAIC,MAAEF,IAAE,GAAEG,KAAE,oBAAI;AAAI,SAAM,EAAC,cAAa;AAAC,WAAOD;AAAA,EAAC,GAAE,UAAUE,IAAE;AAAC,WAAOD,GAAE,IAAIC,EAAC,GAAE,MAAID,GAAE,OAAOC,EAAC;AAAA,EAAC,GAAE,SAASA,OAAKC,KAAE;AAAC,QAAIC,KAAEL,GAAEG,EAAC,EAAE,KAAKF,KAAE,GAAGG,GAAC;AAAE,IAAAC,OAAIJ,MAAEI,IAAEH,GAAE,QAAQ,CAAAI,QAAGA,IAAE,CAAC;AAAA,EAAE,EAAC;AAAC;;;ACAtL,SAASC,KAAG;AAAC,MAAIC;AAAE,SAAM,EAAC,OAAO,EAAC,KAAIC,GAAC,GAAE;AAAC,QAAIC;AAAE,QAAIC,KAAEF,GAAE;AAAgB,IAAAD,QAAIE,MAAED,GAAE,gBAAc,OAAKC,MAAE,QAAQ,aAAWC,GAAE;AAAA,EAAW,GAAE,MAAM,EAAC,KAAIF,IAAE,GAAEE,GAAC,GAAE;AAAC,QAAIC,MAAEH,GAAE,iBAAgBC,MAAEE,IAAE,cAAYA,IAAE,aAAYC,KAAEL,MAAEE;AAAE,IAAAC,GAAE,MAAMC,KAAE,gBAAe,GAAGC,EAAC,IAAI;AAAA,EAAC,EAAC;AAAC;;;ACAjJ,SAASC,MAAG;AAAC,SAAOC,GAAE,IAAE,EAAC,OAAO,EAAC,KAAIC,IAAE,GAAEC,KAAE,MAAKC,IAAC,GAAE;AAAC,aAASC,IAAEC,KAAE;AAAC,aAAOF,IAAE,WAAW,QAAQ,CAAAG,OAAGA,GAAE,CAAC,EAAE,KAAK,CAAAA,OAAGA,GAAE,SAASD,GAAC,CAAC;AAAA,IAAC;AAAC,IAAAH,IAAE,UAAU,MAAI;AAAC,UAAIK;AAAE,UAAG,OAAO,iBAAiBN,GAAE,eAAe,EAAE,mBAAiB,QAAO;AAAC,YAAID,MAAEI,GAAE;AAAE,QAAAJ,IAAE,MAAMC,GAAE,iBAAgB,kBAAiB,MAAM,GAAEC,IAAE,IAAI,MAAIA,IAAE,UAAU,MAAIF,IAAE,QAAQ,CAAC,CAAC;AAAA,MAAC;AAAC,UAAIK,OAAGE,MAAE,OAAO,YAAU,OAAKA,MAAE,OAAO,aAAYD,KAAE;AAAK,MAAAJ,IAAE,iBAAiBD,IAAE,SAAQ,CAAAD,QAAG;AAAC,YAAGA,IAAE,kBAAkB,YAAY,KAAG;AAAC,cAAIQ,KAAER,IAAE,OAAO,QAAQ,GAAG;AAAE,cAAG,CAACQ,GAAE;AAAO,cAAG,EAAC,MAAKC,IAAC,IAAE,IAAI,IAAID,GAAE,IAAI,GAAEE,KAAET,GAAE,cAAcQ,GAAC;AAAE,UAAAC,MAAG,CAACN,IAAEM,EAAC,MAAIJ,KAAEI;AAAA,QAAE,QAAM;AAAA,QAAC;AAAA,MAAC,GAAE,IAAE,GAAER,IAAE,iBAAiBD,IAAE,cAAa,CAAAD,QAAG;AAAC,YAAGA,IAAE,kBAAkB,YAAY,KAAGI,IAAEJ,IAAE,MAAM,GAAE;AAAC,cAAIQ,KAAER,IAAE;AAAO,iBAAKQ,GAAE,iBAAeJ,IAAEI,GAAE,aAAa,IAAG,CAAAA,KAAEA,GAAE;AAAc,UAAAN,IAAE,MAAMM,IAAE,sBAAqB,SAAS;AAAA,QAAC,MAAM,CAAAN,IAAE,MAAMF,IAAE,QAAO,eAAc,MAAM;AAAA,MAAC,CAAC,GAAEE,IAAE,iBAAiBD,IAAE,aAAY,CAAAD,QAAG;AAAC,YAAGA,IAAE,kBAAkB,YAAY,KAAGI,IAAEJ,IAAE,MAAM,GAAE;AAAC,cAAIQ,KAAER,IAAE;AAAO,iBAAKQ,GAAE,iBAAeA,GAAE,QAAQ,qBAAmB,MAAI,EAAEA,GAAE,eAAaA,GAAE,gBAAcA,GAAE,cAAYA,GAAE,eAAc,CAAAA,KAAEA,GAAE;AAAc,UAAAA,GAAE,QAAQ,qBAAmB,MAAIR,IAAE,eAAe;AAAA,QAAC,MAAM,CAAAA,IAAE,eAAe;AAAA,MAAC,GAAE,EAAC,SAAQ,MAAE,CAAC,GAAEE,IAAE,IAAI,MAAI;AAAC,YAAIM;AAAE,YAAIR,OAAGQ,KAAE,OAAO,YAAU,OAAKA,KAAE,OAAO;AAAY,QAAAH,QAAIL,OAAG,OAAO,SAAS,GAAEK,GAAC,GAAEC,MAAGA,GAAE,gBAAcA,GAAE,eAAe,EAAC,OAAM,UAAS,CAAC,GAAEA,KAAE;AAAA,MAAK,CAAC;AAAA,IAAC,CAAC;AAAA,EAAC,EAAC,IAAE,CAAC;AAAC;;;ACAj5C,SAASK,KAAG;AAAC,SAAM,EAAC,OAAO,EAAC,KAAIC,IAAE,GAAEC,IAAC,GAAE;AAAC,IAAAA,IAAE,MAAMD,GAAE,iBAAgB,YAAW,QAAQ;AAAA,EAAC,EAAC;AAAC;;;ACA0M,SAASE,GAAEC,IAAE;AAAC,MAAIC,KAAE,CAAC;AAAE,WAAQC,OAAKF,GAAE,QAAO,OAAOC,IAAEC,IAAED,EAAC,CAAC;AAAE,SAAOA;AAAC;AAAC,IAAIE,KAAEA,GAAE,MAAI,oBAAI,OAAI,EAAC,KAAKH,IAAEC,IAAE;AAAC,MAAIG;AAAE,MAAIF,OAAGE,MAAE,KAAK,IAAIJ,EAAC,MAAI,OAAKI,MAAE,EAAC,KAAIJ,IAAE,OAAM,GAAE,GAAEI,GAAE,GAAE,MAAK,oBAAI,MAAG;AAAE,SAAOF,IAAE,SAAQA,IAAE,KAAK,IAAID,EAAC,GAAE,KAAK,IAAID,IAAEE,GAAC,GAAE;AAAI,GAAE,IAAIF,IAAEC,IAAE;AAAC,MAAIC,MAAE,KAAK,IAAIF,EAAC;AAAE,SAAOE,QAAIA,IAAE,SAAQA,IAAE,KAAK,OAAOD,EAAC,IAAG;AAAI,GAAE,eAAe,EAAC,KAAID,IAAE,GAAEC,IAAE,MAAKC,IAAC,GAAE;AAAC,MAAIE,MAAE,EAAC,KAAIJ,IAAE,GAAEC,IAAE,MAAKF,GAAEG,GAAC,EAAC,GAAEG,MAAE,CAACC,IAAE,GAAED,GAAE,GAAEE,GAAE,CAAC;AAAE,EAAAF,IAAE,QAAQ,CAAC,EAAC,QAAOG,GAAC,MAAIA,MAAG,OAAK,SAAOA,GAAEJ,GAAC,CAAC,GAAEC,IAAE,QAAQ,CAAC,EAAC,OAAMG,GAAC,MAAIA,MAAG,OAAK,SAAOA,GAAEJ,GAAC,CAAC;AAAC,GAAE,aAAa,EAAC,GAAEJ,GAAC,GAAE;AAAC,EAAAA,GAAE,QAAQ;AAAC,GAAE,SAAS,EAAC,KAAIA,GAAC,GAAE;AAAC,OAAK,OAAOA,EAAC;AAAC,EAAC,CAAC;AAAEG,GAAE,UAAU,MAAI;AAAC,MAAIH,KAAEG,GAAE,YAAY,GAAEF,KAAE,oBAAI;AAAI,WAAO,CAACC,GAAC,KAAIF,GAAE,CAAAC,GAAE,IAAIC,KAAEA,IAAE,gBAAgB,MAAM,QAAQ;AAAE,WAAQA,OAAKF,GAAE,OAAO,GAAE;AAAC,QAAII,MAAEH,GAAE,IAAIC,IAAE,GAAG,MAAI,UAASG,MAAEH,IAAE,UAAQ;AAAE,KAACG,OAAG,CAACD,OAAG,CAACC,OAAGD,QAAID,GAAE,SAASD,IAAE,QAAM,IAAE,mBAAiB,gBAAeA,GAAC,GAAEA,IAAE,UAAQ,KAAGC,GAAE,SAAS,YAAWD,GAAC;AAAA,EAAC;AAAC,CAAC;;;ACA76B,SAASO,GAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,MAAEC,GAAEC,EAAC,GAAEC,MAAEN,KAAEG,IAAE,IAAIH,EAAC,IAAE,QAAOO,KAAED,MAAEA,IAAE,QAAM,IAAE;AAAG,SAAO,EAAE,MAAI;AAAC,QAAG,EAAE,CAACN,MAAG,CAACC,IAAG,QAAOI,GAAE,SAAS,QAAOL,IAAEE,EAAC,GAAE,MAAIG,GAAE,SAAS,OAAML,IAAEE,EAAC;AAAA,EAAC,GAAE,CAACD,IAAED,EAAC,CAAC,GAAEO;AAAC;;;ACAnQ,IAAIC,KAAE,oBAAI;AAAV,IAAcC,MAAE,oBAAI;AAAI,SAAS,EAAEC,IAAEC,MAAE,MAAG;AAAC,IAAE,MAAI;AAAC,QAAIC;AAAE,QAAG,CAACD,IAAE;AAAO,QAAIE,KAAE,OAAOH,MAAG,aAAWA,GAAE,IAAEA,GAAE;AAAQ,QAAG,CAACG,GAAE;AAAO,aAASC,MAAG;AAAC,UAAIC;AAAE,UAAG,CAACF,GAAE;AAAO,UAAIG,MAAGD,MAAEN,IAAE,IAAII,EAAC,MAAI,OAAKE,MAAE;AAAE,UAAGC,OAAI,IAAEP,IAAE,OAAOI,EAAC,IAAEJ,IAAE,IAAII,IAAEG,KAAE,CAAC,GAAEA,OAAI,EAAE;AAAO,UAAIC,KAAET,GAAE,IAAIK,EAAC;AAAE,MAAAI,OAAIA,GAAE,aAAa,MAAI,OAAKJ,GAAE,gBAAgB,aAAa,IAAEA,GAAE,aAAa,eAAcI,GAAE,aAAa,CAAC,GAAEJ,GAAE,QAAMI,GAAE,OAAMT,GAAE,OAAOK,EAAC;AAAA,IAAE;AAAC,QAAIK,OAAGN,MAAEH,IAAE,IAAII,EAAC,MAAI,OAAKD,MAAE;AAAE,WAAOH,IAAE,IAAII,IAAEK,MAAE,CAAC,GAAEA,QAAI,MAAIV,GAAE,IAAIK,IAAE,EAAC,eAAcA,GAAE,aAAa,aAAa,GAAE,OAAMA,GAAE,MAAK,CAAC,GAAEA,GAAE,aAAa,eAAc,MAAM,GAAEA,GAAE,QAAM,OAAIC;AAAA,EAAC,GAAE,CAACJ,IAAEC,GAAC,CAAC;AAAC;;;ACAznB,IAAAQ,iBAAuC;AAAiK,SAASC,GAAE,EAAC,mBAAkBC,MAAE,CAAC,GAAE,SAAQC,IAAE,iBAAgBC,IAAC,IAAE,CAAC,GAAE;AAAC,MAAIC;AAAE,MAAIC,UAAE,eAAAC,SAAGF,MAAED,OAAG,OAAK,SAAOA,IAAE,YAAU,OAAKC,MAAE,IAAI,GAAEG,MAAEC,GAAEH,GAAC,GAAEI,MAAER,GAAE,MAAI;AAAC,QAAIS,IAAEC,KAAEC;AAAE,QAAIJ,KAAE,CAAC;AAAE,aAAQK,MAAKZ,IAAE,CAAAY,OAAI,SAAOA,cAAa,cAAYL,GAAE,KAAKK,EAAC,IAAE,aAAYA,MAAGA,GAAE,mBAAmB,eAAaL,GAAE,KAAKK,GAAE,OAAO;AAAG,QAAGX,MAAG,QAAMA,GAAE,QAAQ,UAAQW,MAAKX,GAAE,QAAQ,CAAAM,GAAE,KAAKK,EAAC;AAAE,aAAQA,OAAKH,KAAEH,OAAG,OAAK,SAAOA,IAAE,iBAAiB,oBAAoB,MAAI,OAAKG,KAAE,CAAC,EAAE,CAAAG,OAAI,SAAS,QAAMA,OAAI,SAAS,QAAMA,cAAa,eAAaA,GAAE,OAAK,6BAA2BA,GAAE,SAASR,IAAE,OAAO,KAAGQ,GAAE,UAAUD,OAAGD,MAAEN,IAAE,YAAU,OAAK,SAAOM,IAAE,YAAY,MAAI,OAAK,SAAOC,IAAE,IAAI,KAAGJ,GAAE,KAAK,CAAAM,OAAGD,GAAE,SAASC,EAAC,CAAC,KAAGN,GAAE,KAAKK,EAAC;AAAG,WAAOL;AAAA,EAAC,CAAC;AAAE,SAAM,EAAC,mBAAkBC,KAAE,UAASR,GAAE,CAAAO,OAAGC,IAAE,EAAE,KAAK,CAAAC,OAAGA,GAAE,SAASF,EAAC,CAAC,CAAC,GAAE,iBAAgBH,KAAE,kBAAa,eAAAU,SAAE,MAAI,WAAU;AAAC,WAAOZ,OAAG,OAAK,OAAK,eAAAa,QAAE,cAAcb,IAAE,EAAC,UAASQ,GAAE,QAAO,KAAIN,IAAC,CAAC;AAAA,EAAC,GAAE,CAACA,KAAEF,GAAC,CAAC,EAAC;AAAC;AAAC,SAASc,KAAG;AAAC,MAAIhB,UAAE,eAAAK,QAAE,IAAI;AAAE,SAAM,EAAC,iBAAgBL,KAAE,kBAAa,eAAAc,SAAE,MAAI,WAAU;AAAC,WAAO,eAAAC,QAAE,cAAcb,IAAE,EAAC,UAASQ,GAAE,QAAO,KAAIV,IAAC,CAAC;AAAA,EAAC,GAAE,CAACA,GAAC,CAAC,EAAC;AAAC;;;ACAvrC,IAAAiB,iBAAiD;AAAkI,IAAIC,SAAE,eAAAC,eAAE,MAAI;AAAC,CAAC;AAAED,GAAE,cAAY;AAAe,IAAIE,OAAG,CAAAC,QAAIA,GAAEA,GAAE,MAAI,CAAC,IAAE,OAAMA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,KAAID,OAAG,CAAC,CAAC;AAAE,SAASE,KAAG;AAAC,aAAO,eAAAC,YAAEL,EAAC;AAAC;AAAC,SAASM,GAAE,EAAC,UAASC,IAAE,UAASC,IAAE,MAAKL,IAAE,SAAQM,IAAE,SAAQC,IAAC,GAAE;AAAC,MAAIC,MAAEP,GAAE,GAAEQ,MAAEA,GAAE,IAAIC,QAAI;AAAC,IAAAL,MAAG,QAAMA,GAAE,GAAGK,GAAC,GAAEF,IAAE,GAAGE,GAAC;AAAA,EAAC,CAAC;AAAE,SAAO,EAAE,MAAI;AAAC,QAAIA,MAAEH,QAAI,UAAQA,QAAI;AAAG,WAAOG,OAAGD,IAAE,GAAET,IAAEM,EAAC,GAAE,MAAI;AAAC,MAAAI,OAAGD,IAAE,GAAET,IAAEM,EAAC;AAAA,IAAC;AAAA,EAAC,GAAE,CAACG,KAAET,IAAEM,IAAEC,GAAC,CAAC,GAAE,eAAAI,QAAE,cAAcd,GAAE,UAAS,EAAC,OAAMY,IAAC,GAAEL,EAAC;AAAC;;;ACAviB,IAAAQ,iBAA4E;AAAsT,IAAIC,UAAE,eAAAC,eAAE,IAAI;AAAE,SAASC,MAAG;AAAC,MAAIC,SAAE,eAAAC,YAAEJ,GAAC;AAAE,MAAGG,OAAI,MAAK;AAAC,QAAIE,MAAE,IAAI,MAAM,+EAA+E;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBA,KAAEH,GAAC,GAAEG;AAAA,EAAC;AAAC,SAAOF;AAAC;AAAC,SAASG,KAAG;AAAC,MAAG,CAACH,IAAEE,GAAC,QAAE,eAAAE,UAAE,CAAC,CAAC;AAAE,SAAM,CAACJ,GAAE,SAAO,IAAEA,GAAE,KAAK,GAAG,IAAE,YAAO,eAAAK,SAAE,MAAI,SAASC,IAAE;AAAC,QAAIC,KAAEC,GAAE,CAAAC,SAAIP,IAAE,CAAAM,QAAG,CAAC,GAAGA,KAAEC,GAAC,CAAC,GAAE,MAAIP,IAAE,CAAAM,QAAG;AAAC,UAAIE,KAAEF,IAAE,MAAM,GAAEG,MAAED,GAAE,QAAQD,GAAC;AAAE,aAAOE,QAAI,MAAID,GAAE,OAAOC,KAAE,CAAC,GAAED;AAAA,IAAC,CAAC,EAAE,GAAEE,SAAE,eAAAP,SAAE,OAAK,EAAC,UAASE,IAAE,MAAKD,GAAE,MAAK,MAAKA,GAAE,MAAK,OAAMA,GAAE,MAAK,IAAG,CAACC,IAAED,GAAE,MAAKA,GAAE,MAAKA,GAAE,KAAK,CAAC;AAAE,WAAO,eAAAO,QAAE,cAAchB,IAAE,UAAS,EAAC,OAAMe,GAAC,GAAEN,GAAE,QAAQ;AAAA,EAAC,GAAE,CAACJ,GAAC,CAAC,CAAC;AAAC;AAAC,IAAIY,KAAE;AAAI,SAASC,GAAEf,IAAEE,KAAE;AAAC,MAAIc,MAAE,EAAE,GAAE,EAAC,IAAGV,KAAE,0BAA0BU,GAAC,IAAG,GAAGT,GAAC,IAAEP,IAAEY,KAAEb,IAAE,GAAEU,MAAEQ,GAAEf,GAAC;AAAE,IAAE,MAAIU,GAAE,SAASN,EAAC,GAAE,CAACA,IAAEM,GAAE,QAAQ,CAAC;AAAE,MAAIJ,MAAE,EAAC,KAAIC,KAAE,GAAGG,GAAE,OAAM,IAAGN,GAAC;AAAE,SAAO,EAAE,EAAC,UAASE,KAAE,YAAWD,IAAE,MAAKK,GAAE,QAAM,CAAC,GAAE,YAAWE,IAAE,MAAKF,GAAE,QAAM,cAAa,CAAC;AAAC;AAAC,IAAIM,KAAE,EAAEH,EAAC;AAAT,IAAW,IAAE,OAAO,OAAOG,IAAE,CAAC,CAAC;;;ArBAiT,IAAI,MAAI,CAAAC,QAAIA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,KAAI,MAAI,CAAC,CAAC;AAAhE,IAAkE,MAAI,CAAAC,QAAIA,GAAEA,GAAE,aAAW,CAAC,IAAE,cAAaA,KAAI,MAAI,CAAC,CAAC;AAAE,IAAI,KAAG,EAAC,CAAC,CAAC,EAAEC,KAAED,IAAE;AAAC,SAAOC,IAAE,YAAUD,GAAE,KAAGC,MAAE,EAAC,GAAGA,KAAE,SAAQD,GAAE,GAAE;AAAC,EAAC;AAA/D,IAAiEE,SAAE,eAAAC,eAAG,IAAI;AAAED,GAAE,cAAY;AAAgB,SAASE,GAAEH,KAAE;AAAC,MAAID,SAAE,eAAAK,YAAEH,EAAC;AAAE,MAAGF,OAAI,MAAK;AAAC,QAAID,KAAE,IAAI,MAAM,IAAIE,GAAC,+CAA+C;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBF,IAAEK,EAAC,GAAEL;AAAA,EAAC;AAAC,SAAOC;AAAC;AAAC,SAAS,GAAGC,KAAED,IAAED,KAAE,MAAI,CAAC,SAAS,IAAI,GAAE;AAAC,EAAAO,GAAGL,KAAED,IAAE,CAAAO,OAAG;AAAC,QAAIC;AAAE,WAAM,EAAC,YAAW,CAAC,IAAIA,KAAED,GAAE,eAAa,OAAKC,KAAE,CAAC,GAAET,EAAC,EAAC;AAAA,EAAC,CAAC;AAAC;AAAC,SAAS,GAAGE,KAAED,IAAE;AAAC,SAAO,EAAEA,GAAE,MAAK,IAAGC,KAAED,EAAC;AAAC;AAAC,IAAI,KAAG;AAAP,IAAa,KAAGS,GAAE,iBAAeA,GAAE;AAAO,SAAS,GAAGR,KAAED,IAAE;AAAC,MAAID,KAAE,EAAE,GAAE,EAAC,IAAGQ,KAAE,qBAAqBR,EAAC,IAAG,MAAKS,IAAE,SAAQE,KAAE,cAAaC,KAAE,MAAKC,MAAE,UAAS,YAAWC,KAAE,OAAG,GAAGC,IAAC,IAAEb,KAAE,CAACc,IAAEC,GAAC,QAAE,eAAAC,UAAG,CAAC,GAAEC,SAAE,eAAAC,QAAE,KAAE;AAAE,EAAAP,MAAE,WAAU;AAAC,WAAOA,QAAI,YAAUA,QAAI,gBAAcA,OAAGM,GAAE,YAAUA,GAAE,UAAQ,MAAG,QAAQ,KAAK,iBAAiBN,GAAC,0GAA0G,IAAG;AAAA,EAAS,EAAE;AAAE,MAAIQ,KAAEC,GAAG;AAAE,EAAAb,OAAI,UAAQY,OAAI,SAAOZ,MAAGY,KAAEE,GAAE,UAAQA,GAAE;AAAM,MAAIC,SAAE,eAAAJ,QAAE,IAAI,GAAEK,MAAGC,GAAEF,IAAEvB,EAAC,GAAE0B,KAAElB,GAAGe,EAAC,GAAE,IAAEtB,IAAE,eAAe,MAAM,KAAGmB,OAAI,MAAKO,KAAE1B,IAAE,eAAe,SAAS;AAAE,MAAG,CAAC,KAAG,CAAC0B,GAAE,OAAM,IAAI,MAAM,gFAAgF;AAAE,MAAG,CAAC,EAAE,OAAM,IAAI,MAAM,4EAA4E;AAAE,MAAG,CAACA,GAAE,OAAM,IAAI,MAAM,4EAA4E;AAAE,MAAG,OAAOnB,MAAG,UAAU,OAAM,IAAI,MAAM,8FAA8FA,EAAC,EAAE;AAAE,MAAG,OAAOE,OAAG,WAAW,OAAM,IAAI,MAAM,kGAAkGA,GAAC,EAAE;AAAE,MAAIJ,KAAEE,KAAE,IAAE,GAAE,CAACoB,IAAEC,GAAE,QAAE,eAAAC,YAAG,IAAG,EAAC,SAAQ,MAAK,eAAc,MAAK,cAAS,eAAAC,WAAG,EAAC,CAAC,GAAEC,KAAE/B,GAAE,MAAIS,IAAE,KAAE,CAAC,GAAEuB,KAAEhC,GAAE,CAAAiC,QAAGL,IAAG,EAAC,MAAK,GAAE,IAAGK,IAAC,CAAC,CAAC,GAAEC,MAAEzB,GAAG,IAAEG,KAAE,QAAGP,OAAI,IAAE,OAAG8B,KAAErB,KAAE,GAAEsB,SAAE,eAAAhC,YAAEH,EAAC,MAAI,MAAK,CAACoC,KAAGC,GAAE,IAAEf,IAAG,GAAEgB,MAAG,EAAC,IAAI,UAAS;AAAC,QAAIN;AAAE,YAAOA,MAAEN,GAAE,SAAS,YAAU,OAAKM,MAAEX,GAAE;AAAA,EAAO,EAAC,GAAE,EAAC,mBAAkBkB,IAAE,iBAAgBC,IAAE,cAAaC,IAAE,IAAEC,GAAG,EAAC,SAAQN,KAAG,mBAAkB,CAACE,GAAE,EAAC,CAAC,GAAEK,MAAGT,KAAE,WAAS,QAAOU,KAAE1B,OAAI,QAAMA,KAAEE,GAAE,aAAWA,GAAE,UAAQ,OAAGyB,MAAI,uBAAIV,MAAGS,KAAE,QAAGX,KAAG,GAAEa,UAAG,eAAAC,aAAE,MAAI;AAAC,QAAIf,KAAEgB;AAAE,YAAOA,MAAE,MAAM,MAAMhB,MAAER,MAAG,OAAK,SAAOA,GAAE,iBAAiB,UAAU,MAAI,OAAKQ,MAAE,CAAC,CAAC,EAAE,KAAK,CAAAZ,QAAGA,IAAE,OAAK,2BAAyB,QAAGA,IAAE,SAASoB,GAAE,OAAO,KAAGpB,eAAa,WAAW,MAAI,OAAK4B,MAAE;AAAA,EAAI,GAAE,CAACR,EAAC,CAAC;AAAE,IAAEM,KAAGD,GAAE;AAAE,MAAII,MAAI,uBAAIf,KAAE,OAAGD,KAAG,GAAEiB,UAAG,eAAAH,aAAE,MAAI;AAAC,QAAIf,KAAEgB;AAAE,YAAOA,MAAE,MAAM,MAAMhB,MAAER,MAAG,OAAK,SAAOA,GAAE,iBAAiB,0BAA0B,MAAI,OAAKQ,MAAE,CAAC,CAAC,EAAE,KAAK,CAAAZ,QAAGA,IAAE,SAASoB,GAAE,OAAO,KAAGpB,eAAa,WAAW,MAAI,OAAK4B,MAAE;AAAA,EAAI,GAAE,CAACR,EAAC,CAAC;AAAE,IAAEU,KAAGD,GAAE;AAAE,MAAIE,MAAI,uBAAI,EAAE,CAAClB,OAAGC,KAAI;AAAE,EAAAX,GAAGgB,IAAE,CAAAP,QAAG;AAAC,IAAAA,IAAE,eAAe,GAAEF,GAAE;AAAA,EAAC,GAAEqB,GAAE;AAAE,MAAIC,MAAI,uBAAI,EAAElB,MAAG9B,OAAI,IAAI;AAAE,EAAAc,GAAGM,MAAG,OAAK,SAAOA,GAAE,aAAY,WAAU,CAAAQ,QAAG;AAAC,IAAAoB,QAAKpB,IAAE,oBAAkBA,IAAE,QAAMjC,IAAG,WAASiC,IAAE,eAAe,GAAEA,IAAE,gBAAgB,GAAEF,GAAE;AAAA,EAAG,CAAC;AAAE,MAAIuB,MAAI,uBAAI,EAAET,MAAGxC,OAAI,KAAG+B,KAAI;AAAE,KAAGX,IAAE6B,KAAGd,EAAC,OAAE,eAAAe,WAAE,MAAI;AAAC,QAAGlD,OAAI,KAAG,CAACiB,GAAE,QAAQ;AAAO,QAAIW,MAAE,IAAI,eAAe,CAAAgB,QAAG;AAAC,eAAQ5B,OAAK4B,KAAE;AAAC,YAAIO,MAAEnC,IAAE,OAAO,sBAAsB;AAAE,QAAAmC,IAAE,MAAI,KAAGA,IAAE,MAAI,KAAGA,IAAE,UAAQ,KAAGA,IAAE,WAAS,KAAGzB,GAAE;AAAA,MAAC;AAAA,IAAC,CAAC;AAAE,WAAOE,IAAE,QAAQX,GAAE,OAAO,GAAE,MAAIW,IAAE,WAAW;AAAA,EAAC,GAAE,CAAC5B,IAAEiB,IAAES,EAAC,CAAC;AAAE,MAAG,CAAC0B,KAAGC,GAAE,IAAElB,GAAG,GAAEmB,UAAG,eAAAnC,SAAE,MAAI,CAAC,EAAC,aAAYnB,IAAE,OAAM0B,IAAE,YAAWC,GAAC,GAAEL,EAAC,GAAE,CAACtB,IAAEsB,IAAEI,IAAEC,EAAC,CAAC,GAAE4B,SAAE,eAAApC,SAAE,OAAK,EAAC,MAAKnB,OAAI,EAAC,IAAG,CAACA,EAAC,CAAC,GAAEwD,MAAG,EAAC,KAAItC,KAAG,IAAGjB,IAAE,MAAKK,KAAE,cAAaN,OAAI,IAAE,OAAG,QAAO,mBAAkBsB,GAAE,SAAQ,oBAAmB8B,IAAE;AAAE,SAAO,eAAArC,QAAE,cAAcjB,IAAG,EAAC,MAAK,UAAS,SAAQE,OAAI,GAAE,SAAQiB,IAAE,UAAStB,GAAE,CAACiC,KAAEgB,QAAI;AAAC,IAAAA,QAAI,YAAU,EAAEhB,KAAE,EAAC,CAACvB,IAAE,GAAG,GAAE,MAAIK,IAAE,CAAAM,QAAGA,MAAE,CAAC,GAAE,CAACX,IAAE,MAAM,GAAE,MAAIK,IAAE,CAAAM,QAAGA,MAAE,CAAC,EAAC,CAAC;AAAA,EAAC,CAAC,EAAC,GAAE,eAAAD,QAAE,cAAcX,IAAE,EAAC,OAAM,KAAE,GAAE,eAAAW,QAAE,cAAc,IAAE,MAAK,eAAAA,QAAE,cAAcnB,GAAE,UAAS,EAAC,OAAM0D,IAAE,GAAE,eAAAvC,QAAE,cAAc,GAAE,OAAM,EAAC,QAAOE,GAAC,GAAE,eAAAF,QAAE,cAAcX,IAAE,EAAC,OAAM,MAAE,GAAE,eAAAW,QAAE,cAAcsC,KAAG,EAAC,MAAKE,IAAE,MAAK,qBAAoB,GAAE,eAAAxC,QAAE,cAAc+B,KAAE,EAAC,cAAazC,KAAE,YAAW8B,IAAE,UAASN,MAAE,EAAEU,KAAG,EAAC,QAAOO,IAAE,SAAS,cAAa,MAAKA,IAAE,SAAS,MAAI,CAACA,IAAE,SAAS,UAAS,CAAC,IAAEA,IAAE,SAAS,KAAI,GAAE,eAAA/B,QAAE,cAAckB,KAAG,MAAK,EAAE,EAAC,UAASuB,KAAG,YAAWhD,KAAE,MAAK+C,IAAE,YAAW,IAAG,UAAS,IAAG,SAAQvD,OAAI,GAAE,MAAK,SAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAE,eAAAe,QAAE,cAAcsB,KAAG,IAAI,CAAC;AAAC;AAAC,IAAIoB,MAAG;AAAM,SAASC,IAAG/D,KAAED,IAAE;AAAC,MAAID,KAAE,EAAE,GAAE,EAAC,IAAGQ,KAAE,6BAA6BR,EAAC,IAAG,GAAGS,GAAC,IAAEP,KAAE,CAAC,EAAC,aAAYS,KAAE,OAAMC,IAAC,CAAC,IAAEP,GAAE,gBAAgB,GAAEQ,MAAEa,GAAEzB,EAAC,GAAEa,KAAEZ,GAAE,CAAAe,QAAG;AAAC,QAAGA,IAAE,WAASA,IAAE,eAAc;AAAC,UAAGjB,GAAGiB,IAAE,aAAa,EAAE,QAAOA,IAAE,eAAe;AAAE,MAAAA,IAAE,eAAe,GAAEA,IAAE,gBAAgB,GAAEL,IAAE;AAAA,IAAC;AAAA,EAAC,CAAC,GAAEG,UAAE,eAAAW,SAAE,OAAK,EAAC,MAAKf,QAAI,EAAC,IAAG,CAACA,GAAC,CAAC;AAAE,SAAO,EAAE,EAAC,UAAS,EAAC,KAAIE,KAAE,IAAGL,IAAE,eAAc,MAAG,SAAQM,GAAC,GAAE,YAAWL,IAAE,MAAKM,KAAE,YAAWiD,KAAG,MAAK,iBAAgB,CAAC;AAAC;AAAC,IAAI,KAAG;AAAM,SAAS,GAAG9D,KAAED,IAAE;AAAC,MAAID,KAAE,EAAE,GAAE,EAAC,IAAGQ,KAAE,8BAA8BR,EAAC,IAAG,GAAGS,GAAC,IAAEP,KAAE,CAAC,EAAC,aAAYS,IAAC,GAAEC,GAAC,IAAEP,GAAE,iBAAiB,GAAEQ,MAAEa,GAAEzB,EAAC;AAAE,qBAAAwD,WAAE,MAAI;AAAC,QAAG7C,IAAE,SAAS,YAAU,KAAK,OAAM,IAAI,MAAM,6FAA6F;AAAA,EAAC,GAAE,CAACA,IAAE,QAAQ,CAAC;AAAE,MAAIE,SAAE,eAAAY,SAAE,OAAK,EAAC,MAAKf,QAAI,EAAC,IAAG,CAACA,GAAC,CAAC;AAAE,SAAO,eAAAW,QAAE,cAAcX,IAAE,EAAC,OAAM,KAAE,GAAE,eAAAW,QAAE,cAAc,IAAE,MAAK,EAAE,EAAC,UAAS,EAAC,KAAIT,KAAE,IAAGL,IAAE,eAAc,KAAE,GAAE,YAAWC,IAAE,MAAKK,IAAE,YAAW,IAAG,MAAK,kBAAiB,CAAC,CAAC,CAAC;AAAC;AAAC,IAAI,KAAG;AAAM,SAAS,GAAGZ,KAAED,IAAE;AAAC,MAAID,KAAE,EAAE,GAAE,EAAC,IAAGQ,KAAE,2BAA2BR,EAAC,IAAG,GAAGS,GAAC,IAAEP,KAAE,CAAC,EAAC,aAAYS,IAAC,GAAEC,GAAC,IAAEP,GAAE,cAAc,GAAEQ,MAAEa,GAAEzB,IAAEW,IAAE,QAAQ,GAAEE,SAAE,eAAAY,SAAE,OAAK,EAAC,MAAKf,QAAI,EAAC,IAAG,CAACA,GAAC,CAAC,GAAEI,MAAEb,GAAE,CAAAe,QAAG;AAAC,IAAAA,IAAE,gBAAgB;AAAA,EAAC,CAAC;AAAE,SAAO,EAAE,EAAC,UAAS,EAAC,KAAIJ,KAAE,IAAGL,IAAE,SAAQO,IAAC,GAAE,YAAWN,IAAE,MAAKK,IAAE,YAAW,IAAG,MAAK,eAAc,CAAC;AAAC;AAAC,IAAIoD,MAAG;AAAK,SAASC,IAAGjE,KAAED,IAAE;AAAC,MAAID,KAAE,EAAE,GAAE,EAAC,IAAGQ,KAAE,2BAA2BR,EAAC,IAAG,GAAGS,GAAC,IAAEP,KAAE,CAAC,EAAC,aAAYS,KAAE,YAAWC,IAAC,CAAC,IAAEP,GAAE,cAAc,GAAEQ,MAAEa,GAAEzB,EAAC;AAAE,qBAAAwD,WAAE,OAAK7C,IAAEJ,EAAC,GAAE,MAAII,IAAE,IAAI,IAAG,CAACJ,IAAEI,GAAC,CAAC;AAAE,MAAIE,SAAE,eAAAY,SAAE,OAAK,EAAC,MAAKf,QAAI,EAAC,IAAG,CAACA,GAAC,CAAC;AAAE,SAAO,EAAE,EAAC,UAAS,EAAC,KAAIE,KAAE,IAAGL,GAAC,GAAE,YAAWC,IAAE,MAAKK,IAAE,YAAWoD,KAAG,MAAK,eAAc,CAAC;AAAC;AAAC,IAAIE,MAAG,EAAE,EAAE;AAAX,IAAaC,MAAG,EAAE,EAAE;AAApB,IAAsBC,MAAG,EAAE,EAAE;AAA7B,IAA+BC,MAAG,EAAEN,GAAE;AAAtC,IAAwCO,MAAG,EAAEL,GAAE;AAA/C,IAAiD,KAAG,OAAO,OAAOC,KAAG,EAAC,UAASC,KAAG,OAAMC,KAAG,SAAQC,KAAG,OAAMC,KAAG,aAAY,EAAE,CAAC;;;AsBA58N,IAAAC,iBAAuH;;;ACAjH,IAAAC,iBAAa;AAAnB,IAAIC;AAAuB,IAAIC,MAAGD,MAAE,eAAAE,QAAE,oBAAkB,OAAKF,MAAE,SAASG,IAAE;AAAC,EAAAA,GAAE;AAAC;;;ADAiwB,IAAIC,MAAG,CAAAC,SAAIA,IAAEA,IAAE,OAAK,CAAC,IAAE,QAAOA,IAAEA,IAAE,SAAO,CAAC,IAAE,UAASA,MAAID,MAAG,CAAC,CAAC;AAA9D,IAAgEE,MAAG,CAAAC,SAAIA,IAAEA,IAAE,mBAAiB,CAAC,IAAE,oBAAmBA,IAAEA,IAAE,kBAAgB,CAAC,IAAE,mBAAkBA,IAAEA,IAAE,cAAY,CAAC,IAAE,eAAcA,IAAEA,IAAE,aAAW,CAAC,IAAE,cAAaA,IAAEA,IAAE,YAAU,CAAC,IAAE,aAAYA,IAAEA,IAAE,cAAY,CAAC,IAAE,eAAcA,MAAID,MAAG,CAAC,CAAC;AAAE,IAAIE,KAAE,EAAC,CAAC,CAAC,GAAE,CAAAC,QAAI,EAAC,GAAGA,IAAE,iBAAgB,EAAEA,GAAE,iBAAgB,EAAC,CAAC,CAAC,GAAE,GAAE,CAAC,CAAC,GAAE,EAAC,CAAC,EAAC,IAAG,CAAC,CAAC,GAAE,CAAAA,OAAGA,GAAE,oBAAkB,IAAEA,KAAE,EAAC,GAAGA,IAAE,iBAAgB,EAAC,GAAE,CAAC,CAAC,EAAEA,IAAE;AAAC,SAAOA,GAAE,gBAAc,OAAGA,KAAE,EAAC,GAAGA,IAAE,aAAY,KAAE;AAAC,GAAE,CAAC,CAAC,EAAEA,IAAE;AAAC,SAAOA,GAAE,gBAAc,QAAGA,KAAE,EAAC,GAAGA,IAAE,aAAY,MAAE;AAAC,GAAE,CAAC,CAAC,EAAEA,IAAEC,IAAE;AAAC,SAAOD,GAAE,aAAWC,GAAE,WAASD,KAAE,EAAC,GAAGA,IAAE,UAASC,GAAE,SAAQ;AAAC,GAAE,CAAC,CAAC,EAAED,IAAEC,IAAE;AAAC,SAAOD,GAAE,YAAUC,GAAE,UAAQD,KAAE,EAAC,GAAGA,IAAE,SAAQC,GAAE,QAAO;AAAC,EAAC;AAA7X,IAA+XC,SAAE,eAAAC,eAAE,IAAI;AAAED,GAAE,cAAY;AAAoB,SAASE,GAAEJ,IAAE;AAAC,MAAIC,SAAE,eAAAI,YAAEH,EAAC;AAAE,MAAGD,OAAI,MAAK;AAAC,QAAIL,MAAE,IAAI,MAAM,IAAII,EAAC,mDAAmD;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBJ,KAAEQ,EAAC,GAAER;AAAA,EAAC;AAAC,SAAOK;AAAC;AAAC,IAAIK,SAAE,eAAAH,eAAE,IAAI;AAAEG,GAAE,cAAY;AAAuB,SAASC,GAAEP,IAAE;AAAC,MAAIC,SAAE,eAAAI,YAAEC,EAAC;AAAE,MAAGL,OAAI,MAAK;AAAC,QAAIL,MAAE,IAAI,MAAM,IAAII,EAAC,mDAAmD;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBJ,KAAEW,EAAC,GAAEX;AAAA,EAAC;AAAC,SAAOK;AAAC;AAAC,IAAIO,SAAE,eAAAL,eAAE,IAAI;AAAEK,GAAE,cAAY;AAAyB,SAASC,KAAG;AAAC,aAAO,eAAAJ,YAAEG,EAAC;AAAC;AAAC,SAASE,IAAGV,IAAEC,IAAE;AAAC,SAAO,EAAEA,GAAE,MAAKF,IAAEC,IAAEC,EAAC;AAAC;AAAC,IAAIU,MAAG,eAAAC;AAAE,SAAS,GAAGZ,IAAEC,IAAE;AAAC,MAAG,EAAC,aAAYL,MAAE,OAAG,GAAGiB,GAAC,IAAEb,IAAEc,UAAE,eAAAC,QAAE,IAAI,GAAEC,MAAEC,GAAEhB,IAAEiB,GAAE,CAAAC,QAAG;AAAC,IAAAL,IAAE,UAAQK;AAAA,EAAC,GAAEnB,GAAE,OAAK,UAAQA,GAAE,OAAK,eAAAY,QAAC,CAAC,GAAEd,UAAE,eAAAiB,QAAE,IAAI,GAAEK,UAAE,eAAAL,QAAE,IAAI,GAAEM,UAAE,eAAAC,YAAEZ,KAAG,EAAC,iBAAgBd,MAAE,IAAE,GAAE,aAAY,OAAG,WAAUwB,KAAE,UAAStB,KAAE,UAAS,MAAK,SAAQ,KAAI,CAAC,GAAE,CAAC,EAAC,iBAAgByB,KAAE,UAASC,IAAC,GAAEC,EAAC,IAAEJ,KAAEK,KAAE9B,GAAE,CAAAuB,QAAG;AAAC,IAAAM,GAAE,EAAC,MAAK,EAAC,CAAC;AAAE,QAAIR,KAAErB,GAAEkB,GAAC;AAAE,QAAG,CAACG,MAAG,CAACO,IAAE;AAAO,QAAIG,MAAG,MAAIR,MAAEA,eAAa,cAAYA,MAAEA,IAAE,mBAAmB,cAAYA,IAAE,UAAQF,GAAE,eAAeO,GAAC,IAAEP,GAAE,eAAeO,GAAC,GAAG;AAAE,IAAAG,MAAG,QAAMA,GAAE,MAAM;AAAA,EAAC,CAAC,GAAEC,SAAE,eAAAC,SAAE,OAAK,EAAC,OAAMH,GAAC,IAAG,CAACA,EAAC,CAAC,GAAER,SAAE,eAAAW,SAAE,OAAK,EAAC,MAAKN,QAAI,GAAE,OAAMG,GAAC,IAAG,CAACH,KAAEG,EAAC,CAAC,GAAEI,KAAE,EAAC,KAAId,IAAC;AAAE,SAAO,eAAAe,QAAE,cAAc7B,GAAE,UAAS,EAAC,OAAMmB,IAAC,GAAE,eAAAU,QAAE,cAAczB,GAAE,UAAS,EAAC,OAAMsB,GAAC,GAAE,eAAAG,QAAE,cAAcV,IAAE,EAAC,OAAM,EAAEE,KAAE,EAAC,CAAC,CAAC,GAAEH,GAAE,MAAK,CAAC,CAAC,GAAEA,GAAE,OAAM,CAAC,EAAC,GAAE,EAAE,EAAC,UAASU,IAAE,YAAWjB,IAAE,MAAKK,IAAE,YAAWP,KAAG,MAAK,aAAY,CAAC,CAAC,CAAC,CAAC;AAAC;AAAC,IAAI,KAAG;AAAS,SAAS,GAAGX,IAAEC,IAAE;AAAC,MAAIL,MAAE,EAAE,GAAE,EAAC,IAAGiB,KAAE,gCAAgCjB,GAAC,IAAG,GAAGkB,IAAC,IAAEd,IAAE,CAACgB,KAAElB,GAAC,IAAEM,GAAE,mBAAmB,GAAEgB,MAAEX,GAAE,GAAEY,MAAED,QAAI,OAAK,QAAGA,QAAIJ,IAAE,SAAQO,UAAE,eAAAR,QAAE,IAAI,GAAES,MAAEP,GAAEM,KAAEtB,IAAEoB,MAAE,OAAKL,IAAE,SAAS,GAAES,KAAEtB,GAAE;AAAE,qBAAA6B,WAAE,MAAI;AAAC,QAAG,CAACX,IAAE,QAAOvB,IAAE,EAAC,MAAK,GAAE,UAASe,GAAC,CAAC,GAAE,MAAI;AAAC,MAAAf,IAAE,EAAC,MAAK,GAAE,UAAS,KAAI,CAAC;AAAA,IAAC;AAAA,EAAC,GAAE,CAACe,IAAEf,KAAEuB,GAAC,CAAC;AAAE,MAAIK,KAAE9B,GAAE,CAAA+B,OAAG;AAAC,QAAIM;AAAE,QAAGZ,KAAE;AAAC,UAAGL,IAAE,oBAAkB,EAAE;AAAO,cAAOW,GAAE,KAAI;AAAA,QAAC,KAAK/B,IAAE;AAAA,QAAM,KAAKA,IAAE;AAAM,UAAA+B,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAE7B,IAAE,EAAC,MAAK,EAAC,CAAC,IAAGmC,MAAEjB,IAAE,UAAU,YAAU,QAAMiB,IAAE,MAAM;AAAE;AAAA,MAAK;AAAA,IAAC,MAAM,SAAON,GAAE,KAAI;AAAA,MAAC,KAAK/B,IAAE;AAAA,MAAM,KAAKA,IAAE;AAAM,QAAA+B,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAE7B,IAAE,EAAC,MAAK,EAAC,CAAC;AAAE;AAAA,IAAK;AAAA,EAAC,CAAC,GAAE8B,KAAEhC,GAAE,CAAA+B,OAAG;AAAC,YAAOA,GAAE,KAAI;AAAA,MAAC,KAAK/B,IAAE;AAAM,QAAA+B,GAAE,eAAe;AAAE;AAAA,IAAK;AAAA,EAAC,CAAC,GAAET,KAAEtB,GAAE,CAAA+B,OAAG;AAAC,QAAIM;AAAE,IAAAN,GAAEA,GAAE,aAAa,KAAG3B,GAAE,aAAWqB,OAAGvB,IAAE,EAAC,MAAK,EAAC,CAAC,IAAGmC,MAAEjB,IAAE,UAAU,YAAU,QAAMiB,IAAE,MAAM,KAAGnC,IAAE,EAAC,MAAK,EAAC,CAAC;AAAA,EAAE,CAAC,GAAEgC,SAAE,eAAAD,SAAE,OAAK,EAAC,MAAKb,IAAE,oBAAkB,EAAC,IAAG,CAACA,GAAC,CAAC,GAAEG,MAAED,GAAElB,IAAEuB,GAAC,GAAEN,KAAEI,MAAE,EAAC,KAAIG,KAAE,MAAKL,KAAE,WAAUO,IAAE,SAAQR,GAAC,IAAE,EAAC,KAAIM,KAAE,IAAGX,IAAE,MAAKM,KAAE,iBAAgBH,IAAE,oBAAkB,GAAE,iBAAgBA,IAAE,cAAYA,IAAE,UAAQ,QAAO,WAAUU,IAAE,SAAQE,IAAE,SAAQV,GAAC;AAAE,SAAO,EAAE,EAAC,WAAUO,IAAE,UAASR,IAAE,YAAWH,KAAE,MAAKgB,IAAE,YAAW,IAAG,MAAK,oBAAmB,CAAC;AAAC;AAAC,IAAI,KAAG;AAAP,IAAa,KAAGI,GAAE,iBAAeA,GAAE;AAAO,SAASC,IAAGnC,IAAEC,IAAE;AAAC,MAAIL,MAAE,EAAE,GAAE,EAAC,IAAGiB,KAAE,+BAA+BjB,GAAC,IAAG,GAAGkB,IAAC,IAAEd,IAAE,CAACgB,KAAElB,GAAC,IAAEM,GAAE,kBAAkB,GAAE,EAAC,OAAMgB,IAAC,IAAEb,GAAE,kBAAkB,GAAEc,MAAElB,GAAE,GAAEoB,MAAEN,GAAEhB,IAAEe,IAAE,UAAS,CAAAE,OAAG;AAAC,IAAAM,GAAE,MAAI1B,IAAE,EAAC,MAAKoB,KAAE,IAAE,EAAC,CAAC,CAAC;AAAA,EAAC,CAAC;AAAE,qBAAAc,WAAE,OAAKlC,IAAE,EAAC,MAAK,GAAE,SAAQe,GAAC,CAAC,GAAE,MAAI;AAAC,IAAAf,IAAE,EAAC,MAAK,GAAE,SAAQ,KAAI,CAAC;AAAA,EAAC,IAAG,CAACe,IAAEf,GAAC,CAAC;AAAE,MAAI0B,MAAEL,GAAE,GAAEM,MAAG,MAAID,QAAI,QAAMA,MAAEJ,GAAE,UAAQA,GAAE,OAAKJ,IAAE,oBAAkB,GAAG,GAAEU,SAAE,eAAAG,SAAE,OAAK,EAAC,MAAKb,IAAE,oBAAkB,GAAE,OAAMI,IAAC,IAAG,CAACJ,KAAEI,GAAC,CAAC,GAAEQ,KAAE,EAAC,KAAIL,KAAE,IAAGV,GAAC;AAAE,SAAO,eAAAkB,QAAE,cAAcvB,GAAE,UAAS,EAAC,OAAMQ,IAAE,QAAO,GAAE,EAAE,EAAC,WAAUK,KAAE,UAASO,IAAE,YAAWd,KAAE,MAAKY,IAAE,YAAW,IAAG,UAAS,IAAG,SAAQD,IAAE,MAAK,mBAAkB,CAAC,CAAC;AAAC;AAAC,IAAIW,MAAG,EAAE,EAAE;AAAX,IAAa,KAAG,EAAE,EAAE;AAApB,IAAsB,KAAG,EAAED,GAAE;AAA7B,IAA+BE,MAAG,OAAO,OAAOD,KAAG,EAAC,QAAO,IAAG,OAAM,GAAE,CAAC;;;AEAlgJ,IAAAE,iBAA6J;;;ACA7J,IAAAC,iBAAuB;;;ACAvB,IAAIC,KAAE;AAAuH,SAASC,IAAEC,IAAE;AAAC,MAAIC,IAAEC;AAAE,MAAIC,MAAGF,KAAED,GAAE,cAAY,OAAKC,KAAE,IAAGG,MAAEJ,GAAE,UAAU,IAAE;AAAE,MAAG,EAAEI,eAAa,aAAa,QAAOD;AAAE,MAAIE,MAAE;AAAG,WAAQC,OAAKF,IAAE,iBAAiB,qCAAqC,EAAE,CAAAE,IAAE,OAAO,GAAED,MAAE;AAAG,MAAIE,MAAEF,OAAGH,KAAEE,IAAE,cAAY,OAAKF,KAAE,KAAGC;AAAE,SAAOL,GAAE,KAAKS,GAAC,MAAIA,MAAEA,IAAE,QAAQT,IAAE,EAAE,IAAGS;AAAC;AAAC,SAASC,GAAER,IAAE;AAAC,MAAIG,KAAEH,GAAE,aAAa,YAAY;AAAE,MAAG,OAAOG,MAAG,SAAS,QAAOA,GAAE,KAAK;AAAE,MAAIC,MAAEJ,GAAE,aAAa,iBAAiB;AAAE,MAAGI,KAAE;AAAC,QAAIC,MAAED,IAAE,MAAM,GAAG,EAAE,IAAI,CAAAG,QAAG;AAAC,UAAIN,KAAE,SAAS,eAAeM,GAAC;AAAE,UAAGN,IAAE;AAAC,YAAIC,KAAED,GAAE,aAAa,YAAY;AAAE,eAAO,OAAOC,MAAG,WAASA,GAAE,KAAK,IAAEH,IAAEE,EAAC,EAAE,KAAK;AAAA,MAAC;AAAC,aAAO;AAAA,IAAI,CAAC,EAAE,OAAO,OAAO;AAAE,QAAGI,IAAE,SAAO,EAAE,QAAOA,IAAE,KAAK,IAAI;AAAA,EAAC;AAAC,SAAON,IAAEC,EAAC,EAAE,KAAK;AAAC;;;ADAnoB,SAASS,IAAEC,KAAE;AAAC,MAAIC,UAAE,eAAAC,QAAE,EAAE,GAAEC,SAAE,eAAAD,QAAE,EAAE;AAAE,SAAOE,GAAE,MAAI;AAAC,QAAIC,KAAEL,IAAE;AAAQ,QAAG,CAACK,GAAE,QAAM;AAAG,QAAIC,MAAED,GAAE;AAAU,QAAGJ,IAAE,YAAUK,IAAE,QAAOH,GAAE;AAAQ,QAAII,KAAEC,GAAEH,EAAC,EAAE,KAAK,EAAE,YAAY;AAAE,WAAOJ,IAAE,UAAQK,KAAEH,GAAE,UAAQI,IAAEA;AAAA,EAAC,CAAC;AAAC;;;ADA62C,IAAIE,OAAI,CAAAC,QAAIA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,KAAID,OAAI,CAAC,CAAC;AAAhE,IAAkEE,OAAI,CAAAD,QAAIA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,KAAIC,OAAI,CAAC,CAAC;AAAhI,IAAkIC,OAAI,CAAAF,QAAIA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,KAAIE,OAAI,CAAC,CAAC;AAAlM,IAAoMC,OAAI,CAAAC,QAAIA,GAAEA,GAAE,cAAY,CAAC,IAAE,eAAcA,GAAEA,GAAE,eAAa,CAAC,IAAE,gBAAeA,GAAEA,GAAE,aAAW,CAAC,IAAE,cAAaA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,GAAEA,GAAE,cAAY,CAAC,IAAE,eAAcA,GAAEA,GAAE,iBAAe,CAAC,IAAE,kBAAiBA,GAAEA,GAAE,mBAAiB,CAAC,IAAE,oBAAmBA,GAAEA,GAAE,gBAAc,CAAC,IAAE,iBAAgBA,KAAID,OAAI,CAAC,CAAC;AAAE,SAASE,GAAEC,IAAEC,MAAE,CAAAP,OAAGA,IAAE;AAAC,MAAIA,KAAEM,GAAE,sBAAoB,OAAKA,GAAE,QAAQA,GAAE,iBAAiB,IAAE,MAAKE,KAAEC,GAAGF,IAAED,GAAE,QAAQ,MAAM,CAAC,GAAE,CAAAI,QAAGA,IAAE,QAAQ,QAAQ,OAAO,OAAO,GAAEC,MAAEX,KAAEQ,GAAE,QAAQR,EAAC,IAAE;AAAK,SAAOW,QAAI,OAAKA,MAAE,OAAM,EAAC,SAAQH,IAAE,mBAAkBG,IAAC;AAAC;AAAC,IAAIC,MAAG,EAAC,CAAC,CAAC,EAAEN,IAAE;AAAC,SAAOA,GAAE,QAAQ,QAAQ,YAAUA,GAAE,iBAAe,IAAEA,KAAE,EAAC,GAAGA,IAAE,mBAAkB,MAAK,cAAa,EAAC;AAAC,GAAE,CAAC,CAAC,EAAEA,IAAE;AAAC,MAAGA,GAAE,QAAQ,QAAQ,YAAUA,GAAE,iBAAe,EAAE,QAAOA;AAAE,MAAIC,MAAED,GAAE,mBAAkB,EAAC,YAAWN,GAAC,IAAEM,GAAE,QAAQ,SAAQE,KAAEF,GAAE,QAAQ,UAAU,CAAAK,QAAGX,GAAEW,IAAE,QAAQ,QAAQ,KAAK,CAAC;AAAE,SAAOH,OAAI,OAAKD,MAAEC,KAAG,EAAC,GAAGF,IAAE,cAAa,GAAE,mBAAkBC,IAAC;AAAC,GAAE,CAAC,CAAC,EAAED,IAAEC,KAAE;AAAC,MAAII;AAAE,MAAGL,GAAE,QAAQ,QAAQ,YAAUA,GAAE,iBAAe,EAAE,QAAOA;AAAE,MAAIN,KAAEK,GAAEC,EAAC,GAAEE,KAAEK,GAAGN,KAAE,EAAC,cAAa,MAAIP,GAAE,SAAQ,oBAAmB,MAAIA,GAAE,mBAAkB,WAAU,CAAAU,QAAGA,IAAE,IAAG,iBAAgB,CAAAA,QAAGA,IAAE,QAAQ,QAAQ,SAAQ,CAAC;AAAE,SAAM,EAAC,GAAGJ,IAAE,GAAGN,IAAE,aAAY,IAAG,mBAAkBQ,IAAE,oBAAmBG,MAAEJ,IAAE,YAAU,OAAKI,MAAE,EAAC;AAAC,GAAE,CAAC,CAAC,GAAE,CAACL,IAAEC,QAAI;AAAC,MAAGD,GAAE,QAAQ,QAAQ,YAAUA,GAAE,iBAAe,EAAE,QAAOA;AAAE,MAAIE,KAAEF,GAAE,gBAAc,KAAG,IAAE,GAAEK,MAAEL,GAAE,cAAYC,IAAE,MAAM,YAAY,GAAEO,MAAGR,GAAE,sBAAoB,OAAKA,GAAE,QAAQ,MAAMA,GAAE,oBAAkBE,EAAC,EAAE,OAAOF,GAAE,QAAQ,MAAM,GAAEA,GAAE,oBAAkBE,EAAC,CAAC,IAAEF,GAAE,SAAS,KAAK,CAAAF,OAAG;AAAC,QAAIW;AAAE,WAAM,CAACX,GAAE,QAAQ,QAAQ,cAAYW,KAAEX,GAAE,QAAQ,QAAQ,cAAY,OAAK,SAAOW,GAAE,WAAWJ,GAAC;AAAA,EAAE,CAAC,GAAEK,MAAEF,KAAER,GAAE,QAAQ,QAAQQ,EAAC,IAAE;AAAG,SAAOE,QAAI,MAAIA,QAAIV,GAAE,oBAAkB,EAAC,GAAGA,IAAE,aAAYK,IAAC,IAAE,EAAC,GAAGL,IAAE,aAAYK,KAAE,mBAAkBK,KAAE,mBAAkB,EAAC;AAAC,GAAE,CAAC,CAAC,EAAEV,IAAE;AAAC,SAAOA,GAAE,QAAQ,QAAQ,YAAUA,GAAE,iBAAe,KAAGA,GAAE,gBAAc,KAAGA,KAAE,EAAC,GAAGA,IAAE,aAAY,GAAE;AAAC,GAAE,CAAC,CAAC,GAAE,CAACA,IAAEC,QAAI;AAAC,MAAIP,KAAE,EAAC,IAAGO,IAAE,IAAG,SAAQA,IAAE,QAAO,GAAEC,KAAEH,GAAEC,IAAE,CAAAK,QAAG,CAAC,GAAGA,KAAEX,EAAC,CAAC;AAAE,SAAOM,GAAE,sBAAoB,QAAMA,GAAE,QAAQ,QAAQ,WAAWC,IAAE,QAAQ,QAAQ,KAAK,MAAIC,GAAE,oBAAkBA,GAAE,QAAQ,QAAQR,EAAC,IAAG,EAAC,GAAGM,IAAE,GAAGE,GAAC;AAAC,GAAE,CAAC,CAAC,GAAE,CAACF,IAAEC,QAAI;AAAC,MAAIP,KAAEK,GAAEC,IAAE,CAAAE,OAAG;AAAC,QAAIG,MAAEH,GAAE,UAAU,CAAAE,QAAGA,IAAE,OAAKH,IAAE,EAAE;AAAE,WAAOI,QAAI,MAAIH,GAAE,OAAOG,KAAE,CAAC,GAAEH;AAAA,EAAC,CAAC;AAAE,SAAM,EAAC,GAAGF,IAAE,GAAGN,IAAE,mBAAkB,EAAC;AAAC,GAAE,CAAC,CAAC,GAAE,CAACM,IAAEC,SAAK,EAAC,GAAGD,IAAE,SAAQC,IAAE,GAAE,GAAE;AAAjqD,IAAmqD,QAAE,eAAAU,eAAE,IAAI;AAAE,EAAE,cAAY;AAAwB,SAASC,GAAEZ,IAAE;AAAC,MAAIC,UAAE,eAAAY,YAAG,CAAC;AAAE,MAAGZ,QAAI,MAAK;AAAC,QAAIP,KAAE,IAAI,MAAM,IAAIM,EAAC,gDAAgD;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBN,IAAEkB,EAAC,GAAElB;AAAA,EAAC;AAAC,SAAOO;AAAC;AAAC,IAAIa,SAAE,eAAAH,eAAE,IAAI;AAAEG,GAAE,cAAY;AAAqB,SAASC,GAAEf,IAAE;AAAC,MAAIC,UAAE,eAAAY,YAAGC,EAAC;AAAE,MAAGb,QAAI,MAAK;AAAC,QAAIP,KAAE,IAAI,MAAM,IAAIM,EAAC,gDAAgD;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBN,IAAEqB,EAAC,GAAErB;AAAA,EAAC;AAAC,SAAOO;AAAC;AAAC,SAASe,IAAGhB,IAAEC,KAAE;AAAC,SAAO,EAAEA,IAAE,MAAKK,KAAGN,IAAEC,GAAC;AAAC;AAAC,IAAIgB,MAAG,eAAAC;AAAG,SAASC,IAAGnB,IAAEC,KAAE;AAAC,MAAG,EAAC,OAAMP,IAAE,cAAaQ,IAAE,MAAKG,KAAE,MAAKD,KAAE,UAASI,IAAE,IAAGE,MAAE,CAACU,KAAEC,QAAID,QAAIC,KAAE,UAASvB,KAAE,OAAG,YAAWW,KAAE,OAAG,UAASa,KAAE,OAAG,GAAGC,IAAC,IAAEvB;AAAE,QAAMwB,KAAEf,KAAE,eAAa;AAAW,MAAIgB,MAAEC,GAAEzB,GAAC,GAAE,CAAC0B,KAAEL,KAAE,CAAC,IAAE,QAAOM,EAAC,IAAE,EAAGlC,IAAEc,IAAEN,EAAC,GAAE,CAAC2B,IAAEC,GAAC,QAAE,eAAAC,YAAGf,KAAG,EAAC,aAAQ,eAAAgB,WAAG,GAAE,cAAa,GAAE,SAAQ,CAAC,GAAE,aAAY,IAAG,SAAQ,MAAK,mBAAkB,MAAK,mBAAkB,EAAC,CAAC,GAAEC,SAAE,eAAAC,QAAE,EAAC,QAAO,OAAG,MAAK,MAAE,CAAC,GAAEC,SAAE,eAAAD,QAAE,IAAI,GAAEE,SAAE,eAAAF,QAAE,IAAI,GAAE,QAAE,eAAAA,QAAE,IAAI,GAAE/B,MAAE2B,GAAE,OAAOpB,OAAG,WAAS,CAACU,KAAEC,QAAI;AAAC,QAAIgB,KAAE3B;AAAE,YAAOU,OAAG,OAAK,SAAOA,IAAEiB,EAAC,QAAMhB,OAAG,OAAK,SAAOA,IAAEgB,EAAC;AAAA,EAAE,IAAE3B,GAAC,GAAE4B,SAAE,eAAAC,aAAG,CAAAnB,QAAG,EAAEoB,IAAE,MAAK,EAAC,CAAC,CAAC,GAAE,MAAIb,GAAE,KAAK,CAAAN,QAAGlB,IAAEkB,KAAED,GAAC,CAAC,GAAE,CAAC,CAAC,GAAE,MAAIjB,IAAEwB,IAAEP,GAAC,EAAC,CAAC,GAAE,CAACO,EAAC,CAAC,GAAEa,UAAE,eAAAC,SAAE,OAAK,EAAC,GAAGZ,IAAE,OAAMF,IAAE,UAAS7B,IAAE,MAAKwB,KAAE,IAAE,GAAE,aAAYE,IAAE,SAAQrB,KAAE,YAAWmC,IAAE,iBAAgBL,IAAE,UAASE,IAAE,WAAUC,IAAE,YAAW,EAAC,IAAG,CAACT,IAAE7B,IAAEwB,IAAEO,EAAC,CAAC;AAAE,IAAE,MAAI;AAAC,IAAAA,GAAE,QAAQ,UAAQW;AAAA,EAAC,GAAE,CAACA,GAAC,CAAC,GAAEd,GAAG,CAACc,IAAE,WAAUA,IAAE,UAAU,GAAE,CAACpB,KAAEC,QAAI;AAAC,QAAIgB;AAAE,IAAAP,IAAE,EAAC,MAAK,EAAC,CAAC,GAAE,EAAGT,KAAEQ,GAAG,KAAK,MAAIT,IAAE,eAAe,IAAGiB,KAAEG,IAAE,UAAU,YAAU,QAAMH,GAAE,MAAM;AAAA,EAAE,GAAEG,IAAE,iBAAe,CAAC;AAAE,MAAIE,SAAE,eAAAD,SAAE,OAAK,EAAC,MAAKD,IAAE,iBAAe,GAAE,UAAS1C,IAAE,OAAM6B,GAAC,IAAG,CAACa,KAAE1C,IAAE6B,EAAC,CAAC,GAAEgB,MAAGb,GAAE,CAAAV,QAAG;AAAC,QAAIC,MAAEmB,IAAE,QAAQ,KAAK,CAAAH,OAAGA,GAAE,OAAKjB,GAAC;AAAE,IAAAC,OAAGuB,GAAEvB,IAAE,QAAQ,QAAQ,KAAK;AAAA,EAAC,CAAC,GAAEwB,MAAGf,GAAE,MAAI;AAAC,QAAGU,IAAE,sBAAoB,MAAK;AAAC,UAAG,EAAC,SAAQpB,KAAE,IAAGC,IAAC,IAAEmB,IAAE,QAAQA,IAAE,iBAAiB;AAAE,MAAAI,GAAExB,IAAE,QAAQ,KAAK,GAAEU,IAAE,EAAC,MAAK,GAAE,OAAMT,GAAE,UAAS,IAAGA,IAAC,CAAC;AAAA,IAAC;AAAA,EAAC,CAAC,GAAEyB,MAAGhB,GAAE,MAAIA,IAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAEiB,MAAGjB,GAAE,MAAIA,IAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAEkB,MAAGlB,GAAE,CAACV,KAAEC,KAAEgB,OAAIjB,QAAIC,GAAE,WAASS,IAAE,EAAC,MAAK,GAAE,OAAMT,GAAE,UAAS,IAAGA,KAAE,SAAQgB,GAAC,CAAC,IAAEP,IAAE,EAAC,MAAK,GAAE,OAAMV,KAAE,SAAQiB,GAAC,CAAC,CAAC,GAAEY,MAAGnB,GAAE,CAACV,KAAEC,SAAKS,IAAE,EAAC,MAAK,GAAE,IAAGV,KAAE,SAAQC,IAAC,CAAC,GAAE,MAAIS,IAAE,EAAC,MAAK,GAAE,IAAGV,IAAC,CAAC,EAAE,GAAE8B,MAAGpB,GAAE,CAAAV,SAAIU,IAAE,EAAC,MAAK,GAAE,IAAGV,IAAC,CAAC,GAAE,MAAIU,IAAE,EAAC,MAAK,GAAE,IAAG,KAAI,CAAC,EAAE,GAAEc,KAAEd,GAAE,CAAAV,QAAG,EAAEoB,IAAE,MAAK,EAAC,CAAC,CAAC,IAAG;AAAC,WAAOZ,MAAG,OAAK,SAAOA,GAAER,GAAC;AAAA,EAAC,GAAE,CAAC,CAAC,IAAG;AAAC,QAAIC,MAAEmB,IAAE,MAAM,MAAM,GAAEH,KAAEhB,IAAE,UAAU,CAAA8B,OAAGhD,IAAEgD,IAAE/B,GAAC,CAAC;AAAE,WAAOiB,OAAI,KAAGhB,IAAE,KAAKD,GAAC,IAAEC,IAAE,OAAOgB,IAAE,CAAC,GAAET,MAAG,OAAK,SAAOA,GAAEP,GAAC;AAAA,EAAC,EAAC,CAAC,CAAC,GAAE+B,MAAGtB,GAAE,CAAAV,QAAGU,IAAE,EAAC,MAAK,GAAE,OAAMV,IAAC,CAAC,CAAC,GAAEiC,MAAGvB,GAAE,MAAIA,IAAE,EAAC,MAAK,EAAC,CAAC,CAAC,GAAEwB,UAAG,eAAAb,SAAE,OAAK,EAAC,UAASG,IAAE,gBAAeK,KAAG,eAAcC,KAAG,YAAWF,KAAG,cAAaD,KAAG,aAAYD,KAAG,oBAAmBD,KAAG,cAAaF,KAAG,QAAOS,KAAG,aAAYC,IAAE,IAAG,CAAC,CAAC,GAAEE,MAAG,EAAC,KAAI9B,IAAC,GAAE+B,SAAE,eAAAtB,QAAE,IAAI,GAAEuB,MAAG,EAAE;AAAE,aAAO,eAAAC,WAAG,MAAI;AAAC,IAAAF,GAAE,WAAStD,OAAI,UAAQuD,IAAG,iBAAiBD,GAAE,SAAQ,SAAQ,MAAI;AAAC,MAAA5B,MAAG,QAAMA,GAAE1B,EAAC;AAAA,IAAC,CAAC;AAAA,EAAC,GAAE,CAACsD,IAAE5B,EAAC,CAAC,GAAE,eAAA+B,QAAE,cAAc,EAAE,UAAS,EAAC,OAAML,IAAE,GAAE,eAAAK,QAAE,cAAc7C,GAAE,UAAS,EAAC,OAAM0B,IAAC,GAAE,eAAAmB,QAAE,cAAcvC,IAAG,EAAC,OAAM,EAAEoB,IAAE,cAAa,EAAC,CAAC,CAAC,GAAEA,GAAE,MAAK,CAAC,CAAC,GAAEA,GAAE,OAAM,CAAC,EAAC,GAAEpC,OAAG,QAAMuB,MAAG,QAAM,EAAG,EAAC,CAACvB,GAAC,GAAEuB,GAAC,CAAC,EAAE,IAAI,CAAC,CAACP,KAAEC,GAAC,GAAEgB,OAAI,eAAAsB,QAAE,cAAcjD,IAAG,EAAC,UAASU,GAAG,QAAO,KAAIiB,OAAI,IAAE,CAAAc,OAAG;AAAC,QAAIS;AAAE,IAAAJ,GAAE,WAASI,KAAET,MAAG,OAAK,SAAOA,GAAE,QAAQ,MAAM,MAAI,OAAKS,KAAE;AAAA,EAAI,IAAE,QAAO,GAAG,EAAG,EAAC,KAAIxC,KAAE,IAAG,SAAQ,MAAK,UAAS,QAAO,MAAG,UAAS,MAAG,MAAKf,KAAE,UAASP,IAAE,MAAKsB,KAAE,OAAMC,IAAC,CAAC,EAAC,CAAC,CAAC,GAAE,EAAE,EAAC,UAASkC,KAAG,YAAWhC,KAAE,MAAKmB,IAAE,YAAWzB,KAAG,MAAK,UAAS,CAAC,CAAC,CAAC,CAAC;AAAC;AAAC,IAAI4C,MAAG;AAAS,SAASC,IAAG9D,IAAEC,KAAE;AAAC,MAAI2B;AAAE,MAAIlC,KAAE,EAAE,GAAE,EAAC,IAAGQ,KAAE,6BAA6BR,EAAC,IAAG,GAAGW,IAAC,IAAEL,IAAEI,MAAEW,GAAE,gBAAgB,GAAEP,KAAEI,GAAE,gBAAgB,GAAEF,MAAEgB,GAAEtB,IAAE,WAAUH,GAAC,GAAEH,KAAE,EAAE,GAAEW,KAAEqB,GAAE,CAAAD,OAAG;AAAC,YAAOA,GAAE,KAAI;AAAA,MAAC,KAAKC,IAAE;AAAA,MAAM,KAAKA,IAAE;AAAA,MAAM,KAAKA,IAAE;AAAU,QAAAD,GAAE,eAAe,GAAErB,GAAE,YAAY,GAAEV,GAAE,UAAU,MAAI;AAAC,UAAAM,IAAE,SAAOI,GAAE,WAAWa,GAAE,KAAK;AAAA,QAAC,CAAC;AAAE;AAAA,MAAM,KAAKS,IAAE;AAAQ,QAAAD,GAAE,eAAe,GAAErB,GAAE,YAAY,GAAEV,GAAE,UAAU,MAAI;AAAC,UAAAM,IAAE,SAAOI,GAAE,WAAWa,GAAE,IAAI;AAAA,QAAC,CAAC;AAAE;AAAA,IAAK;AAAA,EAAC,CAAC,GAAEC,KAAEQ,GAAE,CAAAD,OAAG;AAAC,YAAOA,GAAE,KAAI;AAAA,MAAC,KAAKC,IAAE;AAAM,QAAAD,GAAE,eAAe;AAAE;AAAA,IAAK;AAAA,EAAC,CAAC,GAAEN,MAAEO,GAAE,CAAAD,OAAG;AAAC,QAAG3B,GAAG2B,GAAE,aAAa,EAAE,QAAOA,GAAE,eAAe;AAAE,IAAAzB,IAAE,iBAAe,KAAGI,GAAE,aAAa,GAAEV,GAAE,UAAU,MAAI;AAAC,UAAIgC;AAAE,cAAOA,MAAE1B,IAAE,UAAU,YAAU,OAAK,SAAO0B,IAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,IAAC,CAAC,MAAID,GAAE,eAAe,GAAErB,GAAE,YAAY;AAAA,EAAE,CAAC,GAAEgB,KAAE1B,GAAG,MAAI;AAAC,QAAGM,IAAE,QAAQ,QAAM,CAACA,IAAE,SAAQF,EAAC,EAAE,KAAK,GAAG;AAAA,EAAC,GAAE,CAACE,IAAE,SAAQF,EAAC,CAAC,GAAEuB,UAAE,eAAAgB,SAAE,OAAK,EAAC,MAAKrC,IAAE,iBAAe,GAAE,UAASA,IAAE,UAAS,OAAMA,IAAE,MAAK,IAAG,CAACA,GAAC,CAAC,GAAEuB,KAAE,EAAC,KAAIjB,KAAE,IAAGR,IAAE,MAAK2B,GAAG7B,IAAEI,IAAE,SAAS,GAAE,iBAAgB,WAAU,kBAAiBwB,KAAExB,IAAE,WAAW,YAAU,OAAK,SAAOwB,GAAE,IAAG,iBAAgBxB,IAAE,iBAAe,GAAE,mBAAkBoB,IAAE,UAASpB,IAAE,UAAS,WAAUK,IAAE,SAAQa,IAAE,SAAQC,IAAC;AAAE,SAAO,EAAE,EAAC,UAASI,IAAE,YAAWtB,KAAE,MAAKoB,KAAE,YAAWoC,KAAG,MAAK,iBAAgB,CAAC;AAAC;AAAC,IAAIE,MAAG;AAAQ,SAASC,IAAGhE,IAAEC,KAAE;AAAC,MAAIP,KAAE,EAAE,GAAE,EAAC,IAAGQ,KAAE,4BAA4BR,EAAC,IAAG,GAAGW,IAAC,IAAEL,IAAEI,MAAEW,GAAE,eAAe,GAAEP,KAAEI,GAAE,eAAe,GAAEF,MAAEgB,GAAEtB,IAAE,UAASH,GAAC;AAAE,IAAE,MAAIO,GAAE,cAAcN,EAAC,GAAE,CAACA,EAAC,CAAC;AAAE,MAAIJ,KAAEgC,GAAE,MAAI;AAAC,QAAIP;AAAE,YAAOA,MAAEnB,IAAE,UAAU,YAAU,OAAK,SAAOmB,IAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,EAAC,CAAC,GAAEd,SAAE,eAAAgC,SAAE,OAAK,EAAC,MAAKrC,IAAE,iBAAe,GAAE,UAASA,IAAE,SAAQ,IAAG,CAACA,GAAC,CAAC;AAAE,SAAO,EAAE,EAAC,UAAS,EAAC,KAAIM,KAAE,IAAGR,IAAE,SAAQJ,GAAC,GAAE,YAAWO,KAAE,MAAKI,IAAE,YAAWsD,KAAG,MAAK,gBAAe,CAAC;AAAC;AAAC,IAAIE,MAAG;AAAP,IAAYC,MAAG7B,GAAG,iBAAeA,GAAG;AAAO,SAAS8B,IAAGnE,IAAEC,KAAE;AAAC,MAAI4B;AAAE,MAAInC,KAAE,EAAE,GAAE,EAAC,IAAGQ,KAAE,8BAA8BR,EAAC,IAAG,GAAGW,IAAC,IAAEL,IAAEI,MAAEW,GAAE,iBAAiB,GAAEP,KAAEI,GAAE,iBAAiB,GAAEF,MAAEgB,GAAEtB,IAAE,YAAWH,GAAC,GAAEH,KAAE,EAAE,GAAEW,KAAE,EAAE,GAAEa,KAAEZ,GAAG,GAAEa,OAAG,MAAID,OAAI,QAAMA,KAAEkB,GAAE,UAAQA,GAAE,OAAKpC,IAAE,iBAAe,GAAG;AAAE,qBAAAsD,WAAG,MAAI;AAAC,QAAIzB;AAAE,QAAIH,MAAE1B,IAAE,WAAW;AAAQ,IAAA0B,OAAG1B,IAAE,iBAAe,KAAG0B,UAAMG,KAAEH,GAAGA,GAAC,MAAI,OAAK,SAAOG,GAAE,kBAAgBH,IAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,EAAC,GAAE,CAAC1B,IAAE,cAAaA,IAAE,UAAU,CAAC;AAAE,MAAIoB,KAAEM,GAAE,CAAAA,QAAG;AAAC,YAAOrB,GAAE,QAAQ,GAAEqB,IAAE,KAAI;AAAA,MAAC,KAAKA,IAAE;AAAM,YAAG1B,IAAE,gBAAc,GAAG,QAAO0B,IAAE,eAAe,GAAEA,IAAE,gBAAgB,GAAEtB,GAAE,OAAOsB,IAAE,GAAG;AAAA,MAAE,KAAKA,IAAE;AAAM,YAAGA,IAAE,eAAe,GAAEA,IAAE,gBAAgB,GAAE1B,IAAE,sBAAoB,MAAK;AAAC,cAAG,EAAC,SAAQ6B,GAAC,IAAE7B,IAAE,QAAQA,IAAE,iBAAiB;AAAE,UAAAI,GAAE,SAASyB,GAAE,QAAQ,KAAK;AAAA,QAAC;AAAC,QAAA7B,IAAE,SAAO,MAAII,GAAE,aAAa,GAAEsB,GAAE,EAAE,UAAU,MAAI;AAAC,cAAIG;AAAE,kBAAOA,KAAE7B,IAAE,UAAU,YAAU,OAAK,SAAO6B,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,QAAC,CAAC;AAAG;AAAA,MAAM,KAAK,EAAE7B,IAAE,aAAY,EAAC,UAAS0B,IAAE,WAAU,YAAWA,IAAE,WAAU,CAAC;AAAE,eAAOA,IAAE,eAAe,GAAEA,IAAE,gBAAgB,GAAEtB,GAAE,WAAWa,GAAE,IAAI;AAAA,MAAE,KAAK,EAAEjB,IAAE,aAAY,EAAC,UAAS0B,IAAE,SAAQ,YAAWA,IAAE,UAAS,CAAC;AAAE,eAAOA,IAAE,eAAe,GAAEA,IAAE,gBAAgB,GAAEtB,GAAE,WAAWa,GAAE,QAAQ;AAAA,MAAE,KAAKS,IAAE;AAAA,MAAK,KAAKA,IAAE;AAAO,eAAOA,IAAE,eAAe,GAAEA,IAAE,gBAAgB,GAAEtB,GAAE,WAAWa,GAAE,KAAK;AAAA,MAAE,KAAKS,IAAE;AAAA,MAAI,KAAKA,IAAE;AAAS,eAAOA,IAAE,eAAe,GAAEA,IAAE,gBAAgB,GAAEtB,GAAE,WAAWa,GAAE,IAAI;AAAA,MAAE,KAAKS,IAAE;AAAO,eAAOA,IAAE,eAAe,GAAEA,IAAE,gBAAgB,GAAEtB,GAAE,aAAa,GAAEV,GAAE,UAAU,MAAI;AAAC,cAAImC;AAAE,kBAAOA,KAAE7B,IAAE,UAAU,YAAU,OAAK,SAAO6B,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,QAAC,CAAC;AAAA,MAAE,KAAKH,IAAE;AAAI,QAAAA,IAAE,eAAe,GAAEA,IAAE,gBAAgB;AAAE;AAAA,MAAM;AAAQ,QAAAA,IAAE,IAAI,WAAS,MAAItB,GAAE,OAAOsB,IAAE,GAAG,GAAErB,GAAE,WAAW,MAAID,GAAE,YAAY,GAAE,GAAG;AAAG;AAAA,IAAK;AAAA,EAAC,CAAC,GAAEiB,MAAE3B,GAAG,MAAI;AAAC,QAAIgC;AAAE,YAAOA,MAAE1B,IAAE,UAAU,YAAU,OAAK,SAAO0B,IAAE;AAAA,EAAE,GAAE,CAAC1B,IAAE,UAAU,OAAO,CAAC,GAAEuB,SAAE,eAAAc,SAAE,OAAK,EAAC,MAAKrC,IAAE,iBAAe,EAAC,IAAG,CAACA,GAAC,CAAC,GAAEwB,KAAE,EAAC,yBAAwBxB,IAAE,sBAAoB,SAAOyB,KAAEzB,IAAE,QAAQA,IAAE,iBAAiB,MAAI,OAAK,SAAOyB,GAAE,IAAG,wBAAuBzB,IAAE,SAAO,IAAE,OAAG,QAAO,mBAAkBqB,KAAE,oBAAmBrB,IAAE,aAAY,IAAGF,IAAE,WAAUsB,IAAE,MAAK,WAAU,UAAS,GAAE,KAAId,IAAC;AAAE,SAAO,EAAE,EAAC,UAASkB,IAAE,YAAWvB,KAAE,MAAKsB,IAAE,YAAWsC,KAAG,UAASC,KAAG,SAAQ3C,KAAE,MAAK,kBAAiB,CAAC;AAAC;AAAC,IAAI6C,MAAG;AAAK,SAASC,IAAGrE,IAAEC,KAAE;AAAC,MAAIP,KAAE,EAAE,GAAE,EAAC,IAAGQ,KAAE,6BAA6BR,EAAC,IAAG,UAASW,MAAE,OAAG,OAAMD,KAAE,GAAGI,GAAC,IAAER,IAAEU,MAAEK,GAAE,gBAAgB,GAAEjB,KAAEc,GAAE,gBAAgB,GAAEH,KAAEC,IAAE,sBAAoB,OAAKA,IAAE,QAAQA,IAAE,iBAAiB,EAAE,OAAKR,KAAE,OAAGoB,KAAEZ,IAAE,WAAWN,GAAC,GAAEmB,UAAE,eAAAW,QAAE,IAAI,GAAEV,KAAEJ,IAAGG,GAAC,GAAEE,MAAEL,GAAG,EAAC,UAASf,KAAE,OAAMD,KAAE,QAAOmB,KAAE,IAAI,YAAW;AAAC,WAAOC,GAAE;AAAA,EAAC,EAAC,CAAC,GAAEG,KAAED,GAAEzB,KAAEsB,GAAC;AAAE,IAAE,MAAI;AAAC,QAAGb,IAAE,iBAAe,KAAG,CAACD,MAAGC,IAAE,sBAAoB,EAAE;AAAO,QAAI4B,KAAER,GAAE;AAAE,WAAOQ,GAAE,sBAAsB,MAAI;AAAC,UAAIE,KAAEE;AAAE,OAACA,MAAGF,MAAEjB,IAAE,YAAU,OAAK,SAAOiB,IAAE,mBAAiB,QAAME,GAAE,KAAKF,KAAE,EAAC,OAAM,UAAS,CAAC;AAAA,IAAC,CAAC,GAAEF,GAAE;AAAA,EAAO,GAAE,CAACf,KAAEd,IAAEC,IAAE,cAAaA,IAAE,mBAAkBA,IAAE,iBAAiB,CAAC,GAAE,EAAE,MAAIZ,GAAE,eAAeI,IAAEuB,GAAC,GAAE,CAACA,KAAEvB,EAAC,CAAC;AAAE,MAAI0B,KAAEE,GAAE,CAAAQ,OAAG;AAAC,QAAGjC,IAAE,QAAOiC,GAAE,eAAe;AAAE,IAAAxC,GAAE,SAASM,GAAC,GAAEM,IAAE,SAAO,MAAIZ,GAAE,aAAa,GAAEgC,GAAE,EAAE,UAAU,MAAI;AAAC,UAAIU;AAAE,cAAOA,MAAE9B,IAAE,UAAU,YAAU,OAAK,SAAO8B,IAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,IAAC,CAAC;AAAA,EAAE,CAAC,GAAEX,KAAEC,GAAE,MAAI;AAAC,QAAGzB,IAAE,QAAOP,GAAE,WAAWuB,GAAE,OAAO;AAAE,IAAAvB,GAAE,WAAWuB,GAAE,UAASnB,EAAC;AAAA,EAAC,CAAC,GAAE4B,MAAEpB,GAAG,GAAEuB,KAAEH,GAAE,CAAAQ,OAAGR,IAAE,OAAOQ,EAAC,CAAC,GAAEH,KAAEL,GAAE,CAAAQ,OAAG;AAAC,IAAAR,IAAE,SAASQ,EAAC,MAAIjC,OAAGI,MAAGX,GAAE,WAAWuB,GAAE,UAASnB,IAAE,CAAC;AAAA,EAAE,CAAC,GAAEkC,KAAEN,GAAE,CAAAQ,OAAG;AAAC,IAAAR,IAAE,SAASQ,EAAC,MAAIjC,OAAGI,MAAGX,GAAE,WAAWuB,GAAE,OAAO;AAAA,EAAE,CAAC,GAAE,QAAE,eAAAoB,SAAE,OAAK,EAAC,QAAOhC,IAAE,UAASa,IAAE,UAASjB,IAAC,IAAG,CAACI,IAAEa,IAAEjB,GAAC,CAAC;AAAE,SAAO,EAAE,EAAC,UAAS,EAAC,IAAGH,IAAE,KAAIyB,IAAE,MAAK,UAAS,UAAStB,QAAI,OAAG,SAAO,IAAG,iBAAgBA,QAAI,OAAG,OAAG,QAAO,iBAAgBiB,IAAE,UAAS,QAAO,SAAQM,IAAE,SAAQC,IAAE,gBAAeI,IAAE,cAAaA,IAAE,eAAcE,IAAE,aAAYA,IAAE,gBAAeC,IAAE,cAAaA,GAAC,GAAE,YAAW5B,IAAE,MAAK,GAAE,YAAW4D,KAAG,MAAK,iBAAgB,CAAC;AAAC;AAAC,IAAIE,MAAG,EAAEnD,GAAE;AAAX,IAAaoD,MAAG,EAAET,GAAE;AAApB,IAAsBU,MAAG,EAAER,GAAE;AAA7B,IAA+BS,MAAG,EAAEN,GAAE;AAAtC,IAAwCO,MAAG,EAAEL,GAAE;AAA/C,IAAiD,KAAG,OAAO,OAAOC,KAAG,EAAC,QAAOC,KAAG,OAAMC,KAAG,SAAQC,KAAG,QAAOC,IAAE,CAAC;;;AGArkY,IAAAC,iBAAsI;AAAs0C,IAAIC,OAAI,CAAAC,QAAIA,GAAEA,GAAE,OAAK,CAAC,IAAE,QAAOA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,KAAID,OAAI,CAAC,CAAC;AAAhE,IAAkEE,OAAI,CAAAD,QAAIA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,QAAM,CAAC,IAAE,SAAQA,KAAIC,OAAI,CAAC,CAAC;AAAlI,IAAoI,MAAI,CAAAC,SAAIA,IAAEA,IAAE,WAAS,CAAC,IAAE,YAAWA,IAAEA,IAAE,YAAU,CAAC,IAAE,aAAYA,IAAEA,IAAE,WAAS,CAAC,IAAE,YAAWA,IAAEA,IAAE,SAAO,CAAC,IAAE,UAASA,IAAEA,IAAE,cAAY,CAAC,IAAE,eAAcA,IAAEA,IAAE,eAAa,CAAC,IAAE,gBAAeA,IAAEA,IAAE,iBAAe,CAAC,IAAE,kBAAiBA,MAAI,MAAI,CAAC,CAAC;AAAE,SAASC,GAAEC,IAAEC,MAAE,CAAAL,OAAGA,IAAE;AAAC,MAAIA,KAAEI,GAAE,oBAAkB,OAAKA,GAAE,MAAMA,GAAE,eAAe,IAAE,MAAKE,MAAEC,GAAGF,IAAED,GAAE,MAAM,MAAM,CAAC,GAAE,CAAAI,QAAGA,IAAE,QAAQ,QAAQ,OAAO,OAAO,GAAEC,KAAET,KAAEM,IAAE,QAAQN,EAAC,IAAE;AAAK,SAAOS,OAAI,OAAKA,KAAE,OAAM,EAAC,OAAMH,KAAE,iBAAgBG,GAAC;AAAC;AAAC,IAAI,KAAG,EAAC,CAAC,CAAC,EAAEL,IAAE;AAAC,SAAOA,GAAE,cAAY,IAAEA,KAAE,EAAC,GAAGA,IAAE,iBAAgB,MAAK,WAAU,EAAC;AAAC,GAAE,CAAC,CAAC,EAAEA,IAAE;AAAC,SAAOA,GAAE,cAAY,IAAEA,KAAE,EAAC,GAAGA,IAAE,YAAW,OAAG,WAAU,EAAC;AAAC,GAAE,CAAC,CAAC,GAAE,CAACA,IAAEC,QAAI;AAAC,MAAII;AAAE,MAAIT,KAAEG,GAAEC,EAAC,GAAEE,MAAEI,GAAGL,KAAE,EAAC,cAAa,MAAIL,GAAE,OAAM,oBAAmB,MAAIA,GAAE,iBAAgB,WAAU,CAAAQ,QAAGA,IAAE,IAAG,iBAAgB,CAAAA,QAAGA,IAAE,QAAQ,QAAQ,SAAQ,CAAC;AAAE,SAAM,EAAC,GAAGJ,IAAE,GAAGJ,IAAE,aAAY,IAAG,iBAAgBM,KAAE,oBAAmBG,KAAEJ,IAAE,YAAU,OAAKI,KAAE,EAAC;AAAC,GAAE,CAAC,CAAC,GAAE,CAACL,IAAEC,QAAI;AAAC,MAAIC,MAAEF,GAAE,gBAAc,KAAG,IAAE,GAAEK,KAAEL,GAAE,cAAYC,IAAE,MAAM,YAAY,GAAEM,OAAGP,GAAE,oBAAkB,OAAKA,GAAE,MAAM,MAAMA,GAAE,kBAAgBE,GAAC,EAAE,OAAOF,GAAE,MAAM,MAAM,GAAEA,GAAE,kBAAgBE,GAAC,CAAC,IAAEF,GAAE,OAAO,KAAK,CAAAQ,QAAG;AAAC,QAAIC;AAAE,aAAQA,MAAED,IAAE,QAAQ,QAAQ,cAAY,OAAK,SAAOC,IAAE,WAAWJ,EAAC,MAAI,CAACG,IAAE,QAAQ,QAAQ;AAAA,EAAQ,CAAC,GAAEV,MAAES,MAAEP,GAAE,MAAM,QAAQO,GAAC,IAAE;AAAG,SAAOT,QAAI,MAAIA,QAAIE,GAAE,kBAAgB,EAAC,GAAGA,IAAE,aAAYK,GAAC,IAAE,EAAC,GAAGL,IAAE,aAAYK,IAAE,iBAAgBP,KAAE,mBAAkB,EAAC;AAAC,GAAE,CAAC,CAAC,EAAEE,IAAE;AAAC,SAAOA,GAAE,gBAAc,KAAGA,KAAE,EAAC,GAAGA,IAAE,aAAY,IAAG,uBAAsB,KAAI;AAAC,GAAE,CAAC,CAAC,GAAE,CAACA,IAAEC,QAAI;AAAC,MAAIL,KAAEG,GAAEC,IAAE,CAAAE,QAAG,CAAC,GAAGA,KAAE,EAAC,IAAGD,IAAE,IAAG,SAAQA,IAAE,QAAO,CAAC,CAAC;AAAE,SAAM,EAAC,GAAGD,IAAE,GAAGJ,GAAC;AAAC,GAAE,CAAC,CAAC,GAAE,CAACI,IAAEC,QAAI;AAAC,MAAIL,KAAEG,GAAEC,IAAE,CAAAE,QAAG;AAAC,QAAIG,KAAEH,IAAE,UAAU,CAAAE,QAAGA,IAAE,OAAKH,IAAE,EAAE;AAAE,WAAOI,OAAI,MAAIH,IAAE,OAAOG,IAAE,CAAC,GAAEH;AAAA,EAAC,CAAC;AAAE,SAAM,EAAC,GAAGF,IAAE,GAAGJ,IAAE,mBAAkB,EAAC;AAAC,EAAC;AAAzoC,IAA2oCc,SAAE,eAAAC,eAAE,IAAI;AAAED,GAAE,cAAY;AAAc,SAASE,GAAEZ,IAAE;AAAC,MAAIC,UAAE,eAAAY,YAAEH,EAAC;AAAE,MAAGT,QAAI,MAAK;AAAC,QAAIL,KAAE,IAAI,MAAM,IAAII,EAAC,6CAA6C;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBJ,IAAEgB,EAAC,GAAEhB;AAAA,EAAC;AAAC,SAAOK;AAAC;AAAC,SAASa,IAAGd,IAAEC,KAAE;AAAC,SAAO,EAAEA,IAAE,MAAK,IAAGD,IAAEC,GAAC;AAAC;AAAC,IAAIc,MAAG,eAAAC;AAAE,SAASC,IAAGjB,IAAEC,KAAE;AAAC,MAAG,EAAC,YAAWL,KAAE,OAAG,GAAGM,IAAC,IAAEF,IAAEK,SAAE,eAAAa,YAAEJ,KAAG,EAAC,YAAWlB,IAAE,WAAUA,KAAE,IAAE,GAAE,eAAU,eAAAuB,WAAE,GAAE,cAAS,eAAAA,WAAE,GAAE,OAAM,CAAC,GAAE,aAAY,IAAG,iBAAgB,MAAK,mBAAkB,EAAC,CAAC,GAAE,CAAC,EAAC,WAAUf,KAAE,UAASG,KAAE,WAAUT,IAAC,GAAEU,GAAC,IAAEH,IAAEI,MAAEW,GAAEnB,GAAC;AAAE,EAAAmB,GAAE,CAACtB,KAAES,GAAC,GAAE,CAACc,IAAEC,OAAI;AAAC,QAAIC;AAAE,IAAAf,IAAE,EAAC,MAAK,EAAC,CAAC,GAAE,EAAGc,IAAEE,GAAG,KAAK,MAAIH,GAAE,eAAe,IAAGE,KAAEzB,IAAE,YAAU,QAAMyB,GAAE,MAAM;AAAA,EAAE,GAAEnB,QAAI,CAAC;AAAE,MAAID,MAAEI,GAAE,MAAI;AAAC,IAAAC,IAAE,EAAC,MAAK,EAAC,CAAC;AAAA,EAAC,CAAC,GAAEiB,SAAE,eAAAC,SAAE,OAAK,EAAC,MAAKtB,QAAI,GAAE,OAAMD,IAAC,IAAG,CAACC,KAAED,GAAC,CAAC,GAAEG,MAAE,EAAC,KAAIG,IAAC;AAAE,SAAO,eAAAkB,QAAE,cAAcjB,GAAE,UAAS,EAAC,OAAML,GAAC,GAAE,eAAAsB,QAAE,cAAczB,IAAG,EAAC,OAAM,EAAEE,KAAE,EAAC,CAAC,CAAC,GAAEwB,GAAE,MAAK,CAAC,CAAC,GAAEA,GAAE,OAAM,CAAC,EAAC,GAAE,EAAE,EAAC,UAAStB,KAAE,YAAWJ,KAAE,MAAKuB,IAAE,YAAWV,KAAG,MAAK,OAAM,CAAC,CAAC,CAAC;AAAC;AAAC,IAAIc,MAAG;AAAS,SAAS,GAAG7B,IAAEC,KAAE;AAAC,MAAIqB;AAAE,MAAI1B,KAAE,EAAE,GAAE,EAAC,IAAGM,MAAE,0BAA0BN,EAAC,IAAG,GAAGS,GAAC,IAAEL,IAAE,CAACI,KAAEG,GAAC,IAAEK,GAAE,aAAa,GAAEd,MAAEsB,GAAEhB,IAAE,WAAUH,GAAC,GAAEO,MAAE,EAAE,GAAEC,MAAEF,GAAE,CAAAgB,OAAG;AAAC,YAAOA,GAAE,KAAI;AAAA,MAAC,KAAKhB,IAAE;AAAA,MAAM,KAAKA,IAAE;AAAA,MAAM,KAAKA,IAAE;AAAU,QAAAgB,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEhB,IAAE,EAAC,MAAK,EAAC,CAAC,GAAEC,IAAE,UAAU,MAAID,IAAE,EAAC,MAAK,GAAE,OAAMuB,GAAE,MAAK,CAAC,CAAC;AAAE;AAAA,MAAM,KAAKvB,IAAE;AAAQ,QAAAgB,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEhB,IAAE,EAAC,MAAK,EAAC,CAAC,GAAEC,IAAE,UAAU,MAAID,IAAE,EAAC,MAAK,GAAE,OAAMuB,GAAE,KAAI,CAAC,CAAC;AAAE;AAAA,IAAK;AAAA,EAAC,CAAC,GAAE3B,MAAEI,GAAE,CAAAgB,OAAG;AAAC,YAAOA,GAAE,KAAI;AAAA,MAAC,KAAKhB,IAAE;AAAM,QAAAgB,GAAE,eAAe;AAAE;AAAA,IAAK;AAAA,EAAC,CAAC,GAAEE,KAAElB,GAAE,CAAAgB,OAAG;AAAC,QAAG3B,GAAG2B,GAAE,aAAa,EAAE,QAAOA,GAAE,eAAe;AAAE,IAAAvB,GAAE,aAAWI,IAAE,cAAY,KAAGG,IAAE,EAAC,MAAK,EAAC,CAAC,GAAEC,IAAE,UAAU,MAAI;AAAC,UAAIuB;AAAE,cAAOA,KAAE3B,IAAE,UAAU,YAAU,OAAK,SAAO2B,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,IAAC,CAAC,MAAIR,GAAE,eAAe,GAAEhB,IAAE,EAAC,MAAK,EAAC,CAAC;AAAA,EAAG,CAAC,GAAED,UAAE,eAAAoB,SAAE,OAAK,EAAC,MAAKtB,IAAE,cAAY,EAAC,IAAG,CAACA,GAAC,CAAC,GAAEiB,KAAE,EAAC,KAAIvB,KAAE,IAAGI,KAAE,MAAKsB,GAAGxB,IAAEI,IAAE,SAAS,GAAE,iBAAgB,QAAO,kBAAiBkB,KAAElB,IAAE,SAAS,YAAU,OAAK,SAAOkB,GAAE,IAAG,iBAAgBlB,IAAE,cAAY,GAAE,WAAUK,KAAE,SAAQN,KAAE,SAAQsB,GAAC;AAAE,SAAO,EAAE,EAAC,UAASJ,IAAE,YAAWhB,IAAE,MAAKC,KAAE,YAAWuB,KAAG,MAAK,cAAa,CAAC;AAAC;AAAC,IAAIG,MAAG;AAAP,IAAaC,MAAGC,GAAE,iBAAeA,GAAE;AAAO,SAASC,IAAGnC,IAAEC,KAAE;AAAC,MAAI8B,IAAEK;AAAE,MAAIxC,KAAE,EAAE,GAAE,EAAC,IAAGM,MAAE,yBAAyBN,EAAC,IAAG,GAAGS,GAAC,IAAEL,IAAE,CAACI,KAAEG,GAAC,IAAEK,GAAE,YAAY,GAAEd,MAAEsB,GAAEhB,IAAE,UAASH,GAAC,GAAEO,MAAE6B,GAAEjC,IAAE,QAAQ,GAAEK,MAAE,EAAE,GAAEN,MAAEF,GAAG,GAAEwB,MAAG,MAAItB,QAAI,QAAMA,MAAEyB,GAAE,UAAQA,GAAE,OAAKxB,IAAE,cAAY,GAAG;AAAE,qBAAAkC,WAAE,MAAI;AAAC,QAAID,KAAEjC,IAAE,SAAS;AAAQ,IAAAiC,MAAGjC,IAAE,cAAY,KAAGiC,QAAK7B,OAAG,OAAK,SAAOA,IAAE,kBAAgB6B,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,EAAC,GAAE,CAACjC,IAAE,WAAUA,IAAE,UAASI,GAAC,CAAC,GAAE+B,GAAG,EAAC,WAAUnC,IAAE,SAAS,SAAQ,SAAQA,IAAE,cAAY,GAAE,OAAOiC,IAAE;AAAC,WAAOA,GAAE,aAAa,MAAM,MAAI,aAAW,WAAW,gBAAcA,GAAE,aAAa,MAAM,IAAE,WAAW,cAAY,WAAW;AAAA,EAAa,GAAE,KAAKA,IAAE;AAAC,IAAAA,GAAE,aAAa,QAAO,MAAM;AAAA,EAAC,EAAC,CAAC;AAAE,MAAI/B,MAAEC,GAAE,CAAA8B,OAAG;AAAC,QAAIG,IAAEC;AAAE,YAAOhC,IAAE,QAAQ,GAAE4B,GAAE,KAAI;AAAA,MAAC,KAAK9B,IAAE;AAAM,YAAGH,IAAE,gBAAc,GAAG,QAAOiC,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAE9B,IAAE,EAAC,MAAK,GAAE,OAAM8B,GAAE,IAAG,CAAC;AAAA,MAAE,KAAK9B,IAAE;AAAM,YAAG8B,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAE9B,IAAE,EAAC,MAAK,EAAC,CAAC,GAAEH,IAAE,oBAAkB,MAAK;AAAC,cAAG,EAAC,SAAQsC,IAAC,IAAEtC,IAAE,MAAMA,IAAE,eAAe;AAAE,WAACqC,MAAGD,KAAEE,IAAE,YAAU,OAAK,SAAOF,GAAE,OAAO,YAAU,QAAMC,GAAE,MAAM;AAAA,QAAC;AAAC,UAAErC,IAAE,UAAU,OAAO;AAAE;AAAA,MAAM,KAAKG,IAAE;AAAU,eAAO8B,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAE9B,IAAE,EAAC,MAAK,GAAE,OAAMuB,GAAE,KAAI,CAAC;AAAA,MAAE,KAAKvB,IAAE;AAAQ,eAAO8B,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAE9B,IAAE,EAAC,MAAK,GAAE,OAAMuB,GAAE,SAAQ,CAAC;AAAA,MAAE,KAAKvB,IAAE;AAAA,MAAK,KAAKA,IAAE;AAAO,eAAO8B,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAE9B,IAAE,EAAC,MAAK,GAAE,OAAMuB,GAAE,MAAK,CAAC;AAAA,MAAE,KAAKvB,IAAE;AAAA,MAAI,KAAKA,IAAE;AAAS,eAAO8B,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAE9B,IAAE,EAAC,MAAK,GAAE,OAAMuB,GAAE,KAAI,CAAC;AAAA,MAAE,KAAKvB,IAAE;AAAO,QAAA8B,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAE9B,IAAE,EAAC,MAAK,EAAC,CAAC,GAAEA,GAAE,EAAE,UAAU,MAAI;AAAC,cAAImC;AAAE,kBAAOA,MAAEtC,IAAE,UAAU,YAAU,OAAK,SAAOsC,IAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,QAAC,CAAC;AAAE;AAAA,MAAM,KAAKnC,IAAE;AAAI,QAAA8B,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAE9B,IAAE,EAAC,MAAK,EAAC,CAAC,GAAEA,GAAE,EAAE,UAAU,MAAI;AAAC,YAAGH,IAAE,UAAU,SAAQiC,GAAE,WAAS,EAAE,WAAS,EAAE,IAAI;AAAA,QAAC,CAAC;AAAE;AAAA,MAAM;AAAQ,QAAAA,GAAE,IAAI,WAAS,MAAI9B,IAAE,EAAC,MAAK,GAAE,OAAM8B,GAAE,IAAG,CAAC,GAAE5B,IAAE,WAAW,MAAIF,IAAE,EAAC,MAAK,EAAC,CAAC,GAAE,GAAG;AAAG;AAAA,IAAK;AAAA,EAAC,CAAC,GAAEc,KAAEd,GAAE,CAAA8B,OAAG;AAAC,YAAOA,GAAE,KAAI;AAAA,MAAC,KAAK9B,IAAE;AAAM,QAAA8B,GAAE,eAAe;AAAE;AAAA,IAAK;AAAA,EAAC,CAAC,GAAEf,SAAE,eAAAI,SAAE,OAAK,EAAC,MAAKtB,IAAE,cAAY,EAAC,IAAG,CAACA,GAAC,CAAC,GAAEmB,KAAE,EAAC,yBAAwBnB,IAAE,oBAAkB,SAAO2B,KAAE3B,IAAE,MAAMA,IAAE,eAAe,MAAI,OAAK,SAAO2B,GAAE,IAAG,oBAAmBK,KAAEhC,IAAE,UAAU,YAAU,OAAK,SAAOgC,GAAE,IAAG,IAAGlC,KAAE,WAAUI,KAAE,SAAQe,IAAE,MAAK,QAAO,UAAS,GAAE,KAAIvB,IAAC;AAAE,SAAO,EAAE,EAAC,UAASyB,IAAE,YAAWlB,IAAE,MAAKiB,IAAE,YAAWU,KAAG,UAASC,KAAG,SAAQR,IAAE,MAAK,aAAY,CAAC;AAAC;AAAC,IAAI,KAAG,eAAAT;AAAE,SAAS2B,IAAG3C,IAAEC,KAAE;AAAC,MAAIL,KAAE,EAAE,GAAE,EAAC,IAAGM,MAAE,wBAAwBN,EAAC,IAAG,UAASS,KAAE,OAAG,GAAGD,IAAC,IAAEJ,IAAE,CAACO,KAAET,GAAC,IAAEc,GAAE,WAAW,GAAEJ,MAAED,IAAE,oBAAkB,OAAKA,IAAE,MAAMA,IAAE,eAAe,EAAE,OAAKL,MAAE,OAAGO,UAAE,eAAAmC,QAAE,IAAI,GAAEzC,MAAEiB,GAAEnB,KAAEQ,GAAC;AAAE,IAAE,MAAI;AAAC,QAAGF,IAAE,cAAYA,IAAE,cAAY,KAAG,CAACC,OAAGD,IAAE,sBAAoB,EAAE;AAAO,QAAIiB,KAAEjB,GAAE;AAAE,WAAOiB,GAAE,sBAAsB,MAAI;AAAC,UAAIqB,IAAEC;AAAE,OAACA,MAAGD,KAAEpC,IAAE,YAAU,OAAK,SAAOoC,GAAE,mBAAiB,QAAMC,GAAE,KAAKD,IAAE,EAAC,OAAM,UAAS,CAAC;AAAA,IAAC,CAAC,GAAErB,GAAE;AAAA,EAAO,GAAE,CAACjB,IAAE,YAAWE,KAAED,KAAED,IAAE,WAAUA,IAAE,mBAAkBA,IAAE,eAAe,CAAC;AAAE,MAAIkB,KAAEvB,IAAGO,GAAC,GAAEH,UAAE,eAAAsC,QAAE,EAAC,UAASvC,IAAE,QAAOI,KAAE,IAAI,YAAW;AAAC,WAAOgB,GAAE;AAAA,EAAC,EAAC,CAAC;AAAE,IAAE,MAAI;AAAC,IAAAnB,IAAE,QAAQ,WAASD;AAAA,EAAC,GAAE,CAACC,KAAED,EAAC,CAAC,GAAE,EAAE,OAAKP,IAAE,EAAC,MAAK,GAAE,IAAGI,KAAE,SAAQI,IAAC,CAAC,GAAE,MAAIR,IAAE,EAAC,MAAK,GAAE,IAAGI,IAAC,CAAC,IAAG,CAACI,KAAEJ,GAAC,CAAC;AAAE,MAAImB,KAAEd,GAAE,MAAI;AAAC,IAAAT,IAAE,EAAC,MAAK,EAAC,CAAC;AAAA,EAAC,CAAC,GAAEwB,KAAEf,GAAE,CAAAiB,OAAG;AAAC,QAAGnB,GAAE,QAAOmB,GAAE,eAAe;AAAE,IAAA1B,IAAE,EAAC,MAAK,EAAC,CAAC,GAAE,EAAES,IAAE,UAAU,OAAO;AAAA,EAAC,CAAC,GAAEgB,KAAEhB,GAAE,MAAI;AAAC,QAAGF,GAAE,QAAOP,IAAE,EAAC,MAAK,GAAE,OAAMgC,GAAE,QAAO,CAAC;AAAE,IAAAhC,IAAE,EAAC,MAAK,GAAE,OAAMgC,GAAE,UAAS,IAAG5B,IAAC,CAAC;AAAA,EAAC,CAAC,GAAE6B,KAAE9B,GAAG,GAAEmC,KAAE7B,GAAE,CAAAiB,OAAGO,GAAE,OAAOP,EAAC,CAAC,GAAEa,KAAE9B,GAAE,CAAAiB,OAAG;AAAC,IAAAO,GAAE,SAASP,EAAC,MAAInB,MAAGG,OAAGV,IAAE,EAAC,MAAK,GAAE,OAAMgC,GAAE,UAAS,IAAG5B,KAAE,SAAQ,EAAC,CAAC;AAAA,EAAE,CAAC,GAAEsC,KAAEjC,GAAE,CAAAiB,OAAG;AAAC,IAAAO,GAAE,SAASP,EAAC,MAAInB,MAAGG,OAAGV,IAAE,EAAC,MAAK,GAAE,OAAMgC,GAAE,QAAO,CAAC;AAAA,EAAE,CAAC,GAAEW,SAAE,eAAAf,SAAE,OAAK,EAAC,QAAOlB,KAAE,UAASH,IAAE,OAAMgB,GAAC,IAAG,CAACb,KAAEH,IAAEgB,EAAC,CAAC;AAAE,SAAO,EAAE,EAAC,UAAS,EAAC,IAAGnB,KAAE,KAAIC,KAAE,MAAK,YAAW,UAASE,OAAI,OAAG,SAAO,IAAG,iBAAgBA,OAAI,OAAG,OAAG,QAAO,UAAS,QAAO,SAAQiB,IAAE,SAAQC,IAAE,gBAAea,IAAE,cAAaA,IAAE,eAAcC,IAAE,aAAYA,IAAE,gBAAeG,IAAE,cAAaA,GAAC,GAAE,YAAWpC,KAAE,MAAKqC,IAAE,YAAW,IAAG,MAAK,YAAW,CAAC;AAAC;AAAC,IAAIM,MAAG,EAAE9B,GAAE;AAAX,IAAa,KAAG,EAAE,EAAE;AAApB,IAAsB,KAAG,EAAEkB,GAAE;AAA7B,IAA+B,KAAG,EAAEQ,GAAE;AAAtC,IAAwCK,MAAG,OAAO,OAAOD,KAAG,EAAC,QAAO,IAAG,OAAM,IAAG,MAAK,GAAE,CAAC;;;ACAriR,IAAAE,iBAA0I;AAA8+C,IAAIC,OAAI,CAAAC,SAAIA,IAAEA,IAAE,OAAK,CAAC,IAAE,QAAOA,IAAEA,IAAE,SAAO,CAAC,IAAE,UAASA,MAAID,OAAI,CAAC,CAAC;AAAhE,IAAkEE,OAAI,CAAAC,QAAIA,GAAEA,GAAE,gBAAc,CAAC,IAAE,iBAAgBA,GAAEA,GAAE,eAAa,CAAC,IAAE,gBAAeA,GAAEA,GAAE,YAAU,CAAC,IAAE,aAAYA,GAAEA,GAAE,cAAY,CAAC,IAAE,eAAcA,GAAEA,GAAE,WAAS,CAAC,IAAE,YAAWA,GAAEA,GAAE,aAAW,CAAC,IAAE,cAAaA,KAAID,OAAI,CAAC,CAAC;AAAE,IAAIE,MAAG,EAAC,CAAC,CAAC,GAAE,CAAAC,QAAG;AAAC,MAAIC,MAAE,EAAC,GAAGD,KAAE,cAAa,EAAEA,IAAE,cAAa,EAAC,CAAC,CAAC,GAAE,GAAE,CAAC,CAAC,GAAE,EAAC,CAAC,EAAC;AAAE,SAAOC,IAAE,iBAAe,MAAIA,IAAE,aAAW,QAAIA;AAAC,GAAE,CAAC,CAAC,EAAED,KAAE;AAAC,SAAOA,IAAE,iBAAe,IAAEA,MAAE,EAAC,GAAGA,KAAE,cAAa,EAAC;AAAC,GAAE,CAAC,CAAC,EAAEA,KAAEC,KAAE;AAAC,SAAOD,IAAE,WAASC,IAAE,SAAOD,MAAE,EAAC,GAAGA,KAAE,QAAOC,IAAE,OAAM;AAAC,GAAE,CAAC,CAAC,EAAED,KAAEC,KAAE;AAAC,SAAOD,IAAE,aAAWC,IAAE,WAASD,MAAE,EAAC,GAAGA,KAAE,UAASC,IAAE,SAAQ;AAAC,GAAE,CAAC,CAAC,EAAED,KAAEC,KAAE;AAAC,SAAOD,IAAE,UAAQC,IAAE,QAAMD,MAAE,EAAC,GAAGA,KAAE,OAAMC,IAAE,MAAK;AAAC,GAAE,CAAC,CAAC,EAAED,KAAEC,KAAE;AAAC,SAAOD,IAAE,YAAUC,IAAE,UAAQD,MAAE,EAAC,GAAGA,KAAE,SAAQC,IAAE,QAAO;AAAC,EAAC;AAAnb,IAAqbC,UAAG,eAAAC,eAAE,IAAI;AAAED,IAAG,cAAY;AAAiB,SAASE,IAAGJ,KAAE;AAAC,MAAIC,UAAE,eAAAI,YAAEH,GAAE;AAAE,MAAGD,QAAI,MAAK;AAAC,QAAIL,MAAE,IAAI,MAAM,IAAII,GAAC,gDAAgD;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBJ,KAAEQ,GAAE,GAAER;AAAA,EAAC;AAAC,SAAOK;AAAC;AAAC,IAAIK,UAAG,eAAAH,eAAE,IAAI;AAAEG,IAAG,cAAY;AAAoB,SAASC,IAAGP,KAAE;AAAC,MAAIC,UAAE,eAAAI,YAAEC,GAAE;AAAE,MAAGL,QAAI,MAAK;AAAC,QAAIL,MAAE,IAAI,MAAM,IAAII,GAAC,gDAAgD;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBJ,KAAEW,GAAE,GAAEX;AAAA,EAAC;AAAC,SAAOK;AAAC;AAAC,IAAIO,UAAG,eAAAL,eAAE,IAAI;AAAEK,IAAG,cAAY;AAAsB,SAASC,MAAI;AAAC,aAAO,eAAAJ,YAAEG,GAAE;AAAC;AAAC,IAAIE,UAAG,eAAAP,eAAE,IAAI;AAAEO,IAAG,cAAY;AAAsB,SAASC,MAAI;AAAC,aAAO,eAAAN,YAAEK,GAAE;AAAC;AAAC,SAAS,GAAGV,KAAEC,KAAE;AAAC,SAAO,EAAEA,IAAE,MAAKF,KAAGC,KAAEC,GAAC;AAAC;AAAC,IAAIW,MAAG;AAAM,SAASC,IAAGb,KAAEC,KAAE;AAAC,MAAIa;AAAE,MAAG,EAAC,YAAWlB,MAAE,OAAG,GAAGmB,GAAC,IAAEf,KAAEgB,SAAE,eAAAC,QAAE,IAAI,GAAEC,KAAEC,GAAElB,KAAEmB,GAAG,CAAAC,QAAG;AAAC,IAAAL,GAAE,UAAQK;AAAA,EAAC,CAAC,CAAC,GAAEvB,SAAE,eAAAmB,QAAE,CAAC,CAAC,GAAEK,UAAE,eAAAC,YAAG,IAAG,EAAC,YAAW3B,KAAE,cAAaA,MAAE,IAAE,GAAE,SAAQE,IAAE,QAAO,MAAK,UAAS,MAAK,OAAM,MAAK,SAAQ,MAAK,yBAAoB,eAAA0B,WAAG,GAAE,wBAAmB,eAAAA,WAAG,EAAC,CAAC,GAAE,CAAC,EAAC,cAAaC,KAAE,QAAOC,KAAE,UAASC,KAAE,OAAMC,KAAE,SAAQC,IAAE,qBAAoBV,IAAE,oBAAmBW,GAAC,GAAEC,EAAC,IAAET,KAAEU,KAAEd,IAAIJ,KAAEE,GAAE,YAAU,OAAKF,KAAEY,GAAC,GAAEO,SAAE,eAAAC,SAAE,MAAI;AAAC,QAAG,CAACR,OAAG,CAACE,IAAE,QAAM;AAAG,aAAQ,KAAK,SAAS,iBAAiB,UAAU,EAAE,KAAG,OAAO,KAAG,OAAK,SAAO,EAAE,SAASF,GAAC,CAAC,IAAE,OAAO,KAAG,OAAK,SAAO,EAAE,SAASE,GAAC,CAAC,EAAE,QAAM;AAAG,QAAIP,MAAEI,GAAG,GAAEU,MAAEd,IAAE,QAAQK,GAAC,GAAEU,MAAGD,MAAEd,IAAE,SAAO,KAAGA,IAAE,QAAOgB,MAAGF,MAAE,KAAGd,IAAE,QAAOiB,KAAEjB,IAAEe,EAAC,GAAEG,MAAGlB,IAAEgB,EAAC;AAAE,WAAM,CAACT,IAAE,SAASU,EAAC,KAAG,CAACV,IAAE,SAASW,GAAE;AAAA,EAAC,GAAE,CAACb,KAAEE,GAAC,CAAC,GAAEY,MAAEd,GAAGC,GAAC,GAAEc,KAAEf,GAAGG,EAAC,GAAEa,SAAE,eAAAR,SAAE,OAAK,EAAC,UAASM,KAAE,SAAQC,IAAE,OAAM,MAAIV,GAAE,EAAC,MAAK,EAAC,CAAC,EAAC,IAAG,CAACS,KAAEC,IAAEV,EAAC,CAAC,GAAEY,KAAElC,IAAG,GAAEmC,KAAED,MAAG,OAAK,SAAOA,GAAE,iBAAgBE,KAAE5C,GAAE,MAAI;AAAC,QAAIoB;AAAE,YAAOA,MAAEsB,MAAG,OAAK,SAAOA,GAAE,0BAA0B,MAAI,OAAKtB,OAAGW,MAAG,OAAK,SAAOA,GAAE,oBAAkBN,OAAG,OAAK,SAAOA,IAAE,SAASM,GAAE,aAAa,OAAKJ,OAAG,OAAK,SAAOA,IAAE,SAASI,GAAE,aAAa;AAAA,EAAG,CAAC;AAAE,qBAAAc,WAAG,MAAIF,MAAG,OAAK,SAAOA,GAAEF,EAAC,GAAE,CAACE,IAAEF,EAAC,CAAC;AAAE,MAAG,CAACK,IAAEC,EAAC,IAAEF,IAAG,GAAE1B,KAAE6B,GAAG,EAAC,iBAAgBN,MAAG,OAAK,SAAOA,GAAE,iBAAgB,SAAQI,IAAE,mBAAkB,CAACrB,KAAEE,GAAC,EAAC,CAAC;AAAE,EAAAK,GAAGD,MAAG,OAAK,SAAOA,GAAE,aAAY,SAAQ,CAAAX,QAAG;AAAC,QAAIc,KAAEC,IAAEC,IAAEC;AAAE,IAAAjB,IAAE,WAAS,UAAQA,IAAE,kBAAkB,eAAaI,QAAI,MAAIoB,GAAE,KAAGnB,OAAGE,QAAIR,GAAE,SAASC,IAAE,MAAM,MAAIe,MAAGD,MAAEhB,GAAE,YAAU,OAAK,SAAOgB,IAAE,aAAW,QAAMC,GAAE,KAAKD,KAAEd,IAAE,MAAM,MAAIiB,MAAGD,KAAEP,GAAE,YAAU,OAAK,SAAOO,GAAE,aAAW,QAAMC,GAAE,KAAKD,IAAEhB,IAAE,MAAM,KAAGU,GAAE,EAAC,MAAK,EAAC,CAAC;AAAA,EAAG,GAAE,IAAE,GAAEZ,GAAGC,GAAE,mBAAkB,CAACC,KAAEc,QAAI;AAAC,IAAAJ,GAAE,EAAC,MAAK,EAAC,CAAC,GAAE,EAAGI,KAAEf,GAAG,KAAK,MAAIC,IAAE,eAAe,GAAEK,OAAG,QAAMA,IAAE,MAAM;AAAA,EAAE,GAAED,QAAI,CAAC;AAAE,MAAIyB,MAAEjD,GAAE,CAAAoB,QAAG;AAAC,IAAAU,GAAE,EAAC,MAAK,EAAC,CAAC;AAAE,QAAII,OAAG,MAAId,MAAEA,eAAa,cAAYA,MAAE,aAAYA,OAAGA,IAAE,mBAAmB,cAAYA,IAAE,UAAQK,MAAEA,KAAG;AAAE,IAAAS,OAAG,QAAMA,IAAE,MAAM;AAAA,EAAC,CAAC,GAAEgB,SAAE,eAAAjB,SAAE,OAAK,EAAC,OAAMgB,KAAE,aAAYjB,GAAC,IAAG,CAACiB,KAAEjB,EAAC,CAAC,GAAEmB,UAAE,eAAAlB,SAAE,OAAK,EAAC,MAAKT,QAAI,GAAE,OAAMyB,IAAC,IAAG,CAACzB,KAAEyB,GAAC,CAAC,GAAEG,KAAE,EAAC,KAAInC,GAAC;AAAE,SAAO,eAAAoC,QAAE,cAAc5C,IAAG,UAAS,EAAC,OAAM,KAAI,GAAE,eAAA4C,QAAE,cAAcpD,IAAG,UAAS,EAAC,OAAMoB,IAAC,GAAE,eAAAgC,QAAE,cAAchD,IAAG,UAAS,EAAC,OAAM6C,GAAC,GAAE,eAAAG,QAAE,cAAc5B,IAAG,EAAC,OAAM,EAAED,KAAE,EAAC,CAAC,CAAC,GAAEyB,GAAE,MAAK,CAAC,CAAC,GAAEA,GAAE,OAAM,CAAC,EAAC,GAAE,eAAAI,QAAE,cAAcN,IAAE,MAAK,EAAE,EAAC,UAASK,IAAE,YAAWtC,IAAE,MAAKqC,KAAE,YAAWxC,KAAG,MAAK,UAAS,CAAC,GAAE,eAAA0C,QAAE,cAAclC,GAAE,cAAa,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAAC;AAAC,IAAImC,MAAG;AAAS,SAASC,IAAGxD,KAAEC,KAAE;AAAC,MAAIL,MAAE,EAAE,GAAE,EAAC,IAAGmB,KAAE,6BAA6BnB,GAAC,IAAG,GAAGoB,GAAC,IAAEhB,KAAE,CAACkB,IAAEpB,EAAC,IAAEM,IAAG,gBAAgB,GAAE,EAAC,aAAYkB,IAAC,IAAEf,IAAG,gBAAgB,GAAEkB,UAAE,eAAAR,QAAE,IAAI,GAAES,MAAE,6BAA6B,EAAE,CAAC,IAAGC,MAAElB,IAAG,GAAEmB,MAAED,OAAG,OAAK,SAAOA,IAAE,aAAYR,KAAER,IAAG,MAAI;AAAK,qBAAAmC,WAAG,MAAI;AAAC,QAAG,CAAC3B,GAAE,QAAOrB,GAAE,EAAC,MAAK,GAAE,UAASiB,GAAC,CAAC,GAAE,MAAI;AAAC,MAAAjB,GAAE,EAAC,MAAK,GAAE,UAAS,KAAI,CAAC;AAAA,IAAC;AAAA,EAAC,GAAE,CAACqB,IAAEJ,IAAEjB,EAAC,CAAC;AAAE,MAAG,CAACgC,EAAC,QAAE,eAAA2B,UAAG,MAAI,OAAO,CAAC,GAAE1B,KAAEZ,GAAEM,KAAExB,KAAEkB,KAAE,OAAK,CAAAgC,OAAG;AAAC,QAAGA,GAAE,CAAAjC,GAAE,QAAQ,QAAQ,KAAKY,EAAC;AAAA,SAAM;AAAC,UAAIsB,MAAElC,GAAE,QAAQ,QAAQ,QAAQY,EAAC;AAAE,MAAAsB,QAAI,MAAIlC,GAAE,QAAQ,QAAQ,OAAOkC,KAAE,CAAC;AAAA,IAAC;AAAC,IAAAlC,GAAE,QAAQ,QAAQ,SAAO,KAAG,QAAQ,KAAK,wFAAwF,GAAEiC,MAAGrD,GAAE,EAAC,MAAK,GAAE,QAAOqD,GAAC,CAAC;AAAA,EAAC,CAAC,GAAEnB,KAAEb,GAAEM,KAAExB,GAAC,GAAEgC,KAAEf,GAAGO,GAAC,GAAEe,MAAEvC,GAAE,CAAAkD,OAAG;AAAC,QAAIC,KAAEC,IAAEvC;AAAE,QAAGK,IAAE;AAAC,UAAGD,GAAE,iBAAe,EAAE;AAAO,cAAOiC,GAAE,KAAI;AAAA,QAAC,KAAKlD,IAAE;AAAA,QAAM,KAAKA,IAAE;AAAM,UAAAkD,GAAE,eAAe,IAAGE,MAAGD,MAAED,GAAE,QAAQ,UAAQ,QAAME,GAAE,KAAKD,GAAC,GAAEtD,GAAE,EAAC,MAAK,EAAC,CAAC,IAAGgB,KAAEI,GAAE,WAAS,QAAMJ,GAAE,MAAM;AAAE;AAAA,MAAK;AAAA,IAAC,MAAM,SAAOqC,GAAE,KAAI;AAAA,MAAC,KAAKlD,IAAE;AAAA,MAAM,KAAKA,IAAE;AAAM,QAAAkD,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEjC,GAAE,iBAAe,MAAIU,OAAG,QAAMA,IAAEV,GAAE,QAAQ,IAAGpB,GAAE,EAAC,MAAK,EAAC,CAAC;AAAE;AAAA,MAAM,KAAKG,IAAE;AAAO,YAAGiB,GAAE,iBAAe,EAAE,QAAOU,OAAG,OAAK,SAAOA,IAAEV,GAAE,QAAQ;AAAE,YAAG,CAACO,IAAE,WAASQ,MAAG,QAAMA,GAAE,iBAAe,CAACR,IAAE,QAAQ,SAASQ,GAAE,aAAa,EAAE;AAAO,QAAAkB,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAErD,GAAE,EAAC,MAAK,EAAC,CAAC;AAAE;AAAA,IAAK;AAAA,EAAC,CAAC,GAAE2C,KAAExC,GAAE,CAAAkD,OAAG;AAAC,IAAAhC,MAAGgC,GAAE,QAAMlD,IAAE,SAAOkD,GAAE,eAAe;AAAA,EAAC,CAAC,GAAET,KAAEzC,GAAE,CAAAkD,OAAG;AAAC,QAAIC,KAAEC;AAAE,IAAAF,GAAGA,GAAE,aAAa,KAAGnD,IAAE,aAAWmB,MAAGrB,GAAE,EAAC,MAAK,EAAC,CAAC,IAAGsD,MAAElC,GAAE,WAAS,QAAMkC,IAAE,MAAM,MAAID,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEjC,GAAE,iBAAe,MAAIU,OAAG,QAAMA,IAAEV,GAAE,QAAQ,IAAGpB,GAAE,EAAC,MAAK,EAAC,CAAC,IAAGuD,KAAEnC,GAAE,WAAS,QAAMmC,GAAE,MAAM;AAAA,EAAG,CAAC,GAAEV,KAAE1C,GAAE,CAAAkD,OAAG;AAAC,IAAAA,GAAE,eAAe,GAAEA,GAAE,gBAAgB;AAAA,EAAC,CAAC,GAAEP,KAAE1B,GAAE,iBAAe,GAAE2B,SAAE,eAAAX,SAAE,OAAK,EAAC,MAAKU,GAAC,IAAG,CAACA,EAAC,CAAC,GAAEG,KAAE3B,GAAGpB,KAAEyB,GAAC,GAAEuB,KAAE7B,KAAE,EAAC,KAAIa,IAAE,MAAKe,IAAE,WAAUP,KAAE,SAAQE,GAAC,IAAE,EAAC,KAAIX,IAAE,IAAGb,GAAE,UAAS,MAAK6B,IAAE,iBAAgB7B,GAAE,iBAAe,GAAE,iBAAgBA,GAAE,QAAMA,GAAE,UAAQ,QAAO,WAAUsB,KAAE,SAAQC,IAAE,SAAQC,IAAE,aAAYC,GAAC,GAAEvB,KAAEF,GAAG,GAAEgC,MAAEjD,GAAE,MAAI;AAAC,QAAIkD,KAAEjC,GAAE;AAAM,QAAG,CAACiC,GAAE;AAAO,aAASC,MAAG;AAAC,QAAEhC,GAAE,SAAQ,EAAC,CAACM,IAAE,QAAQ,GAAE,MAAI,EAAEyB,IAAE,EAAE,KAAK,GAAE,CAACzB,IAAE,SAAS,GAAE,MAAI,EAAEyB,IAAE,EAAE,IAAI,EAAC,CAAC,MAAI,EAAG,SAAO,EAAE1B,GAAG,EAAE,OAAO,CAAAX,OAAGA,GAAE,QAAQ,yBAAuB,MAAM,GAAE,EAAEM,GAAE,SAAQ,EAAC,CAACM,IAAE,QAAQ,GAAE,EAAE,MAAK,CAACA,IAAE,SAAS,GAAE,EAAE,SAAQ,CAAC,GAAE,EAAC,YAAWR,GAAE,OAAM,CAAC;AAAA,IAAC;AAAC,IAAAkC,IAAE;AAAA,EAAC,CAAC;AAAE,SAAO,eAAAE,QAAE,cAAc,eAAAA,QAAE,UAAS,MAAK,EAAE,EAAC,UAASN,IAAE,YAAWhC,IAAE,MAAK6B,IAAE,YAAWU,KAAG,MAAK,iBAAgB,CAAC,GAAEX,MAAG,CAACzB,MAAGG,OAAG,eAAAgC,QAAE,cAAc1D,IAAG,EAAC,IAAG8B,KAAE,UAASA,GAAG,WAAU,+BAA8B,MAAG,IAAG,UAAS,MAAK,UAAS,SAAQwB,IAAC,CAAC,CAAC;AAAC;AAAC,IAAIQ,MAAG;AAAP,IAAaC,MAAGhB,GAAG,iBAAeA,GAAG;AAAO,SAASiB,IAAG5D,KAAEC,KAAE;AAAC,MAAIL,MAAE,EAAE,GAAE,EAAC,IAAGmB,KAAE,8BAA8BnB,GAAC,IAAG,GAAGoB,GAAC,IAAEhB,KAAE,CAAC,EAAC,cAAakB,GAAC,GAAEpB,EAAC,IAAEM,IAAG,iBAAiB,GAAEkB,MAAEH,GAAElB,GAAC,GAAEwB,MAAE7B,GAAG,GAAE8B,OAAG,MAAID,QAAI,QAAMA,MAAEyB,GAAE,UAAQA,GAAE,OAAKhC,OAAI,GAAG,GAAES,MAAE1B,GAAE,CAAAkB,OAAG;AAAC,QAAGgC,GAAGhC,GAAE,aAAa,EAAE,QAAOA,GAAE,eAAe;AAAE,IAAArB,GAAE,EAAC,MAAK,EAAC,CAAC;AAAA,EAAC,CAAC,GAAE8B,UAAE,eAAAM,SAAE,OAAK,EAAC,MAAKhB,OAAI,EAAC,IAAG,CAACA,EAAC,CAAC;AAAE,SAAO,EAAE,EAAC,UAAS,EAAC,KAAII,KAAE,IAAGP,IAAE,eAAc,MAAG,SAAQY,IAAC,GAAE,YAAWX,IAAE,MAAKY,KAAE,YAAW8B,KAAG,UAASC,KAAG,SAAQjC,KAAE,MAAK,kBAAiB,CAAC;AAAC;AAAC,IAAImC,MAAG;AAAP,IAAaC,MAAGnB,GAAG,iBAAeA,GAAG;AAAO,SAASoB,IAAG/D,KAAEC,KAAE;AAAC,MAAIL,MAAE,EAAE,GAAE,EAAC,IAAGmB,KAAE,4BAA4BnB,GAAC,IAAG,OAAMoB,KAAE,OAAG,GAAGE,GAAC,IAAElB,KAAE,CAACF,IAAEwB,GAAC,IAAElB,IAAG,eAAe,GAAE,EAAC,OAAMqB,KAAE,aAAYC,IAAC,IAAEnB,IAAG,eAAe,GAAEoB,MAAE,oCAAoC,EAAE,CAAC,IAAGC,MAAE,mCAAmC,EAAE,CAAC,IAAGC,SAAE,eAAAZ,QAAE,IAAI,GAAEE,KAAEA,GAAEU,IAAE5B,KAAE,CAAA8C,OAAG;AAAC,IAAAzB,IAAE,EAAC,MAAK,GAAE,OAAMyB,GAAC,CAAC;AAAA,EAAC,CAAC,GAAEjB,KAAEZ,GAAGW,EAAC,GAAEE,KAAEJ,GAAG;AAAE,IAAG,OAAKL,IAAE,EAAC,MAAK,GAAE,SAAQP,GAAC,CAAC,GAAE,MAAI;AAAC,IAAAO,IAAE,EAAC,MAAK,GAAE,SAAQ,KAAI,CAAC;AAAA,EAAC,IAAG,CAACP,IAAEO,GAAC,CAAC;AAAE,MAAIU,KAAEpC,GAAG,GAAEqC,MAAG,MAAID,OAAI,QAAMA,KAAEkB,GAAE,UAAQA,GAAE,OAAKpD,GAAE,iBAAe,GAAG,GAAE0C,MAAEvC,GAAE,CAAA8C,OAAG;AAAC,QAAIC;AAAE,YAAOD,GAAE,KAAI;AAAA,MAAC,KAAK9C,IAAE;AAAO,YAAGH,GAAE,iBAAe,KAAG,CAAC+B,GAAE,WAASC,MAAG,QAAMA,GAAE,iBAAe,CAACD,GAAE,QAAQ,SAASC,GAAE,aAAa,EAAE;AAAO,QAAAiB,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEzB,IAAE,EAAC,MAAK,EAAC,CAAC,IAAG0B,KAAElD,GAAE,WAAS,QAAMkD,GAAE,MAAM;AAAE;AAAA,IAAK;AAAA,EAAC,CAAC;AAAE,qBAAAF,WAAG,MAAI;AAAC,QAAIC;AAAE,IAAA/C,IAAE,UAAQF,GAAE,iBAAe,OAAKiD,KAAE/C,IAAE,YAAU,QAAM+C,OAAIzB,IAAE,EAAC,MAAK,GAAE,OAAM,KAAI,CAAC;AAAA,EAAC,GAAE,CAACxB,GAAE,cAAaE,IAAE,SAAQA,IAAE,QAAOsB,GAAC,CAAC,OAAE,eAAAwB,WAAG,MAAI;AAAC,QAAGhD,GAAE,cAAY,CAACkB,MAAGlB,GAAE,iBAAe,KAAG,CAAC+B,GAAE,QAAQ;AAAO,QAAIkB,KAAEjB,MAAG,OAAK,SAAOA,GAAE;AAAc,IAAAD,GAAE,QAAQ,SAASkB,EAAC,KAAG,EAAElB,GAAE,SAAQ,EAAE,KAAK;AAAA,EAAC,GAAE,CAAC/B,GAAE,YAAWkB,IAAEa,IAAE/B,GAAE,YAAY,CAAC;AAAE,MAAI2C,SAAE,eAAAP,SAAE,OAAK,EAAC,MAAKpC,GAAE,iBAAe,GAAE,OAAM2B,IAAC,IAAG,CAAC3B,IAAE2B,GAAC,CAAC,GAAEiB,KAAE,EAAC,KAAIvB,IAAE,IAAGJ,IAAE,WAAUyB,KAAE,QAAOxB,MAAGlB,GAAE,iBAAe,IAAE,CAAAiD,OAAG;AAAC,QAAI3B,IAAE8B,KAAEC,IAAEC,KAAEC;AAAE,QAAIL,KAAED,GAAE;AAAc,IAAAC,MAAGnB,GAAE,aAAWT,KAAES,GAAE,YAAU,QAAMT,GAAE,SAAS4B,EAAC,MAAI1B,IAAE,EAAC,MAAK,EAAC,CAAC,KAAI6B,MAAGD,MAAEpD,GAAE,oBAAoB,YAAU,OAAK,SAAOoD,IAAE,aAAW,QAAMC,GAAE,KAAKD,KAAEF,EAAC,MAAIK,MAAGD,MAAEtD,GAAE,mBAAmB,YAAU,OAAK,SAAOsD,IAAE,aAAW,QAAMC,GAAE,KAAKD,KAAEJ,EAAC,MAAIA,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,EAAG,IAAE,QAAO,UAAS,GAAE,GAAEL,KAAEzB,GAAG,GAAE0B,KAAE3C,GAAE,MAAI;AAAC,QAAI8C,KAAElB,GAAE;AAAQ,QAAG,CAACkB,GAAE;AAAO,aAASC,KAAG;AAAC,QAAEL,GAAE,SAAQ,EAAC,CAACjB,IAAE,QAAQ,GAAE,MAAI;AAAC,YAAIwB;AAAE,UAAEH,IAAE,EAAE,KAAK,MAAI,EAAG,WAASG,MAAEpD,GAAE,mBAAmB,YAAU,QAAMoD,IAAE,MAAM;AAAA,MAAE,GAAE,CAACxB,IAAE,SAAS,GAAE,MAAI;AAAC,YAAIN;AAAE,SAACA,KAAEtB,GAAE,WAAS,QAAMsB,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,MAAC,EAAC,CAAC;AAAA,IAAC;AAAC,IAAA4B,GAAE;AAAA,EAAC,CAAC,GAAEH,KAAE5C,GAAE,MAAI;AAAC,QAAI8C,KAAElB,GAAE;AAAQ,QAAG,CAACkB,GAAE;AAAO,aAASC,KAAG;AAAC,QAAEL,GAAE,SAAQ,EAAC,CAACjB,IAAE,QAAQ,GAAE,MAAI;AAAC,YAAIZ;AAAE,YAAG,CAAChB,GAAE,OAAO;AAAO,YAAIsB,KAAEK,GAAG,GAAEyB,MAAE9B,GAAE,QAAQtB,GAAE,MAAM,GAAEqD,KAAE/B,GAAE,MAAM,GAAE8B,MAAE,CAAC,GAAEG,KAAE,CAAC,GAAGjC,GAAE,MAAM8B,MAAE,CAAC,GAAE,GAAGC,EAAC;AAAE,iBAAQ9B,OAAKgC,GAAE,MAAM,EAAE,KAAGhC,IAAE,QAAQ,yBAAuB,WAASP,KAAEhB,GAAE,UAAQ,QAAMgB,GAAE,SAASO,GAAC,GAAE;AAAC,cAAIc,MAAEkB,GAAE,QAAQhC,GAAC;AAAE,UAAAc,QAAI,MAAIkB,GAAE,OAAOlB,KAAE,CAAC;AAAA,QAAC;AAAC,UAAEkB,IAAE,EAAE,OAAM,EAAC,QAAO,MAAE,CAAC;AAAA,MAAC,GAAE,CAAC3B,IAAE,SAAS,GAAE,MAAI;AAAC,YAAIwB;AAAE,UAAEH,IAAE,EAAE,QAAQ,MAAI,EAAG,WAASG,MAAEpD,GAAE,WAAS,QAAMoD,IAAE,MAAM;AAAA,MAAE,EAAC,CAAC;AAAA,IAAC;AAAC,IAAAF,GAAE;AAAA,EAAC,CAAC;AAAE,SAAO,eAAAM,QAAE,cAAc5C,IAAG,UAAS,EAAC,OAAMK,GAAC,GAAEkB,MAAGP,OAAG,eAAA4B,QAAE,cAAc1D,IAAG,EAAC,IAAG+B,KAAE,KAAI7B,GAAE,qBAAoB,UAAS4B,GAAG,WAAU,+BAA8B,MAAG,IAAG,UAAS,MAAK,UAAS,SAAQkB,GAAC,CAAC,GAAE,EAAE,EAAC,WAAUb,IAAE,UAASW,IAAE,YAAWxB,IAAE,MAAKuB,IAAE,YAAWoB,KAAG,UAASC,KAAG,SAAQ7B,IAAE,MAAK,gBAAe,CAAC,GAAEA,MAAGP,OAAG,eAAA4B,QAAE,cAAc1D,IAAG,EAAC,IAAGgC,KAAE,KAAI9B,GAAE,oBAAmB,UAAS4B,GAAG,WAAU,+BAA8B,MAAG,IAAG,UAAS,MAAK,UAAS,SAAQmB,GAAC,CAAC,CAAC;AAAC;AAAC,IAAImB,MAAG;AAAM,SAASC,IAAGjE,KAAEC,KAAE;AAAC,MAAIL,UAAE,eAAAqB,QAAE,IAAI,GAAEF,KAAEI,GAAEvB,KAAEK,GAAC,GAAE,CAACe,IAAEE,EAAC,QAAE,eAAAuC,UAAG,CAAC,CAAC,GAAE3D,KAAEqB,GAAG,GAAEG,MAAErB,GAAE,CAAA8B,OAAG;AAAC,IAAAb,GAAE,CAAAc,OAAG;AAAC,UAAIC,KAAED,GAAE,QAAQD,EAAC;AAAE,UAAGE,OAAI,IAAG;AAAC,YAAIO,MAAER,GAAE,MAAM;AAAE,eAAOQ,IAAE,OAAOP,IAAE,CAAC,GAAEO;AAAA,MAAC;AAAC,aAAOR;AAAA,IAAC,CAAC;AAAA,EAAC,CAAC,GAAEP,MAAExB,GAAE,CAAA8B,QAAIb,GAAE,CAAAc,OAAG,CAAC,GAAGA,IAAED,EAAC,CAAC,GAAE,MAAIT,IAAES,EAAC,EAAE,GAAEL,MAAEzB,GAAE,MAAI;AAAC,QAAIgC;AAAE,QAAIF,KAAE9B,GAAGL,GAAC;AAAE,QAAG,CAACmC,GAAE,QAAM;AAAG,QAAIC,KAAED,GAAE;AAAc,YAAOE,KAAErC,IAAE,YAAU,QAAMqC,GAAE,SAASD,EAAC,IAAE,OAAGhB,GAAE,KAAK,CAAAwB,QAAG;AAAC,UAAIC,IAAEC;AAAE,eAAQD,KAAEV,GAAE,eAAeS,IAAE,SAAS,OAAO,MAAI,OAAK,SAAOC,GAAE,SAAST,EAAC,QAAMU,KAAEX,GAAE,eAAeS,IAAE,QAAQ,OAAO,MAAI,OAAK,SAAOE,GAAE,SAASV,EAAC;AAAA,IAAE,CAAC;AAAA,EAAC,CAAC,GAAEL,MAAE1B,GAAE,CAAA8B,OAAG;AAAC,aAAQC,MAAKhB,GAAE,CAAAgB,GAAE,SAAS,YAAUD,MAAGC,GAAE,MAAM;AAAA,EAAC,CAAC,GAAEJ,UAAE,eAAAM,SAAE,OAAK,EAAC,iBAAgBT,KAAE,mBAAkBH,KAAE,2BAA0BI,KAAE,aAAYC,KAAE,iBAAgB7B,GAAE,gBAAe,IAAG,CAAC2B,KAAEH,KAAEI,KAAEC,KAAE7B,GAAE,eAAe,CAAC,GAAE+B,SAAE,eAAAK,SAAE,OAAK,CAAC,IAAG,CAAC,CAAC,GAAEf,KAAEnB,KAAE8B,KAAE,EAAC,KAAIf,GAAC;AAAE,SAAO,eAAAuC,QAAE,cAAc9C,IAAG,UAAS,EAAC,OAAMoB,IAAC,GAAE,EAAE,EAAC,UAASE,IAAE,YAAWX,IAAE,MAAKU,IAAE,YAAWmC,KAAG,MAAK,gBAAe,CAAC,GAAE,eAAAV,QAAE,cAAcxD,GAAE,cAAa,IAAI,CAAC;AAAC;AAAC,IAAIoE,MAAG,EAAErD,GAAE;AAAX,IAAasD,MAAG,EAAEX,GAAE;AAApB,IAAsBY,MAAG,EAAER,GAAE;AAA7B,IAA+BS,MAAG,EAAEN,GAAE;AAAtC,IAAwCO,MAAG,EAAEL,GAAE;AAA/C,IAAiD,KAAG,OAAO,OAAOC,KAAG,EAAC,QAAOC,KAAG,SAAQC,KAAG,OAAMC,KAAG,OAAMC,IAAE,CAAC;;;ACA7xW,IAAAC,iBAA2G;;;ACA3G,IAAAC,iBAA4E;AAAsT,IAAIC,UAAE,eAAAC,eAAE,IAAI;AAAE,SAASC,MAAG;AAAC,MAAIC,UAAE,eAAAC,YAAEJ,GAAC;AAAE,MAAGG,QAAI,MAAK;AAAC,QAAIE,MAAE,IAAI,MAAM,yEAAyE;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBA,KAAEH,GAAC,GAAEG;AAAA,EAAC;AAAC,SAAOF;AAAC;AAAC,SAASG,KAAG;AAAC,MAAG,CAACH,KAAEE,GAAC,QAAE,eAAAE,UAAE,CAAC,CAAC;AAAE,SAAM,CAACJ,IAAE,SAAO,IAAEA,IAAE,KAAK,GAAG,IAAE,YAAO,eAAAK,SAAE,MAAI,SAASC,IAAE;AAAC,QAAIC,MAAEC,GAAE,CAAAC,QAAIP,IAAE,CAAAQ,QAAG,CAAC,GAAGA,KAAED,EAAC,CAAC,GAAE,MAAIP,IAAE,CAAAQ,QAAG;AAAC,UAAIC,KAAED,IAAE,MAAM,GAAEE,KAAED,GAAE,QAAQF,EAAC;AAAE,aAAOG,OAAI,MAAID,GAAE,OAAOC,IAAE,CAAC,GAAED;AAAA,IAAC,CAAC,EAAE,GAAEH,UAAE,eAAAH,SAAE,OAAK,EAAC,UAASE,KAAE,MAAKD,GAAE,MAAK,MAAKA,GAAE,MAAK,OAAMA,GAAE,MAAK,IAAG,CAACC,KAAED,GAAE,MAAKA,GAAE,MAAKA,GAAE,KAAK,CAAC;AAAE,WAAO,eAAAO,QAAE,cAAchB,IAAE,UAAS,EAAC,OAAMW,IAAC,GAAEF,GAAE,QAAQ;AAAA,EAAC,GAAE,CAACJ,GAAC,CAAC,CAAC;AAAC;AAAC,IAAI,IAAE;AAAQ,SAASY,GAAEd,KAAEE,KAAE;AAAC,MAAIa,KAAE,EAAE,GAAE,EAAC,IAAGT,KAAE,oBAAoBS,EAAC,IAAG,SAAQR,MAAE,OAAG,GAAGC,IAAC,IAAER,KAAES,KAAEV,IAAE,GAAEW,MAAEM,GAAEd,GAAC;AAAE,IAAE,MAAIO,GAAE,SAASH,EAAC,GAAE,CAACA,IAAEG,GAAE,QAAQ,CAAC;AAAE,MAAIE,KAAE,EAAC,KAAID,KAAE,GAAGD,GAAE,OAAM,IAAGH,GAAC;AAAE,SAAOC,QAAI,aAAYI,OAAI,OAAOA,GAAE,SAAQ,OAAOA,GAAE,UAAS,aAAYH,OAAG,OAAOA,IAAE,UAAS,EAAE,EAAC,UAASG,IAAE,YAAWH,KAAE,MAAKC,GAAE,QAAM,CAAC,GAAE,YAAW,GAAE,MAAKA,GAAE,QAAM,QAAO,CAAC;AAAC;AAAC,IAAIQ,KAAE,EAAEH,EAAC;AAAT,IAAWI,KAAE,OAAO,OAAOD,IAAE,CAAC,CAAC;;;ACA/xC,IAAAE,iBAA0C;AAA2D,SAASC,IAAEC,MAAE,GAAE;AAAC,MAAG,CAACC,KAAEC,EAAC,QAAE,eAAAC,UAAEH,GAAC,GAAEI,MAAED,GAAE,GAAEE,UAAE,eAAAC,aAAE,CAAAC,OAAG;AAAC,IAAAH,IAAE,WAASF,GAAE,CAAAM,QAAGA,MAAED,EAAC;AAAA,EAAC,GAAE,CAACN,KAAEG,GAAC,CAAC,GAAEK,UAAE,eAAAH,aAAE,CAAAC,OAAG,QAAQN,MAAEM,EAAC,GAAE,CAACN,GAAC,CAAC,GAAES,UAAE,eAAAJ,aAAE,CAAAC,OAAG;AAAC,IAAAH,IAAE,WAASF,GAAE,CAAAM,QAAGA,MAAE,CAACD,EAAC;AAAA,EAAC,GAAE,CAACL,IAAEE,GAAC,CAAC,GAAEO,SAAE,eAAAL,aAAE,CAAAC,OAAG;AAAC,IAAAH,IAAE,WAASF,GAAE,CAAAM,QAAGA,MAAED,EAAC;AAAA,EAAC,GAAE,CAACL,EAAC,CAAC;AAAE,SAAM,EAAC,OAAMD,KAAE,SAAQI,KAAE,SAAQI,KAAE,YAAWC,KAAE,YAAWC,GAAC;AAAC;;;AFA8hC,IAAIC,OAAI,CAAAC,SAAIA,IAAEA,IAAE,iBAAe,CAAC,IAAE,kBAAiBA,IAAEA,IAAE,mBAAiB,CAAC,IAAE,oBAAmBA,MAAID,OAAI,CAAC,CAAC;AAAE,IAAIE,MAAG,EAAC,CAAC,CAAC,EAAEC,KAAEC,IAAE;AAAC,MAAIH,MAAE,CAAC,GAAGE,IAAE,SAAQ,EAAC,IAAGC,GAAE,IAAG,SAAQA,GAAE,SAAQ,UAASA,GAAE,SAAQ,CAAC;AAAE,SAAM,EAAC,GAAGD,KAAE,SAAQE,GAAGJ,KAAE,CAAAK,OAAGA,GAAE,QAAQ,OAAO,EAAC;AAAC,GAAE,CAAC,CAAC,EAAEH,KAAEC,IAAE;AAAC,MAAIH,MAAEE,IAAE,QAAQ,MAAM,GAAEG,KAAEH,IAAE,QAAQ,UAAU,CAAAI,OAAGA,GAAE,OAAKH,GAAE,EAAE;AAAE,SAAOE,OAAI,KAAGH,OAAGF,IAAE,OAAOK,IAAE,CAAC,GAAE,EAAC,GAAGH,KAAE,SAAQF,IAAC;AAAE,EAAC;AAA/P,IAAiQO,SAAE,eAAAC,eAAE,IAAI;AAAED,GAAE,cAAY;AAAwB,SAASE,IAAGP,KAAE;AAAC,MAAIC,SAAE,eAAAO,YAAEH,EAAC;AAAE,MAAGJ,OAAI,MAAK;AAAC,QAAIH,MAAE,IAAI,MAAM,IAAIE,GAAC,mDAAmD;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBF,KAAES,GAAE,GAAET;AAAA,EAAC;AAAC,SAAOG;AAAC;AAAC,IAAIQ,SAAE,eAAAH,eAAE,IAAI;AAAEG,GAAE,cAAY;AAA2B,SAASC,IAAGV,KAAE;AAAC,MAAIC,SAAE,eAAAO,YAAEC,EAAC;AAAE,MAAGR,OAAI,MAAK;AAAC,QAAIH,MAAE,IAAI,MAAM,IAAIE,GAAC,mDAAmD;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBF,KAAEY,GAAE,GAAEZ;AAAA,EAAC;AAAC,SAAOG;AAAC;AAAC,SAASU,IAAGX,KAAEC,IAAE;AAAC,SAAO,EAAGA,GAAE,MAAKF,KAAGC,KAAEC,EAAC;AAAC;AAAC,IAAIW,MAAG;AAAM,SAASC,IAAGb,KAAEC,IAAE;AAAC,MAAIH,MAAE,EAAE,GAAE,EAAC,IAAGK,KAAE,yBAAyBL,GAAC,IAAG,OAAMM,IAAE,cAAaU,IAAE,MAAKC,IAAE,MAAKC,KAAE,UAASC,IAAE,IAAGC,KAAE,CAACC,IAAEC,OAAID,OAAIC,IAAE,UAASC,KAAE,OAAG,GAAGC,GAAC,IAAEtB,KAAEuB,KAAEvB,GAAE,OAAOkB,MAAG,WAAS,CAACC,IAAEC,OAAI;AAAC,QAAII,KAAEN;AAAE,YAAOC,MAAG,OAAK,SAAOA,GAAEK,EAAC,QAAMJ,MAAG,OAAK,SAAOA,GAAEI,EAAC;AAAA,EAAE,IAAEN,EAAC,GAAE,CAACO,IAAEC,EAAC,QAAE,eAAAC,YAAGhB,KAAG,EAAC,SAAQ,CAAC,EAAC,CAAC,GAAEiB,MAAEH,GAAE,SAAQ,CAACI,IAAEC,EAAC,IAAEC,GAAE,GAAE,CAACC,IAAEC,EAAC,IAAEC,GAAE,GAAEC,SAAE,eAAAC,QAAE,IAAI,GAAE,IAAEb,GAAEY,IAAElC,EAAC,GAAE,CAACoC,KAAEC,GAAC,IAAE,EAAGlC,IAAEa,IAAEH,EAAC,GAAEyB,SAAE,eAAAC,SAAE,MAAIZ,IAAE,KAAK,CAAAT,OAAG,CAACA,GAAE,SAAS,QAAQ,QAAQ,GAAE,CAACS,GAAC,CAAC,GAAEa,SAAE,eAAAD,SAAE,MAAIZ,IAAE,KAAK,CAAAT,OAAGI,GAAEJ,GAAE,SAAS,QAAQ,OAAMkB,GAAC,CAAC,GAAE,CAACT,KAAES,GAAC,CAAC,GAAEK,MAAE1C,GAAE,CAAAmB,OAAG;AAAC,QAAIK;AAAE,QAAGH,MAAGE,GAAEJ,IAAEkB,GAAC,EAAE,QAAM;AAAG,QAAIjB,MAAGI,KAAEI,IAAE,KAAK,CAAAe,QAAGpB,GAAEoB,IAAE,SAAS,QAAQ,OAAMxB,EAAC,CAAC,MAAI,OAAK,SAAOK,GAAE,SAAS;AAAQ,WAAOJ,MAAG,QAAMA,GAAE,WAAS,SAAIkB,OAAG,QAAMA,IAAEnB,EAAC,GAAE;AAAA,EAAG,CAAC;AAAE,EAAAY,GAAG,EAAC,WAAUI,GAAE,SAAQ,OAAOhB,IAAE;AAAC,WAAOA,GAAE,aAAa,MAAM,MAAI,UAAQ,WAAW,gBAAcA,GAAE,aAAa,MAAM,IAAE,WAAW,cAAY,WAAW;AAAA,EAAa,GAAE,KAAKA,IAAE;AAAC,IAAAA,GAAE,aAAa,QAAO,MAAM;AAAA,EAAC,EAAC,CAAC;AAAE,MAAIY,MAAE/B,GAAE,CAAAmB,OAAG;AAAC,QAAIC,KAAEe,GAAE;AAAQ,QAAG,CAACf,GAAE;AAAO,QAAII,KAAExB,GAAGoB,EAAC,GAAEuB,MAAEf,IAAE,OAAO,CAAAgB,QAAGA,IAAE,SAAS,QAAQ,aAAW,KAAE,EAAE,IAAI,CAAAA,QAAGA,IAAE,QAAQ,OAAO;AAAE,YAAOzB,GAAE,KAAI;AAAA,MAAC,KAAKnB,IAAE;AAAM,QAAAG,GAAGgB,GAAE,aAAa;AAAE;AAAA,MAAM,KAAKnB,IAAE;AAAA,MAAU,KAAKA,IAAE;AAAQ,YAAGmB,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAE,EAAEwB,KAAE,EAAE,WAAS,EAAE,UAAU,MAAI,EAAG,SAAQ;AAAC,cAAIE,KAAEjB,IAAE,KAAK,CAAAkB,OAAGA,GAAE,QAAQ,aAAWtB,MAAG,OAAK,SAAOA,GAAE,cAAc;AAAE,UAAAqB,MAAGH,IAAEG,GAAE,SAAS,QAAQ,KAAK;AAAA,QAAC;AAAC;AAAA,MAAM,KAAK7C,IAAE;AAAA,MAAW,KAAKA,IAAE;AAAU,YAAGmB,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAE,EAAEwB,KAAE,EAAE,OAAK,EAAE,UAAU,MAAI,EAAG,SAAQ;AAAC,cAAIE,KAAEjB,IAAE,KAAK,CAAAkB,OAAGA,GAAE,QAAQ,aAAWtB,MAAG,OAAK,SAAOA,GAAE,cAAc;AAAE,UAAAqB,MAAGH,IAAEG,GAAE,SAAS,QAAQ,KAAK;AAAA,QAAC;AAAC;AAAA,MAAM,KAAK7C,IAAE;AAAM;AAAC,UAAAmB,GAAE,eAAe,GAAEA,GAAE,gBAAgB;AAAE,cAAIyB,MAAEhB,IAAE,KAAK,CAAAiB,OAAGA,GAAE,QAAQ,aAAWrB,MAAG,OAAK,SAAOA,GAAE,cAAc;AAAE,UAAAoB,OAAGF,IAAEE,IAAE,SAAS,QAAQ,KAAK;AAAA,QAAC;AAAC;AAAA,IAAK;AAAA,EAAC,CAAC,GAAEG,MAAE/C,GAAE,CAAAmB,QAAIO,GAAE,EAAC,MAAK,GAAE,GAAGP,GAAC,CAAC,GAAE,MAAIO,GAAE,EAAC,MAAK,GAAE,IAAGP,GAAE,GAAE,CAAC,EAAE,GAAEe,SAAE,eAAAM,SAAE,OAAK,EAAC,OAAMH,KAAE,aAAYE,IAAE,uBAAsBE,IAAE,UAASpB,IAAE,SAAQE,IAAE,GAAGE,GAAC,IAAG,CAACY,KAAEE,IAAEE,IAAEpB,IAAEE,IAAEE,EAAC,CAAC,GAAEuB,UAAG,eAAAR,SAAE,OAAK,EAAC,gBAAeO,KAAE,QAAOL,IAAC,IAAG,CAACK,KAAEL,GAAC,CAAC,GAAEO,MAAG,EAAC,KAAI,GAAE,IAAG9C,IAAE,MAAK,cAAa,mBAAkB0B,IAAE,oBAAmBG,IAAE,WAAUD,IAAC,GAAEmB,UAAG,eAAAV,SAAE,OAAK,EAAC,OAAMH,IAAC,IAAG,CAACA,GAAC,CAAC,GAAEnC,UAAE,eAAAkC,QAAE,IAAI,GAAEe,MAAG,EAAG;AAAE,aAAO,eAAAC,WAAG,MAAI;AAAC,IAAAlD,IAAE,WAASY,OAAI,UAAQqC,IAAG,iBAAiBjD,IAAE,SAAQ,SAAQ,MAAI;AAAC,MAAAwC,IAAE5B,EAAC;AAAA,IAAC,CAAC;AAAA,EAAC,GAAE,CAACZ,KAAEwC,GAAC,CAAC,GAAE,eAAAW,QAAE,cAAcpB,IAAE,EAAC,MAAK,yBAAwB,GAAE,eAAAoB,QAAE,cAAcvB,IAAE,EAAC,MAAK,mBAAkB,GAAE,eAAAuB,QAAE,cAAc5C,GAAE,UAAS,EAAC,OAAMuC,IAAE,GAAE,eAAAK,QAAE,cAAchD,GAAE,UAAS,EAAC,OAAM6B,GAAC,GAAElB,OAAG,QAAMqB,OAAG,QAAM,EAAG,EAAC,CAACrB,GAAC,GAAEqB,IAAC,CAAC,EAAE,IAAI,CAAC,CAAClB,IAAEC,EAAC,GAAEI,OAAI,eAAA6B,QAAE,cAAcT,IAAG,EAAC,UAASN,GAAG,QAAO,KAAId,OAAI,IAAE,CAAAmB,QAAG;AAAC,QAAIC;AAAE,IAAA1C,IAAE,WAAS0C,MAAED,OAAG,OAAK,SAAOA,IAAE,QAAQ,MAAM,MAAI,OAAKC,MAAE;AAAA,EAAI,IAAE,QAAO,GAAG,EAAG,EAAC,KAAIzB,IAAE,IAAG,SAAQ,MAAK,SAAQ,SAAQC,MAAG,MAAK,QAAO,MAAG,UAAS,MAAG,MAAKL,IAAE,UAASM,IAAE,MAAKF,IAAE,OAAMC,GAAC,CAAC,EAAC,CAAC,CAAC,GAAE,EAAG,EAAC,UAAS6B,KAAG,YAAW3B,IAAE,MAAK4B,KAAG,YAAWtC,KAAG,MAAK,aAAY,CAAC,CAAC,CAAC,CAAC,CAAC;AAAC;AAAC,IAAI0C,OAAI,CAAAxD,SAAIA,IAAEA,IAAE,QAAM,CAAC,IAAE,SAAQA,IAAEA,IAAE,SAAO,CAAC,IAAE,UAASA,MAAIwD,OAAI,CAAC,CAAC;AAAE,IAAIC,MAAG;AAAM,SAASC,IAAGxD,KAAEC,IAAE;AAAC,MAAI8B;AAAE,MAAIjC,MAAE,EAAE,GAAE,EAAC,IAAGK,KAAE,gCAAgCL,GAAC,IAAG,OAAMM,IAAE,UAASU,KAAE,OAAG,GAAGC,GAAC,IAAEf,KAAEgB,UAAE,eAAAoB,QAAE,IAAI,GAAEnB,KAAEM,GAAEP,KAAEf,EAAC,GAAE,CAACiB,IAAEG,EAAC,IAAEU,GAAE,GAAE,CAACT,IAAEC,EAAC,IAAEW,GAAE,GAAE,EAAC,SAAQT,IAAE,YAAWC,IAAE,SAAQE,IAAC,IAAEmB,IAAG,CAAC,GAAElB,KAAES,GAAG,EAAC,OAAMlC,IAAE,UAASU,GAAC,CAAC,GAAEgB,KAAEvB,IAAG,mBAAmB,GAAEyB,KAAEtB,IAAG,mBAAmB;AAAE,IAAG,MAAIsB,GAAE,eAAe,EAAC,IAAG7B,IAAE,SAAQa,KAAE,UAASa,GAAC,CAAC,GAAE,CAAC1B,IAAE6B,IAAEhB,KAAEa,EAAC,CAAC;AAAE,MAAII,KAAEjC,GAAE,CAAA+C,QAAG;AAAC,QAAIb;AAAE,QAAGjC,GAAE8C,IAAE,aAAa,EAAE,QAAOA,IAAE,eAAe;AAAE,IAAAf,GAAE,OAAO5B,EAAC,MAAIqB,GAAE,CAAC,IAAGS,KAAElB,IAAE,YAAU,QAAMkB,GAAE,MAAM;AAAA,EAAE,CAAC,GAAEC,KAAEnC,GAAE,CAAA+C,QAAG;AAAC,QAAG9C,GAAE8C,IAAE,aAAa,EAAE,QAAOA,IAAE,eAAe;AAAE,IAAAtB,GAAE,CAAC;AAAA,EAAC,CAAC,GAAE,IAAEzB,GAAE,MAAI0B,GAAE,CAAC,CAAC,GAAEW,QAAIN,MAAED,GAAE,gBAAc,OAAK,SAAOC,IAAE,QAAM5B,IAAEmC,MAAER,GAAE,YAAUhB,IAAEyB,KAAET,GAAE,QAAQA,GAAE,OAAM1B,EAAC,GAAEqC,KAAE,EAAC,KAAIxB,IAAE,IAAGd,IAAE,MAAK,SAAQ,gBAAeoC,KAAE,SAAO,SAAQ,mBAAkBrB,IAAE,oBAAmBI,IAAE,iBAAgBgB,MAAE,OAAG,QAAO,WAAU,MAAIA,MAAE,KAAGC,MAAG,CAACT,GAAE,yBAAuBO,MAAE,IAAE,IAAI,GAAE,SAAQC,MAAE,SAAOL,IAAE,SAAQK,MAAE,SAAOH,IAAE,QAAOG,MAAE,SAAO,EAAC,GAAEI,UAAE,eAAAF,SAAE,OAAK,EAAC,SAAQD,IAAE,UAASD,KAAE,QAAOV,IAAE,CAAC,EAAC,IAAG,CAACW,IAAED,KAAEV,GAAC,CAAC;AAAE,SAAO,eAAAyB,QAAE,cAAc9B,IAAE,EAAC,MAAK,yBAAwB,GAAE,eAAA8B,QAAE,cAAchC,IAAE,EAAC,MAAK,mBAAkB,GAAE,EAAG,EAAC,UAASoB,IAAE,YAAW1B,IAAE,MAAK2B,KAAE,YAAWa,KAAG,MAAK,oBAAmB,CAAC,CAAC,CAAC;AAAC;AAAC,IAAIE,MAAG,EAAG5C,GAAE;AAAZ,IAAc6C,MAAG,EAAGF,GAAE;AAAtB,IAAwBG,MAAG,OAAO,OAAOF,KAAG,EAAC,QAAOC,KAAG,OAAMrD,IAAG,aAAY,EAAE,CAAC;;;AGA58L,IAAAuD,iBAAqH;AAA0yB,IAAIC,SAAE,eAAAC,eAAE,IAAI;AAAED,GAAE,cAAY;AAAe,IAAIE,MAAG,eAAAC;AAAE,SAASC,IAAGC,IAAE;AAAC,MAAIC;AAAE,MAAG,CAACC,IAAEC,EAAC,QAAE,eAAAC,UAAE,IAAI,GAAE,CAACC,KAAEC,EAAC,IAAEC,GAAE,GAAE,CAACC,KAAEC,EAAC,IAAEC,GAAE,GAAEC,UAAE,eAAAC,SAAE,OAAK,EAAC,QAAOV,IAAE,WAAUC,IAAE,YAAWE,KAAE,aAAYG,IAAC,IAAG,CAACN,IAAEC,IAAEE,KAAEG,GAAC,CAAC,GAAEK,MAAE,CAAC,GAAEC,KAAEd;AAAE,SAAO,eAAAe,QAAE,cAAcN,IAAE,EAAC,MAAK,qBAAoB,GAAE,eAAAM,QAAE,cAAcT,IAAE,EAAC,MAAK,gBAAe,OAAM,EAAC,UAASL,MAAEU,IAAE,WAAS,OAAK,SAAOV,IAAE,IAAG,QAAQe,KAAE;AAAC,IAAAd,OAAIc,IAAE,cAAc,YAAU,WAASA,IAAE,eAAe,GAAEd,GAAE,MAAM,GAAEA,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC;AAAA,EAAE,EAAC,EAAC,GAAE,eAAAa,QAAE,cAAcpB,GAAE,UAAS,EAAC,OAAMgB,IAAC,GAAE,EAAE,EAAC,UAASE,KAAE,YAAWC,IAAE,YAAWjB,KAAG,MAAK,eAAc,CAAC,CAAC,CAAC,CAAC;AAAC;AAAC,IAAIoB,MAAG;AAAS,SAASC,IAAGlB,IAAEE,IAAE;AAAC,MAAIiB;AAAE,MAAIhB,KAAE,EAAE,GAAE,EAAC,IAAGE,MAAE,qBAAqBF,EAAC,IAAG,SAAQG,IAAE,gBAAeE,MAAE,OAAG,UAASC,IAAE,UAASE,MAAE,OAAG,MAAKE,KAAE,OAAMC,IAAE,MAAKb,KAAE,GAAGe,IAAC,IAAEhB,IAAEoB,UAAE,eAAAb,YAAEZ,EAAC,GAAE0B,UAAE,eAAAC,QAAE,IAAI,GAAEC,KAAET,GAAEO,KAAEnB,IAAEkB,QAAI,OAAK,OAAKA,IAAE,SAAS,GAAE,CAACI,IAAEC,GAAC,IAAE,EAAEnB,IAAEG,IAAED,GAAC,GAAEE,KAAEF,GAAE,MAAIiB,OAAG,OAAK,SAAOA,IAAE,CAACD,EAAC,CAAC,GAAEE,KAAElB,GAAE,CAAAmB,OAAG;AAAC,QAAG3B,GAAE2B,GAAE,aAAa,EAAE,QAAOA,GAAE,eAAe;AAAE,IAAAA,GAAE,eAAe,GAAEjB,GAAE;AAAA,EAAC,CAAC,GAAEkB,KAAEpB,GAAE,CAAAmB,OAAG;AAAC,IAAAA,GAAE,QAAMnB,IAAE,SAAOmB,GAAE,eAAe,GAAEjB,GAAE,KAAGiB,GAAE,QAAMnB,IAAE,SAAOL,GAAEwB,GAAE,aAAa;AAAA,EAAC,CAAC,GAAEE,KAAErB,GAAE,CAAAmB,OAAGA,GAAE,eAAe,CAAC,GAAEG,SAAE,eAAAlB,SAAE,OAAK,EAAC,SAAQY,GAAC,IAAG,CAACA,EAAC,CAAC,GAAEO,KAAE,EAAC,IAAG1B,KAAE,KAAIkB,IAAE,MAAK,UAAS,MAAKjB,GAAEN,IAAEqB,GAAC,GAAE,UAASrB,GAAE,aAAW,KAAG,KAAGmB,KAAEnB,GAAE,aAAW,OAAKmB,KAAE,GAAE,gBAAeK,IAAE,mBAAkBJ,OAAG,OAAK,SAAOA,IAAE,YAAW,oBAAmBA,OAAG,OAAK,SAAOA,IAAE,aAAY,UAAST,KAAE,SAAQe,IAAE,SAAQE,IAAE,YAAWC,GAAC,GAAEG,KAAE,EAAE;AAAE,aAAO,eAAAC,WAAE,MAAI;AAAC,QAAIC;AAAE,QAAIP,MAAGO,KAAEb,IAAE,YAAU,OAAK,SAAOa,GAAE,QAAQ,MAAM;AAAE,IAAAP,MAAGnB,QAAI,UAAQwB,GAAE,iBAAiBL,IAAE,SAAQ,MAAI;AAAC,MAAAF,IAAEjB,GAAC;AAAA,IAAC,CAAC;AAAA,EAAC,GAAE,CAACa,KAAEI,GAAC,CAAC,GAAE,eAAAV,QAAE,cAAc,eAAAA,QAAE,UAAS,MAAKF,OAAG,QAAMW,MAAG,eAAAT,QAAE,cAAcd,IAAE,EAAC,UAASwB,GAAE,QAAO,GAAG,EAAE,EAAC,IAAG,SAAQ,MAAK,YAAW,QAAO,MAAG,UAAS,MAAG,UAASd,KAAE,MAAKV,KAAE,SAAQuB,IAAE,MAAKX,KAAE,OAAMC,GAAC,CAAC,EAAC,CAAC,GAAE,EAAE,EAAC,UAASiB,IAAE,YAAWf,KAAE,MAAKc,IAAE,YAAWb,KAAG,MAAK,SAAQ,CAAC,CAAC;AAAC;AAAC,IAAIkB,MAAG,EAAEjB,GAAE;AAAX,IAAakB,MAAGrC;AAAhB,IAAmBsC,MAAG,OAAO,OAAOF,KAAG,EAAC,OAAMC,KAAG,OAAME,IAAE,aAAY,EAAC,CAAC;;;ACArmF,IAAAC,iBAA0G;;;ACA1G,IAAAC,iBAA4B;AAAqH,SAASC,GAAE,EAAC,SAAQC,GAAC,GAAE;AAAC,MAAG,CAACC,IAAEC,GAAC,QAAE,eAAAC,UAAE,IAAE,GAAEC,MAAEC,GAAE;AAAE,SAAOJ,KAAE,eAAAK,QAAE,cAAcF,IAAE,EAAC,IAAG,UAAS,MAAK,UAAS,UAASE,GAAE,WAAU,SAAQ,CAAAC,QAAG;AAAC,IAAAA,IAAE,eAAe;AAAE,QAAIC,IAAEC,KAAE;AAAG,aAASC,MAAG;AAAC,UAAGD,QAAK,GAAE;AAAC,QAAAD,MAAG,qBAAqBA,EAAC;AAAE;AAAA,MAAM;AAAC,UAAGR,GAAE,GAAE;AAAC,YAAG,qBAAqBQ,EAAC,GAAE,CAACJ,IAAE,QAAQ;AAAO,QAAAF,IAAE,KAAE;AAAE;AAAA,MAAM;AAAC,MAAAM,KAAE,sBAAsBE,GAAC;AAAA,IAAC;AAAC,IAAAF,KAAE,sBAAsBE,GAAC;AAAA,EAAC,EAAC,CAAC,IAAE;AAAI;;;ACA5e,IAAAC,KAAgB;AAAQ,IAAMC,MAAI,iBAAc,IAAI;AAAE,SAASC,KAAG;AAAC,SAAM,EAAC,QAAO,oBAAI,OAAI,IAAIC,IAAEC,KAAE;AAAC,QAAIC;AAAE,QAAIC,KAAE,KAAK,OAAO,IAAIH,EAAC;AAAE,IAAAG,OAAIA,KAAE,oBAAI,OAAI,KAAK,OAAO,IAAIH,IAAEG,EAAC;AAAG,QAAIC,OAAGF,MAAEC,GAAE,IAAIF,GAAC,MAAI,OAAKC,MAAE;AAAE,IAAAC,GAAE,IAAIF,KAAEG,MAAE,CAAC;AAAE,QAAIC,MAAE,MAAM,KAAKF,GAAE,KAAK,CAAC,EAAE,QAAQF,GAAC;AAAE,aAASK,KAAG;AAAC,UAAIC,MAAEJ,GAAE,IAAIF,GAAC;AAAE,MAAAM,MAAE,IAAEJ,GAAE,IAAIF,KAAEM,MAAE,CAAC,IAAEJ,GAAE,OAAOF,GAAC;AAAA,IAAC;AAAC,WAAM,CAACI,KAAEC,EAAC;AAAA,EAAC,EAAC;AAAC;AAAC,SAASE,GAAE,EAAC,UAASR,GAAC,GAAE;AAAC,MAAIC,MAAI,UAAOF,GAAE,CAAC;AAAE,SAAS,iBAAcD,IAAE,UAAS,EAAC,OAAMG,IAAC,GAAED,EAAC;AAAC;AAAC,SAASS,IAAET,IAAE;AAAC,MAAIC,MAAI,cAAWH,GAAC;AAAE,MAAG,CAACG,IAAE,OAAM,IAAI,MAAM,sDAAsD;AAAE,MAAIE,KAAEO,IAAE,GAAE,CAACN,KAAEC,GAAC,IAAEJ,IAAE,QAAQ,IAAID,IAAEG,EAAC;AAAE,SAAS,aAAU,MAAIE,KAAE,CAAC,CAAC,GAAED;AAAC;AAAC,SAASM,MAAG;AAAC,MAAIN,KAAEC,KAAEC;AAAE,MAAIN,MAAGM,MAAGD,OAAGD,MAAI,0DAAqD,OAAK,SAAOA,IAAE,sBAAoB,OAAK,SAAOC,IAAE,YAAU,OAAKC,KAAE;AAAK,MAAG,CAACN,GAAE,QAAO,OAAO;AAAE,MAAIC,MAAE,CAAC,GAAEE,KAAEH;AAAE,SAAKG,KAAG,CAAAF,IAAE,KAAKE,GAAE,KAAK,GAAEA,KAAEA,GAAE;AAAO,SAAM,OAAKF,IAAE,KAAK,GAAG;AAAC;;;AFAsQ,IAAIU,OAAI,CAAAC,SAAIA,IAAEA,IAAE,WAAS,CAAC,IAAE,YAAWA,IAAEA,IAAE,YAAU,CAAC,IAAE,aAAYA,MAAID,OAAI,CAAC,CAAC;AAA9E,IAAgFE,OAAI,CAAAC,SAAIA,IAAEA,IAAE,OAAK,EAAE,IAAE,QAAOA,IAAEA,IAAE,QAAM,CAAC,IAAE,SAAQA,IAAEA,IAAE,UAAQ,CAAC,IAAE,WAAUA,MAAID,OAAI,CAAC,CAAC;AAApK,IAAsKE,OAAI,CAAAC,SAAIA,IAAEA,IAAE,mBAAiB,CAAC,IAAE,oBAAmBA,IAAEA,IAAE,cAAY,CAAC,IAAE,eAAcA,IAAEA,IAAE,gBAAc,CAAC,IAAE,iBAAgBA,IAAEA,IAAE,gBAAc,CAAC,IAAE,iBAAgBA,IAAEA,IAAE,kBAAgB,CAAC,IAAE,mBAAkBA,MAAID,OAAI,CAAC,CAAC;AAAE,IAAIE,MAAG,EAAC,CAAC,CAAC,EAAEC,IAAEC,IAAE;AAAC,MAAIC;AAAE,MAAIR,MAAES,GAAEH,GAAE,MAAK,CAAAI,QAAGA,IAAE,OAAO,GAAER,MAAEO,GAAEH,GAAE,QAAO,CAAAI,QAAGA,IAAE,OAAO,GAAEC,MAAEX,IAAE,OAAO,CAAAU,QAAG;AAAC,QAAIE;AAAE,WAAM,GAAGA,KAAEF,IAAE,YAAU,QAAME,GAAE,aAAa,UAAU;AAAA,EAAE,CAAC,GAAER,MAAE,EAAC,GAAGE,IAAE,MAAKN,KAAE,QAAOE,IAAC;AAAE,MAAGK,GAAE,QAAM,KAAGA,GAAE,QAAMP,IAAE,SAAO,GAAE;AAAC,QAAIU,MAAE,EAAE,KAAK,KAAKH,GAAE,QAAMD,GAAE,aAAa,GAAE,EAAC,CAAC,EAAE,GAAE,MAAI,GAAE,CAAC,CAAC,GAAE,MAAI,EAAE,KAAK,KAAKC,GAAE,KAAK,GAAE,EAAC,CAAC,EAAE,GAAE,MAAI,GAAE,CAAC,CAAC,GAAE,MAAI,GAAE,CAAC,CAAC,GAAE,MAAI,EAAC,CAAC,GAAE,CAAC,CAAC,GAAE,MAAI,EAAC,CAAC;AAAE,QAAGI,IAAE,WAAS,EAAE,QAAOP;AAAE,QAAIQ,KAAE,EAAEF,KAAE,EAAC,CAAC,CAAC,GAAE,MAAIV,IAAE,QAAQW,IAAE,CAAC,CAAC,GAAE,CAAC,CAAC,GAAE,MAAIX,IAAE,QAAQW,IAAEA,IAAE,SAAO,CAAC,CAAC,EAAC,CAAC;AAAE,WAAM,EAAC,GAAGP,KAAE,eAAcQ,OAAI,KAAGN,GAAE,gBAAcM,GAAC;AAAA,EAAC;AAAC,MAAIC,KAAEb,IAAE,MAAM,GAAEO,GAAE,KAAK,GAAEO,MAAE,CAAC,GAAGd,IAAE,MAAMO,GAAE,KAAK,GAAE,GAAGM,EAAC,EAAE,KAAK,CAAAH,QAAGC,IAAE,SAASD,GAAC,CAAC;AAAE,MAAG,CAACI,IAAE,QAAOV;AAAE,MAAIW,MAAGP,KAAER,IAAE,QAAQc,GAAC,MAAI,OAAKN,KAAEF,GAAE;AAAc,SAAOS,OAAI,OAAKA,KAAET,GAAE,gBAAe,EAAC,GAAGF,KAAE,eAAcW,GAAC;AAAC,GAAE,CAAC,CAAC,EAAET,IAAEC,IAAE;AAAC,MAAGD,GAAE,KAAK,SAASC,GAAE,GAAG,EAAE,QAAOD;AAAE,MAAIN,MAAEM,GAAE,KAAKA,GAAE,aAAa,GAAEJ,MAAEO,GAAE,CAAC,GAAGH,GAAE,MAAKC,GAAE,GAAG,GAAE,CAAAH,QAAGA,IAAE,OAAO,GAAEO,MAAEL,GAAE;AAAc,SAAOA,GAAE,KAAK,QAAQ,iBAAeK,MAAET,IAAE,QAAQF,GAAC,GAAEW,QAAI,OAAKA,MAAEL,GAAE,iBAAgB,EAAC,GAAGA,IAAE,MAAKJ,KAAE,eAAcS,IAAC;AAAC,GAAE,CAAC,CAAC,EAAEL,IAAEC,IAAE;AAAC,SAAM,EAAC,GAAGD,IAAE,MAAKA,GAAE,KAAK,OAAO,CAAAN,QAAGA,QAAIO,GAAE,GAAG,EAAC;AAAC,GAAE,CAAC,CAAC,EAAED,IAAEC,IAAE;AAAC,SAAOD,GAAE,OAAO,SAASC,GAAE,KAAK,IAAED,KAAE,EAAC,GAAGA,IAAE,QAAOG,GAAE,CAAC,GAAGH,GAAE,QAAOC,GAAE,KAAK,GAAE,CAAAP,QAAGA,IAAE,OAAO,EAAC;AAAC,GAAE,CAAC,CAAC,EAAEM,IAAEC,IAAE;AAAC,SAAM,EAAC,GAAGD,IAAE,QAAOA,GAAE,OAAO,OAAO,CAAAN,QAAGA,QAAIO,GAAE,KAAK,EAAC;AAAC,EAAC;AAA5nC,IAA8nCS,SAAE,eAAAC,eAAE,IAAI;AAAED,GAAE,cAAY;AAAkB,SAASE,GAAEZ,IAAE;AAAC,MAAIC,SAAE,eAAAY,YAAEH,EAAC;AAAE,MAAGT,OAAI,MAAK;AAAC,QAAIP,MAAE,IAAI,MAAM,IAAIM,EAAC,kDAAkD;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBN,KAAEkB,EAAC,GAAElB;AAAA,EAAC;AAAC,SAAOO;AAAC;AAAC,IAAIa,SAAE,eAAAH,eAAE,IAAI;AAAEG,GAAE,cAAY;AAAqB,SAASC,GAAEf,IAAE;AAAC,MAAIC,SAAE,eAAAY,YAAEC,EAAC;AAAE,MAAGb,OAAI,MAAK;AAAC,QAAIP,MAAE,IAAI,MAAM,IAAIM,EAAC,kDAAkD;AAAE,UAAM,MAAM,qBAAmB,MAAM,kBAAkBN,KAAEqB,EAAC,GAAErB;AAAA,EAAC;AAAC,SAAOO;AAAC;AAAC,SAASe,IAAGhB,IAAEC,IAAE;AAAC,SAAO,EAAEA,GAAE,MAAKF,KAAGC,IAAEC,EAAC;AAAC;AAAC,IAAIgB,MAAG,eAAAC;AAAG,SAASC,IAAGnB,IAAEC,IAAE;AAAC,MAAG,EAAC,cAAaP,MAAE,GAAE,UAASE,MAAE,OAAG,QAAOS,MAAE,OAAG,UAASP,KAAE,eAAcS,KAAE,MAAK,GAAGa,GAAC,IAAEpB;AAAE,QAAMQ,MAAEZ,MAAE,aAAW,cAAaa,KAAEJ,MAAE,WAAS;AAAO,MAAIH,KAAEK,OAAI,MAAKH,MAAEiB,GAAE,EAAC,cAAanB,GAAC,CAAC,GAAEI,KAAEgB,GAAErB,EAAC,GAAE,CAACsB,KAAEC,GAAC,QAAE,eAAAC,YAAGT,KAAG,EAAC,MAAKZ,KAAE,eAAcG,MAAG,OAAKA,KAAEb,KAAE,MAAK,CAAC,GAAE,QAAO,CAAC,EAAC,CAAC,GAAEgC,SAAE,eAAAvB,SAAE,OAAK,EAAC,eAAcoB,IAAE,cAAa,IAAG,CAACA,IAAE,aAAa,CAAC,GAAEI,KAAEN,GAAEvB,QAAI,MAAI;AAAA,EAAC,EAAE,GAAE8B,KAAEP,GAAEE,IAAE,IAAI,GAAEM,SAAE,eAAA1B,SAAE,OAAK,EAAC,aAAYK,KAAE,YAAWC,IAAE,GAAGc,IAAC,IAAG,CAACf,KAAEC,IAAEc,GAAC,CAAC,GAAEO,KAAEzB,GAAE,CAAAgB,SAAIG,IAAE,EAAC,MAAK,GAAE,KAAIH,IAAC,CAAC,GAAE,MAAIG,IAAE,EAAC,MAAK,GAAE,KAAIH,IAAC,CAAC,EAAE,GAAEU,MAAE1B,GAAE,CAAAgB,SAAIG,IAAE,EAAC,MAAK,GAAE,OAAMH,IAAC,CAAC,GAAE,MAAIG,IAAE,EAAC,MAAK,GAAE,OAAMH,IAAC,CAAC,EAAE,GAAEW,KAAE3B,GAAE,CAAAgB,QAAG;AAAC,IAAAY,GAAE,YAAUZ,OAAGM,GAAE,QAAQN,GAAC,GAAEnB,MAAGsB,IAAE,EAAC,MAAK,GAAE,OAAMH,IAAC,CAAC;AAAA,EAAC,CAAC,GAAEY,KAAEZ,GAAEnB,KAAEF,GAAE,gBAAcuB,IAAE,aAAa,GAAE,QAAE,eAAApB,SAAE,OAAK,EAAC,aAAY2B,IAAE,eAAcC,KAAE,QAAOC,GAAC,IAAG,CAAC,CAAC;AAAE,IAAE,MAAI;AAAC,IAAAR,IAAE,EAAC,MAAK,GAAE,OAAMjB,MAAG,OAAKA,KAAEb,IAAC,CAAC;AAAA,EAAC,GAAE,CAACa,EAAC,CAAC,GAAE,EAAE,MAAI;AAAC,QAAG0B,GAAE,YAAU,UAAQV,IAAE,KAAK,UAAQ,EAAE;AAAO,QAAIF,MAAElB,GAAEoB,IAAE,MAAK,CAAAW,QAAGA,IAAE,OAAO;AAAE,IAAAb,IAAE,KAAK,CAACa,KAAEC,OAAIZ,IAAE,KAAKY,EAAC,MAAID,GAAC,KAAGF,GAAEX,IAAE,QAAQE,IAAE,KAAKU,GAAE,OAAO,CAAC,CAAC;AAAA,EAAC,CAAC;AAAE,MAAIG,KAAE,EAAC,KAAI9B,GAAC;AAAE,SAAO,eAAA+B,QAAE,cAAcA,IAAG,MAAK,eAAAA,QAAE,cAAcvB,GAAE,UAAS,EAAC,OAAM,EAAC,GAAE,eAAAuB,QAAE,cAAc3B,GAAE,UAAS,EAAC,OAAMmB,GAAC,GAAEA,GAAE,KAAK,UAAQ,KAAG,eAAAQ,QAAE,cAAc5B,IAAG,EAAC,SAAQ,MAAI;AAAC,QAAIY,KAAEiB;AAAE,aAAQJ,OAAKN,GAAE,QAAQ,OAAKP,MAAEa,IAAE,YAAU,OAAK,SAAOb,IAAE,cAAY,EAAE,SAAOiB,KAAEJ,IAAE,YAAU,QAAMI,GAAE,MAAM,GAAE;AAAG,WAAM;AAAA,EAAE,EAAC,CAAC,GAAE,EAAE,EAAC,UAASF,IAAE,YAAWhB,IAAE,MAAKM,IAAE,YAAWT,KAAG,MAAK,OAAM,CAAC,CAAC,CAAC,CAAC;AAAC;AAAC,IAAIsB,MAAG;AAAM,SAASC,IAAGxC,IAAEC,IAAE;AAAC,MAAG,EAAC,aAAYP,KAAE,eAAcE,IAAC,IAAEgB,GAAE,UAAU,GAAEP,MAAEiB,GAAErB,EAAC;AAAE,SAAO,EAAE,EAAC,UAAS,EAAC,KAAII,KAAE,MAAK,WAAU,oBAAmBX,IAAC,GAAE,YAAWM,IAAE,MAAK,EAAC,eAAcJ,IAAC,GAAE,YAAW2C,KAAG,MAAK,YAAW,CAAC;AAAC;AAAC,IAAIE,MAAG;AAAS,SAASC,IAAG1C,IAAEC,IAAE;AAAC,MAAImC,IAAEf;AAAE,MAAI3B,MAAE,EAAE,GAAE,EAAC,IAAGE,MAAE,uBAAuBF,GAAC,IAAG,GAAGW,IAAC,IAAEL,IAAE,EAAC,aAAYF,KAAE,YAAWS,IAAE,eAAca,IAAE,MAAKZ,KAAE,QAAOC,GAAC,IAAEG,GAAE,KAAK,GAAEV,KAAEa,GAAE,KAAK,GAAEX,MAAEQ,GAAE,KAAK,GAAEN,SAAE,eAAAqC,QAAE,IAAI,GAAEpB,MAAED,GAAEhB,IAAEL,EAAC;AAAE,IAAE,MAAIC,GAAE,YAAYI,EAAC,GAAE,CAACJ,IAAEI,EAAC,CAAC;AAAE,MAAIkB,MAAEU,IAAG,MAAM,GAAER,KAAElB,IAAE,QAAQF,EAAC;AAAE,EAAAoB,OAAI,OAAKA,KAAEF;AAAG,MAAIG,KAAED,OAAIN,IAAEQ,KAAEvB,GAAE,CAAAiC,OAAG;AAAC,QAAIH;AAAE,QAAID,MAAEI,GAAE;AAAE,QAAGJ,QAAI,EAAE,WAAS3B,OAAI,QAAO;AAAC,UAAIqC,MAAGT,KAAE9B,GAAGC,EAAC,MAAI,OAAK,SAAO6B,GAAE,eAAcU,KAAEzC,IAAE,KAAK,UAAU,CAAA0C,QAAIA,IAAG,YAAUF,EAAC;AAAE,MAAAC,OAAI,MAAI3C,GAAE,OAAO2C,EAAC;AAAA,IAAC;AAAC,WAAOX;AAAA,EAAC,CAAC,GAAEL,KAAExB,GAAE,CAAAiC,OAAG;AAAC,QAAIJ,MAAE1B,IAAE,IAAI,CAAAoC,OAAGA,GAAE,OAAO,EAAE,OAAO,OAAO;AAAE,QAAGN,GAAE,QAAMjC,IAAE,SAAOiC,GAAE,QAAMjC,IAAE,OAAM;AAAC,MAAAiC,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEpC,GAAE,OAAOwB,EAAC;AAAE;AAAA,IAAM;AAAC,YAAOY,GAAE,KAAI;AAAA,MAAC,KAAKjC,IAAE;AAAA,MAAK,KAAKA,IAAE;AAAO,eAAOiC,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEV,GAAE,MAAI,EAAEM,KAAE,EAAE,KAAK,CAAC;AAAA,MAAE,KAAK7B,IAAE;AAAA,MAAI,KAAKA,IAAE;AAAS,eAAOiC,GAAE,eAAe,GAAEA,GAAE,gBAAgB,GAAEV,GAAE,MAAI,EAAEM,KAAE,EAAE,IAAI,CAAC;AAAA,IAAC;AAAC,QAAGN,GAAE,MAAI,EAAE9B,KAAE,EAAC,WAAU;AAAC,aAAOwC,GAAE,QAAMjC,IAAE,UAAQ,EAAE6B,KAAE,EAAE,WAAS,EAAE,UAAU,IAAEI,GAAE,QAAMjC,IAAE,YAAU,EAAE6B,KAAE,EAAE,OAAK,EAAE,UAAU,IAAE,EAAE;AAAA,IAAK,GAAE,aAAY;AAAC,aAAOI,GAAE,QAAMjC,IAAE,YAAU,EAAE6B,KAAE,EAAE,WAAS,EAAE,UAAU,IAAEI,GAAE,QAAMjC,IAAE,aAAW,EAAE6B,KAAE,EAAE,OAAK,EAAE,UAAU,IAAE,EAAE;AAAA,IAAK,EAAC,CAAC,CAAC,MAAI,EAAE,QAAQ,QAAOI,GAAE,eAAe;AAAA,EAAC,CAAC,GAAER,SAAE,eAAAa,QAAE,KAAE,GAAEZ,MAAE1B,GAAE,MAAI;AAAC,QAAIiC;AAAE,IAAAR,GAAE,YAAUA,GAAE,UAAQ,OAAIQ,KAAEhC,GAAE,YAAU,QAAMgC,GAAE,MAAM,EAAC,eAAc,KAAE,CAAC,GAAEpC,GAAE,OAAOwB,EAAC,GAAEhC,GAAG,MAAI;AAAC,MAAAoC,GAAE,UAAQ;AAAA,IAAE,CAAC;AAAA,EAAE,CAAC,GAAEE,KAAE3B,GAAE,CAAAiC,OAAG;AAAC,IAAAA,GAAE,eAAe;AAAA,EAAC,CAAC,GAAEL,SAAE,eAAA9B,SAAE,MAAI;AAAC,QAAImC;AAAE,WAAM,EAAC,UAASX,IAAE,WAAUW,KAAEtC,GAAE,aAAW,OAAKsC,KAAE,MAAE;AAAA,EAAC,GAAE,CAACX,IAAE3B,GAAE,QAAQ,CAAC,GAAE,IAAE,EAAC,KAAIuB,KAAE,WAAUM,IAAE,aAAYG,IAAE,SAAQD,KAAE,IAAGnC,KAAE,MAAK,OAAM,MAAKW,GAAGP,IAAEM,EAAC,GAAE,kBAAiBe,OAAGe,KAAE3B,GAAEiB,EAAC,MAAI,OAAK,SAAOU,GAAE,YAAU,OAAK,SAAOf,IAAE,IAAG,iBAAgBM,IAAE,UAASA,KAAE,IAAE,GAAE;AAAE,SAAO,EAAE,EAAC,UAAS,GAAE,YAAWtB,KAAE,MAAK4B,IAAE,YAAWQ,KAAG,MAAK,WAAU,CAAC;AAAC;AAAC,IAAIM,MAAG;AAAM,SAASC,IAAGhD,IAAEC,IAAE;AAAC,MAAG,EAAC,eAAcP,IAAC,IAAEkB,GAAE,YAAY,GAAEhB,MAAE0B,GAAErB,EAAC,GAAEI,UAAE,eAAAF,SAAE,OAAK,EAAC,eAAcT,IAAC,IAAG,CAACA,GAAC,CAAC;AAAE,SAAO,EAAE,EAAC,UAAS,EAAC,KAAIE,IAAC,GAAE,YAAWI,IAAE,MAAKK,KAAE,YAAW0C,KAAG,MAAK,cAAa,CAAC;AAAC;AAAC,IAAIE,MAAG;AAAP,IAAaC,MAAGd,GAAE,iBAAeA,GAAE;AAAO,SAASe,IAAGnD,IAAEC,IAAE;AAAC,MAAI2B,IAAEC,IAAEC,IAAEC;AAAE,MAAIrC,MAAE,EAAE,GAAE,EAAC,IAAGE,MAAE,yBAAyBF,GAAC,IAAG,UAASW,MAAE,GAAE,GAAGP,IAAC,IAAEE,IAAE,EAAC,eAAcO,IAAE,MAAKa,IAAE,QAAOZ,IAAC,IAAEI,GAAE,WAAW,GAAEH,KAAEM,GAAE,WAAW,GAAEb,SAAE,eAAAyC,QAAE,IAAI,GAAEvC,MAAEkB,GAAEpB,IAAED,EAAC;AAAE,IAAE,MAAIQ,GAAE,cAAcP,EAAC,GAAE,CAACO,IAAEP,IAAEN,GAAC,CAAC;AAAE,MAAIU,KAAE4B,IAAG,QAAQ,GAAEX,MAAEf,IAAE,QAAQN,EAAC;AAAE,EAAAqB,QAAI,OAAKA,MAAEjB;AAAG,MAAIkB,MAAED,QAAIhB,IAAEmB,SAAE,eAAAvB,SAAE,OAAK,EAAC,UAASqB,IAAC,IAAG,CAACA,GAAC,CAAC,GAAEG,KAAE,EAAC,KAAIvB,KAAE,IAAGR,KAAE,MAAK,YAAW,oBAAmBiC,MAAGD,KAAER,GAAEG,GAAC,MAAI,OAAK,SAAOK,GAAE,YAAU,OAAK,SAAOC,GAAE,IAAG,UAASL,MAAEnB,MAAE,GAAE;AAAE,SAAM,CAACmB,SAAKM,KAAEhC,IAAE,YAAU,QAAMgC,OAAI,GAAGC,MAAEjC,IAAE,WAAS,QAAMiC,OAAG,eAAAM,QAAE,cAAcd,IAAG,EAAC,IAAG,QAAO,eAAc,QAAO,GAAGI,GAAC,CAAC,IAAE,EAAE,EAAC,UAASA,IAAE,YAAW7B,KAAE,MAAK4B,IAAE,YAAWuB,KAAG,UAASC,KAAG,SAAQ1B,KAAE,MAAK,aAAY,CAAC;AAAC;AAAC,IAAI4B,MAAG,EAAEV,GAAE;AAAX,IAAaW,MAAG,EAAElC,GAAE;AAApB,IAAsBmC,MAAG,EAAEd,GAAE;AAA7B,IAA+Be,MAAG,EAAEP,GAAE;AAAtC,IAAwCQ,MAAG,EAAEL,GAAE;AAA/C,IAAiDM,MAAG,OAAO,OAAOL,KAAG,EAAC,OAAMC,KAAG,MAAKC,KAAG,QAAOC,KAAG,OAAMC,IAAE,CAAC;;;AGAj/N,IAAAE,iBAAsH;;;ACAtH,SAASC,IAAEC,IAAE;AAAC,MAAIC,KAAE,EAAC,QAAO,MAAE;AAAE,SAAM,IAAIC,QAAI;AAAC,QAAG,CAACD,GAAE,OAAO,QAAOA,GAAE,SAAO,MAAGD,GAAE,GAAGE,GAAC;AAAA,EAAC;AAAC;;;ACAmE,SAASC,GAAEC,QAAKC,IAAE;AAAC,EAAAD,OAAGC,GAAE,SAAO,KAAGD,IAAE,UAAU,IAAI,GAAGC,EAAC;AAAC;AAAC,SAASC,GAAEF,QAAKC,IAAE;AAAC,EAAAD,OAAGC,GAAE,SAAO,KAAGD,IAAE,UAAU,OAAO,GAAGC,EAAC;AAAC;AAAC,SAASE,GAAEH,KAAEC,IAAE;AAAC,MAAIG,KAAEC,GAAE;AAAE,MAAG,CAACL,IAAE,QAAOI,GAAE;AAAQ,MAAG,EAAC,oBAAmBE,KAAE,iBAAgBC,IAAC,IAAE,iBAAiBP,GAAC,GAAE,CAACQ,KAAEC,EAAC,IAAE,CAACH,KAAEC,GAAC,EAAE,IAAI,CAAAG,QAAG;AAAC,QAAG,CAACC,KAAE,CAAC,IAAED,IAAE,MAAM,GAAG,EAAE,OAAO,OAAO,EAAE,IAAI,CAAAE,OAAGA,GAAE,SAAS,IAAI,IAAE,WAAWA,EAAC,IAAE,WAAWA,EAAC,IAAE,GAAG,EAAE,KAAK,CAACA,IAAEC,OAAIA,KAAED,EAAC;AAAE,WAAOD;AAAA,EAAC,CAAC,GAAEN,MAAEG,MAAEC;AAAE,MAAGJ,QAAI,GAAE;AAAC,IAAAD,GAAE,MAAM,CAAAO,OAAG;AAAC,MAAAA,GAAE,WAAW,MAAI;AAAC,QAAAV,GAAE,GAAEU,GAAE,QAAQ;AAAA,MAAC,GAAEN,GAAC,GAAEM,GAAE,iBAAiBX,KAAE,iBAAgB,CAAAY,OAAG;AAAC,QAAAA,GAAE,WAASA,GAAE,iBAAeD,GAAE,QAAQ;AAAA,MAAC,CAAC;AAAA,IAAC,CAAC;AAAE,QAAID,MAAEN,GAAE,iBAAiBJ,KAAE,iBAAgB,CAAAW,OAAG;AAAC,MAAAA,GAAE,WAASA,GAAE,kBAAgBV,GAAE,GAAES,IAAE;AAAA,IAAE,CAAC;AAAA,EAAC,MAAM,CAAAT,GAAE;AAAE,SAAOG,GAAE,IAAI,MAAIH,GAAE,CAAC,GAAEG,GAAE;AAAO;AAAC,SAASU,GAAEd,KAAEC,IAAEG,IAAEE,KAAE;AAAC,MAAIC,MAAEH,KAAE,UAAQ,SAAQI,MAAEH,GAAE,GAAEI,KAAEH,QAAI,SAAOI,IAAEJ,GAAC,IAAE,MAAI;AAAA,EAAC;AAAE,EAAAC,QAAI,YAAUP,IAAE,gBAAgB,QAAQ,GAAEA,IAAE,MAAM,UAAQ;AAAI,MAAIK,MAAE,EAAEE,KAAE,EAAC,OAAM,MAAIN,GAAE,OAAM,OAAM,MAAIA,GAAE,MAAK,CAAC,GAAES,MAAE,EAAEH,KAAE,EAAC,OAAM,MAAIN,GAAE,SAAQ,OAAM,MAAIA,GAAE,QAAO,CAAC,GAAEU,KAAE,EAAEJ,KAAE,EAAC,OAAM,MAAIN,GAAE,WAAU,OAAM,MAAIA,GAAE,UAAS,CAAC;AAAE,SAAOC,GAAEF,KAAE,GAAGC,GAAE,MAAK,GAAGA,GAAE,OAAM,GAAGA,GAAE,SAAQ,GAAGA,GAAE,WAAU,GAAGA,GAAE,OAAM,GAAGA,GAAE,WAAU,GAAGA,GAAE,SAAQ,GAAGA,GAAE,OAAO,GAAEF,GAAEC,KAAE,GAAGC,GAAE,MAAK,GAAGI,KAAE,GAAGM,EAAC,GAAEH,IAAE,UAAU,MAAI;AAAC,IAAAN,GAAEF,KAAE,GAAGC,GAAE,MAAK,GAAGI,KAAE,GAAGM,EAAC,GAAEZ,GAAEC,KAAE,GAAGC,GAAE,MAAK,GAAGI,KAAE,GAAGK,GAAC,GAAEP,GAAEH,KAAE,OAAKE,GAAEF,KAAE,GAAGC,GAAE,MAAK,GAAGI,GAAC,GAAEN,GAAEC,KAAE,GAAGC,GAAE,MAAK,GAAGA,GAAE,OAAO,GAAEQ,GAAE,EAAE;AAAA,EAAC,CAAC,GAAED,IAAE;AAAO;;;ACAx/B,SAASO,GAAE,EAAC,WAAUC,KAAE,WAAUC,KAAE,WAAUC,IAAE,SAAQC,KAAE,SAAQC,KAAE,QAAOC,IAAC,GAAE;AAAC,MAAIC,MAAEC,GAAE,GAAEC,MAAE,EAAE,GAAEC,KAAER,GAAEC,EAAC;AAAE,IAAE,MAAI;AAAC,IAAAF,QAAIS,GAAE,UAAQ;AAAA,EAAQ,GAAE,CAACT,GAAC,CAAC,GAAE,EAAE,MAAI;AAAC,QAAIU,KAAEC,GAAE;AAAE,IAAAH,IAAE,IAAIE,GAAE,OAAO;AAAE,QAAIE,KAAEX,IAAE;AAAQ,QAAGW,MAAGH,GAAE,YAAU,UAAQH,IAAE,QAAQ,QAAOI,GAAE,QAAQ,GAAEN,IAAE,QAAQK,GAAE,OAAO,GAAEC,GAAE,IAAIG,GAAED,IAAET,IAAE,SAAQM,GAAE,YAAU,SAAQ,MAAI;AAAC,MAAAC,GAAE,QAAQ,GAAEL,IAAE,QAAQI,GAAE,OAAO;AAAA,IAAC,CAAC,CAAC,GAAEC,GAAE;AAAA,EAAO,GAAE,CAACR,EAAC,CAAC;AAAC;;;AHA6S,SAASY,GAAEC,MAAE,IAAG;AAAC,SAAOA,IAAE,MAAM,KAAK,EAAE,OAAO,CAAAC,OAAGA,GAAE,SAAO,CAAC;AAAC;AAAC,IAAIC,UAAE,eAAAC,eAAE,IAAI;AAAED,IAAE,cAAY;AAAoB,IAAIE,OAAI,CAAAC,QAAIA,GAAE,UAAQ,WAAUA,GAAE,SAAO,UAASA,KAAID,OAAI,CAAC,CAAC;AAAE,SAASE,MAAI;AAAC,MAAIN,UAAE,eAAAO,YAAEL,GAAC;AAAE,MAAGF,QAAI,KAAK,OAAM,IAAI,MAAM,kGAAkG;AAAE,SAAOA;AAAC;AAAC,SAASQ,MAAI;AAAC,MAAIR,UAAE,eAAAO,YAAEE,EAAC;AAAE,MAAGT,QAAI,KAAK,OAAM,IAAI,MAAM,kGAAkG;AAAE,SAAOA;AAAC;AAAC,IAAIS,SAAE,eAAAN,eAAE,IAAI;AAAEM,GAAE,cAAY;AAAiB,SAASC,GAAEV,KAAE;AAAC,SAAM,cAAaA,MAAEU,GAAEV,IAAE,QAAQ,IAAEA,IAAE,QAAQ,OAAO,CAAC,EAAC,IAAGC,GAAC,MAAIA,GAAE,YAAU,IAAI,EAAE,OAAO,CAAC,EAAC,OAAMA,GAAC,MAAIA,OAAI,SAAS,EAAE,SAAO;AAAC;AAAC,SAASU,IAAGX,KAAEC,IAAE;AAAC,MAAII,KAAEO,GAAEZ,GAAC,GAAEY,UAAE,eAAAC,QAAE,CAAC,CAAC,GAAEC,KAAEC,GAAG,GAAEC,KAAE,EAAG,GAAEC,KAAEC,GAAE,CAACC,IAAEC,KAAE,EAAE,WAAS;AAAC,QAAIC,MAAET,IAAE,QAAQ,UAAU,CAAC,EAAC,IAAGM,IAAC,MAAIA,QAAIC,EAAC;AAAE,IAAAE,QAAI,OAAK,EAAED,IAAE,EAAC,CAAC,EAAE,OAAO,IAAG;AAAC,MAAAR,IAAE,QAAQ,OAAOS,KAAE,CAAC;AAAA,IAAC,GAAE,CAAC,EAAE,MAAM,IAAG;AAAC,MAAAT,IAAE,QAAQS,GAAC,EAAE,QAAM;AAAA,IAAQ,EAAC,CAAC,GAAEL,GAAE,UAAU,MAAI;AAAC,UAAIE;AAAE,OAACR,GAAEE,GAAC,KAAGE,GAAE,aAAWI,MAAEb,GAAE,YAAU,QAAMa,IAAE,KAAKb,EAAC;AAAA,IAAE,CAAC;AAAA,EAAE,CAAC,GAAEiB,KAAEJ,GAAE,CAAAC,OAAG;AAAC,QAAIC,KAAER,IAAE,QAAQ,KAAK,CAAC,EAAC,IAAGS,IAAC,MAAIA,QAAIF,EAAC;AAAE,WAAOC,KAAEA,GAAE,UAAQ,cAAYA,GAAE,QAAM,aAAWR,IAAE,QAAQ,KAAK,EAAC,IAAGO,IAAE,OAAM,UAAS,CAAC,GAAE,MAAIF,GAAEE,IAAE,EAAE,OAAO;AAAA,EAAC,CAAC,GAAEI,SAAE,eAAAV,QAAE,CAAC,CAAC,GAAEW,SAAE,eAAAX,QAAE,QAAQ,QAAQ,CAAC,GAAEY,UAAE,eAAAZ,QAAE,EAAC,OAAM,CAAC,GAAE,OAAM,CAAC,GAAE,MAAK,CAAC,EAAC,CAAC,GAAEa,KAAER,GAAE,CAACC,IAAEC,IAAEC,QAAI;AAAC,IAAAE,GAAE,QAAQ,OAAO,CAAC,GAAEtB,OAAIA,GAAE,OAAO,QAAQmB,EAAC,IAAEnB,GAAE,OAAO,QAAQmB,EAAC,EAAE,OAAO,CAAC,CAACF,GAAC,MAAIA,QAAIC,EAAC,IAAGlB,MAAG,QAAMA,GAAE,OAAO,QAAQmB,EAAC,EAAE,KAAK,CAACD,IAAE,IAAI,QAAQ,CAAAD,QAAG;AAAC,MAAAK,GAAE,QAAQ,KAAKL,GAAC;AAAA,IAAC,CAAC,CAAC,CAAC,GAAEjB,MAAG,QAAMA,GAAE,OAAO,QAAQmB,EAAC,EAAE,KAAK,CAACD,IAAE,IAAI,QAAQ,CAAAD,QAAG;AAAC,cAAQ,IAAIO,IAAE,QAAQL,EAAC,EAAE,IAAI,CAAC,CAACL,KAAEY,EAAC,MAAIA,EAAC,CAAC,EAAE,KAAK,MAAIT,IAAE,CAAC;AAAA,IAAC,CAAC,CAAC,CAAC,GAAEE,OAAI,UAAQI,GAAE,UAAQA,GAAE,QAAQ,KAAK,MAAIvB,MAAG,OAAK,SAAOA,GAAE,KAAK,OAAO,EAAE,KAAK,MAAIoB,IAAED,EAAC,CAAC,IAAEC,IAAED,EAAC;AAAA,EAAC,CAAC,GAAEQ,MAAEV,GAAE,CAACC,IAAEC,IAAEC,QAAI;AAAC,YAAQ,IAAII,IAAE,QAAQL,EAAC,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,CAACF,KAAEH,GAAC,MAAIA,GAAC,CAAC,EAAE,KAAK,MAAI;AAAC,UAAIG;AAAE,OAACA,MAAEK,GAAE,QAAQ,MAAM,MAAI,QAAML,IAAE;AAAA,IAAC,CAAC,EAAE,KAAK,MAAIG,IAAED,EAAC,CAAC;AAAA,EAAC,CAAC;AAAE,aAAO,eAAAS,SAAG,OAAK,EAAC,UAASjB,KAAE,UAASU,IAAE,YAAWL,IAAE,SAAQS,IAAE,QAAOE,KAAE,MAAKJ,IAAE,QAAOC,IAAC,IAAG,CAACH,IAAEL,IAAEL,KAAEc,IAAEE,KAAEH,KAAED,EAAC,CAAC;AAAC;AAAC,SAASM,MAAI;AAAC;AAAC,IAAIC,MAAG,CAAC,eAAc,cAAa,eAAc,YAAY;AAAE,SAASC,IAAGhC,KAAE;AAAC,MAAIK;AAAE,MAAIJ,KAAE,CAAC;AAAE,WAAQW,OAAKmB,IAAG,CAAA9B,GAAEW,GAAC,KAAGP,KAAEL,IAAEY,GAAC,MAAI,OAAKP,KAAEyB;AAAG,SAAO7B;AAAC;AAAC,SAASgC,IAAGjC,KAAE;AAAC,MAAIC,SAAE,eAAAY,QAAEmB,IAAGhC,GAAC,CAAC;AAAE,aAAO,eAAAkC,WAAE,MAAI;AAAC,IAAAjC,GAAE,UAAQ+B,IAAGhC,GAAC;AAAA,EAAC,GAAE,CAACA,GAAC,CAAC,GAAEC;AAAC;AAAC,IAAIkC,MAAG;AAAP,IAAaC,MAAGC,GAAG;AAAe,SAASC,IAAGtC,KAAEC,IAAE;AAAC,MAAIsC,IAAEC;AAAE,MAAG,EAAC,aAAYnC,IAAE,YAAWO,KAAE,aAAYE,IAAE,YAAWE,IAAE,OAAMC,IAAE,WAAUK,IAAE,SAAQC,IAAE,SAAQC,IAAE,OAAMC,KAAE,WAAUC,IAAE,SAAQE,KAAE,GAAGT,GAAC,IAAEnB,KAAEoB,SAAE,eAAAP,QAAE,IAAI,GAAEQ,MAAEoB,GAAGrB,IAAEnB,EAAC,GAAEiB,OAAGqB,KAAEpB,GAAE,YAAU,QAAMoB,KAAE,EAAE,UAAQ,EAAE,QAAO,EAAC,MAAKxB,KAAE,QAAOY,IAAE,SAAQe,GAAC,IAAEpC,IAAG,GAAE,CAACqC,KAAEC,EAAC,QAAE,eAAAC,UAAE9B,MAAE,YAAU,QAAQ,GAAE+B,KAAEtC,IAAG,GAAE,EAAC,UAASuC,IAAE,YAAWV,GAAC,IAAES;AAAE,qBAAAZ,WAAE,MAAIa,GAAE3B,EAAC,GAAE,CAAC2B,IAAE3B,EAAC,CAAC,OAAE,eAAAc,WAAE,MAAI;AAAC,QAAGhB,QAAI,EAAE,UAAQE,GAAE,SAAQ;AAAC,UAAGL,OAAG4B,QAAI,WAAU;AAAC,QAAAC,GAAE,SAAS;AAAE;AAAA,MAAM;AAAC,aAAO,EAAED,KAAE,EAAC,CAAC,QAAQ,GAAE,MAAIN,GAAEjB,EAAC,GAAE,CAAC,SAAS,GAAE,MAAI2B,GAAE3B,EAAC,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC,GAAE,CAACuB,KAAEvB,IAAE2B,IAAEV,IAAEtB,KAAEG,GAAC,CAAC;AAAE,MAAI8B,KAAEpC,GAAE,EAAC,MAAKb,GAAEoB,GAAE,SAAS,GAAE,OAAMpB,GAAEkB,EAAC,GAAE,WAAUlB,GAAEuB,EAAC,GAAE,SAAQvB,GAAEwB,EAAC,GAAE,SAAQxB,GAAEyB,EAAC,GAAE,OAAMzB,GAAE0B,GAAC,GAAE,WAAU1B,GAAE2B,EAAC,GAAE,SAAQ3B,GAAE6B,GAAC,EAAC,CAAC,GAAEqB,KAAEhB,IAAG,EAAC,aAAY5B,IAAE,YAAWO,KAAE,aAAYE,IAAE,YAAWE,GAAC,CAAC,GAAEkC,KAAEP,GAAG;AAAE,qBAAAT,WAAE,MAAI;AAAC,QAAGgB,MAAGP,QAAI,aAAWvB,GAAE,YAAU,KAAK,OAAM,IAAI,MAAM,iEAAiE;AAAA,EAAC,GAAE,CAACA,IAAEuB,KAAEO,EAAC,CAAC;AAAE,MAAIC,MAAGT,MAAG,CAACf,IAAEyB,KAAEzB,MAAGZ,OAAG2B,IAAEW,MAAI,uBAAI,CAACH,MAAGC,MAAG,SAAOpC,MAAE,UAAQ,SAAS,GAAEuC,KAAEzC,IAAG,CAAC,GAAE0C,MAAGrC,GAAE,CAAAsC,OAAG,EAAEA,IAAE,EAAC,OAAM,MAAI;AAAC,IAAAF,GAAE,QAAQ1B,GAAE,OAAO,GAAEqB,GAAE,QAAQ,YAAY;AAAA,EAAC,GAAE,OAAM,MAAI;AAAC,IAAAK,GAAE,QAAQ1B,GAAE,OAAO,GAAEqB,GAAE,QAAQ,YAAY;AAAA,EAAC,GAAE,MAAK,MAAI;AAAA,EAAC,EAAC,CAAC,CAAC,GAAEQ,MAAGvC,GAAE,CAAAsC,OAAG,EAAEA,IAAE,EAAC,OAAM,MAAI;AAAC,IAAAF,GAAE,WAAW1B,GAAE,OAAO,GAAEqB,GAAE,QAAQ,WAAW;AAAA,EAAC,GAAE,OAAM,MAAI;AAAC,IAAAK,GAAE,WAAW1B,GAAE,OAAO,GAAEqB,GAAE,QAAQ,WAAW;AAAA,EAAC,GAAE,MAAK,MAAI;AAAA,EAAC,EAAC,CAAC,CAAC,GAAES,KAAE/C,IAAG,MAAI;AAAC,IAAAiC,GAAE,QAAQ,GAAEP,GAAEjB,EAAC;AAAA,EAAC,GAAE0B,EAAC,GAAEa,SAAE,eAAA9C,QAAE,KAAE;AAAE,EAAAG,GAAG,EAAC,WAAUoC,IAAE,WAAUhC,IAAE,SAAQ4B,IAAE,WAAUK,KAAG,SAAQzC,GAAE,CAAA4C,OAAG;AAAC,IAAAG,GAAE,UAAQ,MAAGD,GAAE,QAAQtC,IAAEoC,IAAED,GAAE;AAAA,EAAC,CAAC,GAAE,QAAO3C,GAAE,CAAA4C,OAAG;AAAC,IAAAG,GAAE,UAAQ,OAAGD,GAAE,OAAOtC,IAAEoC,IAAEC,GAAE,GAAED,OAAI,WAAS,CAAC9C,GAAEgD,EAAC,MAAId,GAAE,QAAQ,GAAEP,GAAEjB,EAAC;AAAA,EAAE,CAAC,EAAC,CAAC;AAAE,MAAIwC,KAAEzC,IAAE0C,MAAG,EAAC,KAAIxC,IAAC;AAAE,SAAO+B,KAAEQ,KAAE,EAAC,GAAGA,IAAE,WAAU5D,GAAGmB,GAAE,WAAU,GAAG6B,GAAE,QAAQ,OAAM,GAAGA,GAAE,QAAQ,SAAS,EAAC,IAAEW,GAAE,YAAUC,GAAE,YAAU5D,GAAGmB,GAAE,YAAWqB,KAAEpB,GAAE,YAAU,OAAK,SAAOoB,GAAE,SAAS,GAAEoB,GAAE,cAAY,MAAI,OAAOA,GAAE,YAAW,eAAAE,QAAE,cAAcrD,GAAE,UAAS,EAAC,OAAMiD,GAAC,GAAE,eAAAI,QAAE,cAAclD,IAAG,EAAC,OAAM,EAAE+B,KAAE,EAAC,CAAC,SAAS,GAAEf,GAAE,MAAK,CAAC,QAAQ,GAAEA,GAAE,OAAM,CAAC,IAAE0B,GAAE,MAAK,GAAE,EAAG,EAAC,UAASO,KAAG,YAAWD,IAAE,YAAWzB,KAAG,UAASC,KAAG,SAAQO,QAAI,WAAU,MAAK,mBAAkB,CAAC,CAAC,CAAC;AAAC;AAAC,SAASoB,IAAG/D,KAAEC,IAAE;AAAC,MAAG,EAAC,MAAKI,IAAE,QAAOO,MAAE,OAAG,SAAQE,KAAE,MAAG,GAAGE,GAAC,IAAEhB,KAAEiB,SAAE,eAAAJ,QAAE,IAAI,GAAES,KAAEmB,GAAGxB,IAAEhB,EAAC;AAAE,EAAA0C,GAAG;AAAE,MAAIpB,KAAEE,GAAG;AAAE,MAAGpB,OAAI,UAAQkB,OAAI,SAAOlB,MAAGkB,KAAEK,GAAE,UAAQA,GAAE,OAAM,CAAC,CAAC,MAAG,KAAE,EAAE,SAASvB,EAAC,EAAE,OAAM,IAAI,MAAM,0EAA0E;AAAE,MAAG,CAACmB,IAAEC,GAAC,QAAE,eAAAoB,UAAExC,KAAE,YAAU,QAAQ,GAAEqB,KAAEf,IAAG,MAAI;AAAC,IAAAc,IAAE,QAAQ;AAAA,EAAC,CAAC,GAAE,CAACG,KAAET,EAAC,QAAE,eAAA0B,UAAE,IAAE,GAAEzB,SAAE,eAAAP,QAAE,CAACR,EAAC,CAAC;AAAE,IAAG,MAAI;AAAC,IAAAuB,QAAI,SAAIR,GAAE,QAAQA,GAAE,QAAQ,SAAO,CAAC,MAAIf,OAAIe,GAAE,QAAQ,KAAKf,EAAC,GAAEc,GAAE,KAAE;AAAA,EAAE,GAAE,CAACC,IAAEf,EAAC,CAAC;AAAE,MAAIgB,UAAE,eAAAQ,SAAG,OAAK,EAAC,MAAKxB,IAAE,QAAOO,KAAE,SAAQgB,IAAC,IAAG,CAACvB,IAAEO,KAAEgB,GAAC,CAAC;AAAE,qBAAAM,WAAE,MAAI;AAAC,QAAG7B,GAAE,CAAAoB,IAAE,SAAS;AAAA,aAAU,CAACf,GAAEgB,EAAC,EAAE,CAAAD,IAAE,QAAQ;AAAA,SAAM;AAAC,UAAIiB,KAAEzB,GAAE;AAAQ,UAAG,CAACyB,GAAE;AAAO,UAAIC,MAAED,GAAE,sBAAsB;AAAE,MAAAC,IAAE,MAAI,KAAGA,IAAE,MAAI,KAAGA,IAAE,UAAQ,KAAGA,IAAE,WAAS,KAAGlB,IAAE,QAAQ;AAAA,IAAC;AAAA,EAAC,GAAE,CAACpB,IAAEqB,EAAC,CAAC;AAAE,MAAIR,MAAE,EAAC,SAAQJ,GAAC,GAAEC,MAAEG,GAAE,MAAI;AAAC,QAAIwB;AAAE,IAAAd,OAAGT,GAAE,KAAE,IAAGuB,KAAE1C,IAAE,gBAAc,QAAM0C,GAAE,KAAK1C,GAAC;AAAA,EAAC,CAAC,GAAE2B,KAAET,GAAE,MAAI;AAAC,QAAIwB;AAAE,IAAAd,OAAGT,GAAE,KAAE,IAAGuB,KAAE1C,IAAE,gBAAc,QAAM0C,GAAE,KAAK1C,GAAC;AAAA,EAAC,CAAC;AAAE,SAAO,eAAA8D,QAAE,cAAcrD,GAAE,UAAS,EAAC,OAAMiB,GAAC,GAAE,eAAAoC,QAAE,cAAc5D,IAAE,UAAS,EAAC,OAAMmB,IAAC,GAAE,EAAG,EAAC,UAAS,EAAC,GAAGH,KAAE,IAAG,eAAA8C,UAAE,UAAS,eAAAF,QAAE,cAAcG,KAAG,EAAC,KAAI3C,IAAE,GAAGJ,KAAE,GAAGF,IAAE,aAAYD,KAAE,aAAYY,GAAC,CAAC,EAAC,GAAE,YAAW,CAAC,GAAE,YAAW,eAAAqC,UAAE,UAAS5B,KAAG,SAAQZ,OAAI,WAAU,MAAK,aAAY,CAAC,CAAC,CAAC;AAAC;AAAC,SAAS0C,IAAGlE,KAAEC,IAAE;AAAC,MAAII,SAAE,eAAAE,YAAEL,GAAC,MAAI,MAAKU,MAAEa,GAAG,MAAI;AAAK,SAAO,eAAAqC,QAAE,cAAc,eAAAA,QAAE,UAAS,MAAK,CAACzD,MAAGO,MAAE,eAAAkD,QAAE,cAAcK,IAAE,EAAC,KAAIlE,IAAE,GAAGD,IAAC,CAAC,IAAE,eAAA8D,QAAE,cAAcG,KAAG,EAAC,KAAIhE,IAAE,GAAGD,IAAC,CAAC,CAAC;AAAC;AAAC,IAAImE,KAAE,EAAEJ,GAAE;AAAV,IAAYE,MAAG,EAAE3B,GAAE;AAAnB,IAAqB8B,MAAG,EAAEF,GAAE;AAA5B,IAA8BG,MAAG,OAAO,OAAOF,IAAE,EAAC,OAAMC,KAAG,MAAKD,GAAC,CAAC;", "names": ["a", "b", "i", "opts", "d", "m", "a", "b", "i", "k", "instance", "import_react", "import_react", "t", "e", "n", "e", "f", "t", "c", "import_react", "s", "e", "r", "t", "i", "e", "o", "u", "t", "s", "r", "import_react", "import_react", "o", "t", "e", "s", "a", "r", "l", "r", "c", "i", "s", "f", "e", "t", "o", "u", "d", "n", "import_react", "t", "e", "o", "o", "n", "r", "e", "t", "s", "a", "e", "o", "s", "import_react", "t", "s", "r", "t", "o", "l", "e", "n", "o", "t", "n", "l", "e", "u", "import_react", "r", "n", "a", "e", "t", "o", "r", "c", "e", "n", "o", "t", "f", "r", "T", "l", "I", "i", "s", "E", "x", "p", "d", "a", "u", "t", "i", "import_react", "d", "e", "r", "n", "o", "s", "m", "t", "u", "import_react", "s", "e", "r", "n", "o", "d", "t", "i", "y", "s", "m", "a", "i", "f", "d", "c", "e", "r", "t", "E", "u", "n", "T", "o", "import_react", "n", "e", "t", "o", "import_react", "i", "t", "n", "e", "T", "u", "o", "import_react", "u", "T", "t", "n", "y", "i", "l", "c", "o", "e", "import_react", "t", "e", "u", "o", "r", "n", "import_react", "F", "e", "t", "r", "c", "o", "E", "l", "m", "n", "f", "p", "d", "i", "u", "import_react", "m", "u", "t", "e", "f", "r", "o", "s", "n", "a", "l", "import_react", "t", "r", "n", "O", "n", "e", "r", "t", "o", "a", "f", "l", "s", "m", "y", "u", "d", "F", "i", "c", "T", "p", "g", "P", "N", "E", "I", "S", "j", "h", "p", "s", "e", "l", "d", "o", "n", "t", "r", "u", "import_react", "n", "l", "d", "e", "u", "p", "s", "o", "r", "t", "t", "n", "e", "t", "e", "n", "r", "r", "n", "e", "l", "t", "i", "u", "l", "c", "i", "f", "n", "t", "r", "s", "e", "i", "s", "t", "r", "n", "o", "f", "p", "o", "r", "o", "a", "e", "t", "r", "i", "p", "c", "I", "u", "l", "T", "f", "v", "S", "g", "ie", "ue", "j", "U", "Fe", "w", "me", "fe", "R", "s", "V", "_", "E", "_e", "Pe", "k", "B", "J", "K", "z", "te", "X", "x", "d", "b", "P", "O", "L", "Ie", "n", "oe", "G", "Y", "y", "le", "F", "A", "ae", "h", "C", "D", "N", "ye", "Re", "Ae", "ne", "Se", "Ve", "m", "import_react", "import_react", "import_react", "E", "n", "e", "a", "t", "i", "s", "d", "r", "o", "import_react", "f", "e", "r", "import_react", "c", "t", "r", "o", "e", "n", "u", "import_react", "s", "r", "n", "e", "t", "o", "P", "t", "n", "e", "_", "r", "d", "o", "y", "l", "c", "s", "i", "u", "R", "a", "m", "B", "h", "H", "j", "T", "S", "E", "D", "de", "f", "import_react", "import_react_dom", "import_react", "e", "r", "a", "c", "l", "o", "t", "F", "p", "n", "a", "l", "s", "_", "e", "o", "E", "t", "r", "d", "U", "m", "N", "R", "y", "T", "u", "i", "f", "v", "c", "C", "S", "P", "j", "ee", "g", "D", "I", "e", "l", "i", "e", "t", "d", "u", "h", "f", "p", "y", "e", "t", "c", "a", "n", "o", "u", "f", "r", "h", "p", "d", "t", "r", "e", "n", "r", "s", "c", "t", "y", "a", "e", "n", "S", "t", "a", "a", "o", "r", "t", "n", "e", "s", "i", "c", "c", "o", "e", "l", "n", "t", "r", "d", "t", "r", "l", "c", "o", "a", "n", "s", "e", "f", "i", "l", "e", "o", "m", "e", "n", "t", "a", "o", "c", "d", "l", "r", "p", "e", "r", "n", "f", "S", "a", "o", "i", "u", "t", "r", "l", "o", "e", "a", "d", "i", "n", "f", "import_react", "N", "o", "r", "u", "f", "t", "M", "l", "n", "c", "i", "s", "a", "e", "L", "d", "m", "y", "import_react", "a", "c", "s", "e", "x", "m", "b", "i", "r", "n", "u", "l", "o", "t", "d", "import_react", "d", "m", "f", "r", "D", "t", "w", "T", "l", "e", "i", "o", "s", "p", "c", "n", "u", "I", "S", "a", "y", "h", "r", "e", "o", "I", "Pe", "b", "V", "p", "i", "n", "O", "l", "s", "a", "T", "m", "M", "f", "Ae", "U", "q", "E", "u", "d", "D", "ee", "y", "g", "$", "h", "te", "Ee", "ye", "P", "Y", "t", "S", "x", "j", "oe", "re", "ne", "w", "L", "le", "N", "ae", "J", "ie", "se", "K", "c", "pe", "de", "ue", "fe", "ge", "H", "F", "Te", "ce", "De", "X", "me", "$e", "Ye", "Ve", "qe", "ze", "Qe", "Ze", "et", "tt", "import_react", "import_react", "t", "a", "r", "i", "Q", "o", "V", "t", "Y", "e", "n", "M", "I", "_", "x", "v", "K", "F", "Z", "ee", "te", "H", "i", "f", "R", "l", "y", "T", "u", "d", "s", "G", "c", "a", "D", "p", "r", "P", "S", "C", "E", "h", "m", "O", "ue", "ie", "Ae", "import_react", "import_react", "a", "o", "e", "r", "i", "n", "t", "u", "f", "l", "g", "s", "c", "t", "l", "r", "o", "e", "u", "n", "g", "Be", "n", "He", "Ge", "Ne", "i", "z", "e", "a", "r", "I", "t", "l", "je", "f", "p", "b", "u", "Z", "k", "ee", "q", "w", "Ve", "<PERSON>", "ye", "Qe", "s", "c", "R", "m", "P", "S", "y", "g", "x", "T", "o", "Le", "xe", "L", "h", "U", "B", "O", "A", "ge", "d", "E", "H", "ie", "X", "re", "ae", "le", "se", "pe", "ue", "C", "de", "ce", "fe", "Te", "G", "be", "te", "N", "Y", "We", "Xe", "$e", "ze", "Je", "qe", "Ye", "Ze", "et", "tt", "ot", "nt", "it", "rt", "import_react", "me", "r", "de", "a", "w", "e", "u", "s", "I", "t", "i", "f", "o", "l", "m", "U", "X", "C", "$", "ye", "Ie", "H", "Me", "z", "N", "y", "g", "R", "p", "T", "A", "v", "G", "d", "ge", "c", "M", "Ae", "be", "O", "Ee", "b", "n", "q", "F", "E", "x", "S", "xe", "K", "P", "B", "Pe", "qe", "import_react", "he", "u", "He", "e", "Ge", "t", "o", "ue", "Q", "oe", "Z", "ie", "fe", "Pe", "Ee", "re", "Ne", "we", "Ue", "B", "M", "x", "J", "n", "y", "T", "l", "c", "ge", "de", "f", "s", "I", "a", "v", "A", "P", "p", "E", "h", "S", "q", "U", "z", "be", "F", "D", "_", "O", "L", "$", "ee", "i", "b", "N", "d", "r", "m", "g", "C", "We", "<PERSON>", "ce", "je", "Ve", "$e", "Je", "Xe", "Ye", "qe", "ze", "Qe", "Ze", "et", "tt", "ot", "import_react", "import_react", "d", "m", "u", "a", "L", "t", "F", "b", "f", "e", "s", "o", "r", "l", "n", "p", "c", "h", "i", "y", "v", "B", "import_react", "c", "a", "l", "r", "f", "t", "o", "n", "e", "u", "m", "s", "g", "Ge", "t", "Ce", "o", "r", "I", "p", "T", "B", "J", "oe", "V", "$", "ne", "ke", "Le", "he", "v", "M", "m", "H", "G", "e", "i", "P", "N", "y", "n", "A", "L", "ue", "a", "h", "R", "F", "C", "U", "w", "k", "j", "l", "s", "b", "D", "x", "d", "f", "u", "g", "K", "c", "ie", "ae", "pe", "le", "se", "O", "xe", "Fe", "we", "Ie", "Se", "it", "import_react", "S", "A", "ee", "H", "te", "r", "u", "n", "p", "I", "c", "T", "F", "o", "b", "w", "a", "P", "d", "y", "l", "m", "ne", "re", "E", "t", "f", "U", "C", "i", "s", "L", "e", "x", "v", "G", "R", "k", "M", "_", "oe", "ie", "_e", "B", "import_react", "import_react", "b", "n", "r", "o", "c", "u", "f", "s", "a", "e", "i", "t", "r", "s", "a", "n", "t", "c", "e", "l", "o", "i", "u", "C", "d", "f", "ue", "t", "Te", "l", "de", "a", "ce", "e", "n", "i", "I", "c", "o", "p", "T", "m", "b", "X", "V", "F", "Q", "$", "q", "fe", "be", "ne", "me", "R", "s", "y", "u", "f", "re", "P", "g", "E", "L", "A", "S", "k", "h", "d", "M", "O", "C", "r", "Pe", "ye", "xe", "ge", "J", "K", "z", "te", "Ee", "Ae", "Re", "Le", "_e", "Se", "Ie", "De", "Fe", "he", "$e", "import_react", "l", "r", "e", "t", "g", "t", "e", "v", "b", "n", "o", "m", "a", "u", "p", "l", "r", "i", "T", "M", "D", "t", "s", "n", "u", "a", "c", "l", "f", "d", "e", "r", "o", "i", "M", "S", "t", "n", "I", "Z", "Se", "r", "ye", "J", "xe", "M", "U", "se", "s", "c", "R", "f", "D", "p", "o", "i", "e", "a", "x", "h", "v", "u", "g", "N", "d", "ee", "Ne", "Pe", "ae", "Re", "F", "De", "le", "O", "He", "Q", "Y", "y", "T", "l", "j", "X", "z", "L", "k", "V", "G", "Te", "K", "de", "H", "fe", "C", "me", "w", "B", "P", "ce", "m", "Fe", "$", "ue", "_e", "q", "Le", "qe"]}